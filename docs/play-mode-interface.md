# Play Mode Interface Documentation

## Overview

The Play Mode Interface is a sophisticated three-tab system that provides users with an intuitive music playback experience. It features dynamic contextual tabs that adapt based on user interactions, similar to Spotify's interface design.

## Architecture

### Tab System Structure

The interface consists of a smart tab system that dynamically adjusts between 2-tab and 3-tab modes:

- **Default State**: 2 tabs (Play | Queue)
- **Contextual State**: 3 tabs (Play | Queue | [Dynamic])

### Core Components

1. **Play Tab**: Music visualizations and playback controls
2. **Queue Tab**: Spotify-style queue management
3. **Contextual Tab**: Dynamic tab for Albums, Playlists, or Liked Songs

## Features

### 1. Play Tab - Music Visualizations

The Play tab provides immersive music visualization experiences:

#### Visualization Types
- **Album View**: Large album artwork with track information
- **Waveform Analyzer**: Real-time audio waveform visualization
- **Spectrum Analyzer**: Circular frequency spectrum display

#### Features
- Fullscreen mode support
- Responsive design for different screen sizes
- Real-time audio analysis integration
- Smooth transitions between visualization types

### 2. Queue Tab - Spotify-Style Queue Management

A dedicated queue interface that mirrors Spotify's design:

#### Now Playing Section
- Current track display with larger cover art
- Track information (title, artist, album)
- Visual playing indicator (animated bars)
- Like button integration

#### Next from Section
- Clean header showing source (Album/Playlist name)
- Queue count display
- "Clear queue" functionality
- List of upcoming tracks (excludes current track)

#### Track List Features
- Track covers, titles, artists, and durations
- Hover effects with play buttons
- Like buttons and overflow menus
- Click to play functionality

### 3. Dynamic Contextual Tab

The third tab appears contextually based on user sidebar interactions:

#### Album Tab
- **Icon**: 💿 Disc icon
- **Label**: "Album"
- **Content**: Full album interface with header and track listing

#### Playlist Tab
- **Icon**: 📋 List icon
- **Label**: "Playlist"
- **Content**: Complete playlist view with metadata

#### Liked Songs Tab
- **Icon**: ❤️ Heart icon
- **Label**: "Liked"
- **Content**: User's liked songs collection

## User Experience Flow

```mermaid
graph TD
    A[User in Play Mode] --> B{Default State}
    B --> C[Play Tab]
    B --> D[Queue Tab]
    
    A --> E{User Clicks Sidebar Item}
    E --> F[Album from Sidebar]
    E --> G[Playlist from Sidebar]
    E --> H[Liked Songs from Sidebar]
    
    F --> I[3-Tab Mode: Play | Queue | Album]
    G --> J[3-Tab Mode: Play | Queue | Playlist]
    H --> K[3-Tab Mode: Play | Queue | Liked]
    
    I --> L[Album Tab Shows:<br/>• Album Header<br/>• Track List<br/>• Album Art<br/>• Play Controls]
    
    J --> M[Playlist Tab Shows:<br/>• Playlist Header<br/>• Track List<br/>• Playlist Info<br/>• Play Controls]
    
    K --> N[Liked Tab Shows:<br/>• Liked Songs Header<br/>• Liked Track List<br/>• Heart Icon<br/>• Play Controls]
    
    L --> O[User Can Clear Context]
    M --> O
    N --> O
    O --> B
```

## Technical Implementation

### State Management

The system uses Zustand for global state management:

```typescript
// usePlayModeContext.ts
interface PlayModeContextStore {
  contextualTab: ContextualTab;
  activeTab: 'play' | 'queue' | 'playlist';
  activateContextualTab: (type, label, data?) => void;
  clearContextualTab: () => void;
  setActiveTab: (tab) => void;
}
```

### Integration with Sidebar Components

Sidebar components can easily integrate using helper functions:

```typescript
import { playModeContextActions } from '../../hooks/usePlayModeContext';

// When user clicks an album
const handleAlbumClick = (album) => {
  playModeContextActions.showAlbum(album.title, album);
};

// When user clicks a playlist  
const handlePlaylistClick = (playlist) => {
  playModeContextActions.showPlaylist(playlist.title, playlist);
};

// When user clicks liked songs
const handleLikedSongsClick = () => {
  playModeContextActions.showLikedSongs();
};
```

### Dynamic Tab Navigation

The tab navigation automatically adjusts its layout:

#### 2-Tab Mode (Default)
- Background pill covers 50% width
- Larger padding for better touch targets
- Smooth transitions

#### 3-Tab Mode (Contextual)
- Background pill covers 33.33% width
- Adjusted padding to fit three tabs
- Contextual icons and labels

### Animation System

```css
/* Tab sliding animation */
.tab-background {
  transition: all 300ms ease-out;
}

/* 2-tab positioning */
.two-tab-left { left: 1; right: 1/2; }
.two-tab-right { left: 1/2; right: 1; }

/* 3-tab positioning */
.three-tab-left { left: 1; right: 2/3; }
.three-tab-center { left: 1/3; right: 1/3; }
.three-tab-right { left: 2/3; right: 1; }
```

## Benefits

### User Experience
- **Intuitive Navigation**: Users always know what they're viewing
- **Clean Interface**: No unnecessary tabs cluttering the UI
- **Context Awareness**: Tab labels match user selections
- **Smooth Transitions**: Beautiful animations between states

### Technical Benefits
- **Scalable Architecture**: Easy to add new contextual tab types
- **Performance Optimized**: Efficient state management
- **Responsive Design**: Works across all device sizes
- **Accessibility**: Proper keyboard navigation and screen reader support

## Future Enhancements

### Planned Features
1. **Drag & Drop Queue Reordering**: Allow users to rearrange queue items
2. **Queue History**: Show recently played tracks
3. **Shuffle Queue**: Button to randomize upcoming tracks
4. **Advanced Visualizations**: Additional visualization types
5. **Keyboard Shortcuts**: Hotkeys for tab switching and playback control

### Integration Opportunities
- **Voice Commands**: "Show my liked songs", "Open queue"
- **Gesture Controls**: Swipe between tabs on touch devices
- **Smart Recommendations**: Contextual suggestions in each tab
- **Social Features**: Share queues and playlists

## Conclusion

The Play Mode Interface represents a significant advancement in music application UX design. By combining Spotify's proven queue management patterns with innovative contextual navigation, it provides users with a powerful yet intuitive music playback experience.

The dynamic tab system ensures that users always have access to the right tools at the right time, while maintaining a clean and uncluttered interface. The technical implementation is robust, scalable, and ready for future enhancements.
