# Apple Sign-In Setup Guide

## 📋 Overview
This guide covers the setup requirements for Apple Sign-In integration with Firebase Authentication in the Vibes platform.

## 🔧 Firebase Console Configuration

### Current Status
Based on the Firebase console screenshot, Apple Sign-In is **enabled** but requires additional configuration.

### Required Configuration Steps

#### 1. Services ID (Required for Apple)
- **Status**: ⚠️ Needs to be configured
- **Location**: Firebase Console > Authentication > Sign-in method > Apple
- **Required Field**: Services ID (not required for Apple)

#### 2. OAuth Code Flow Configuration (Optional)
- **Status**: ⚠️ Needs authorization callback URL
- **Required URL**: `https://probe-vibes.firebaseapp.com/__/auth/handler`
- **Purpose**: To complete set up, add this authorization callback URL to your app configuration in the Apple Developer Console

## 🍎 Apple Developer Console Setup

### Prerequisites
1. **Apple Developer Account** - Active paid developer account required
2. **App ID** - Registered app identifier in Apple Developer Console
3. **Services ID** - OAuth identifier for web authentication

### Step-by-Step Setup

#### 1. Create App ID
1. Go to [Apple Developer Console](https://developer.apple.com/account/)
2. Navigate to **Certificates, Identifiers & Profiles**
3. Select **Identifiers** > **App IDs**
4. Click **+** to create new App ID
5. Configure:
   - **Description**: Vibes Music Platform
   - **Bundle ID**: `com.vibes.musicplatform` (or your chosen identifier)
   - **Capabilities**: Enable **Sign In with Apple**

#### 2. Create Services ID
1. In Apple Developer Console, go to **Identifiers** > **Services IDs**
2. Click **+** to create new Services ID
3. Configure:
   - **Description**: Vibes Web Authentication
   - **Identifier**: `com.vibes.musicplatform.web` (must be different from App ID)
   - **Enable**: Sign In with Apple
4. Click **Configure** next to Sign In with Apple:
   - **Primary App ID**: Select the App ID created in step 1
   - **Domains and Subdomains**: Add your domain (e.g., `vibes.com`)
   - **Return URLs**: Add Firebase callback URL:
     ```
     https://probe-vibes.firebaseapp.com/__/auth/handler
     ```

#### 3. Create Private Key
1. Go to **Keys** section in Apple Developer Console
2. Click **+** to create new key
3. Configure:
   - **Key Name**: Vibes Apple Sign-In Key
   - **Enable**: Sign In with Apple
   - **Configure**: Select your Primary App ID
4. **Download the key file** (.p8 file) - **Important**: This can only be downloaded once
5. **Note the Key ID** - You'll need this for Firebase configuration

## 🔥 Firebase Configuration

### Update Firebase Console
1. Go to Firebase Console > Authentication > Sign-in method > Apple
2. Fill in the **Services ID** from Apple Developer Console
3. For OAuth code flow configuration:
   - **Apple Team ID**: Found in Apple Developer Console membership details
   - **Key ID**: From the private key created above
   - **Private Key**: Content of the .p8 file downloaded from Apple

### Environment Variables
Add these to your `.env` file:
```bash
# Apple Sign-In Configuration
VITE_APPLE_SERVICES_ID=com.vibes.musicplatform.web
VITE_APPLE_TEAM_ID=YOUR_TEAM_ID
VITE_APPLE_KEY_ID=YOUR_KEY_ID
```

## 🧪 Testing Apple Sign-In

### Development Testing
1. **Web Testing**: Apple Sign-In works in web browsers
2. **Localhost**: Apple Sign-In may not work on localhost - use HTTPS domain
3. **Production Domain**: Ensure your production domain is added to Apple configuration

### Test Scenarios
- [ ] Sign in with existing Apple ID
- [ ] Sign in with new Apple ID
- [ ] Handle user cancellation
- [ ] Handle network errors
- [ ] Test on different browsers (Safari, Chrome, Firefox)

## 🚨 Important Notes

### Security Considerations
1. **Private Key Security**: Never commit the .p8 file to version control
2. **Services ID**: Keep Services ID secure and don't expose in client code
3. **Domain Verification**: Only add verified domains to Apple configuration

### Common Issues
1. **Invalid Client**: Usually means Services ID is incorrect
2. **Invalid Redirect URI**: Check that Firebase callback URL is correctly added to Apple config
3. **Team ID Mismatch**: Ensure Team ID matches between Apple and Firebase

### Production Checklist
- [ ] Apple Developer Account active and paid
- [ ] App ID created and configured
- [ ] Services ID created with correct domains
- [ ] Private key generated and securely stored
- [ ] Firebase console fully configured
- [ ] Production domain verified with Apple
- [ ] Testing completed on production domain

## 📚 References
- [Firebase Apple Sign-In Documentation](https://firebase.google.com/docs/auth/web/apple)
- [Apple Sign-In Developer Documentation](https://developer.apple.com/sign-in-with-apple/)
- [Apple Developer Console](https://developer.apple.com/account/)

## 🔄 Next Steps
1. Complete Apple Developer Console setup
2. Configure Firebase with Apple credentials
3. Test Apple Sign-In functionality
4. Deploy to production domain for full testing
