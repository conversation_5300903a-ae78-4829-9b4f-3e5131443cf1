# Git Commit Message Template (CSAD Format)

This template provides a structured format for writing clear, informative commit messages that help team members understand the purpose and implementation of changes.

## Template

```
[CSAD] <Brief title describing the change>

Context:
- <What problem are you solving?>
- <Why is this change necessary?>
- <Any relevant background information>

Solution:
- <What you implemented to solve the problem>
- <Key components or features added/modified>
- <How this addresses the issues mentioned in Context>

Approach:
- <Technical approach taken>
- <Key files or components modified>
- <Design patterns or principles applied>

Details:
- <Specific implementation details>
- <Important functions or methods changed>
- <Edge cases handled>
- <Performance considerations>
- <Testing approach>
```

## How to Use This Template

1. Replace the placeholders (text inside `<>`) with your specific information
2. Keep each bullet point concise but informative
3. Include enough detail for reviewers to understand your changes
4. Remove any sections that aren't relevant to your particular commit
5. For complex changes, consider breaking into multiple smaller commits

## Example

```
[CSAD] Fix Visual & Media form persistence and Pricing Step tooltip error

Context:
- Users were losing uploaded thumbnails and gallery images when navigating between steps in the product creation flow
- The Pricing Step had a "Maximum update depth exceeded" error caused by the HelpTooltip component

Solution:
- Implemented immediate form saving when images are uploaded or removed
- Enhanced form initialization to properly handle image URLs
- Created a global TooltipProvider to prevent nested providers
- Memoized the HelpTooltip component with stable content references
- Added proper error handling and user feedback

Approach:
- Modified ProductMediaForm to persist thumbnails and gallery images
- Updated ProductCreationPage to preserve media data during navigation
- Created a dedicated TooltipProvider component for global tooltip context
- Fixed infinite loop in HelpTooltip by using React.memo and useRef

Details:
- Fixed handleThumbnailChange and handleGalleryChange to save form data immediately
- Enhanced clearThumbnail and removeGalleryImage to persist changes
- Improved prepareProductData to properly handle media persistence
- Created tooltip-provider.tsx for global tooltip context
- Updated HelpTooltip to prevent unnecessary re-renders
- Fixed useEffect dependencies in ProductPricingForm
```

## Benefits of CSAD Format

- **Context**: Helps reviewers understand why the change was necessary
- **Solution**: Clearly states what was implemented to address the problem
- **Approach**: Explains the technical strategy used
- **Details**: Provides specific implementation information for thorough understanding

This format ensures that commit messages serve as valuable documentation for the codebase's evolution.
