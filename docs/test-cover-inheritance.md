# Album Cover Inheritance Test Plan

## Issue Fixed
- **Problem**: Album cover photos were not being used as track cover photos when tracks didn't have their own covers
- **Problem**: Play mode was not displaying album covers when tracks had no individual covers
- **Problem**: `setCurrentAlbum()` was not being called consistently when playing albums

## Root Cause Analysis
1. **Missing Album Context**: Most album play functions didn't call `setCurrentAlbum()`, so Play mode didn't know which album was playing
2. **No Cover Inheritance**: Tracks didn't automatically inherit album covers when they don't have their own covers
3. **Inconsistent Display Logic**: Play mode tried to show `currentTrack.coverUrl` instead of falling back to `currentAlbum.coverUrl`

## Implementation Changes

### 1. Fixed Album Context Setting
**Files Modified:**
- `src/components/discover/AlbumCard.tsx` - Added `setCurrentAlbum(album)` call
- `src/components/molecules/AlbumLibraryView.tsx` - Added `setCurrentAlbum(album)` call  
- `src/components/pages/ProfilePage.tsx` - Added `setCurrentAlbum(album)` call in both cached and progressive loading paths

### 2. Updated Display Logic
**Files Modified:**
- `src/components/organisms/PlayModeCard.tsx` - Updated to show `currentTrack?.coverUrl || currentAlbum?.coverUrl`
- `src/components/layout/GlobalPlayer.tsx` - Updated to show `currentTrack?.coverUrl || currentAlbum?.coverUrl`

### 3. Implemented Cover Inheritance
**Files Modified:**
- `src/services/albumService.ts` - Added cover inheritance in `addTracksToAlbum()` method
- `src/components/upload/UploadModal.tsx` - Added cover inheritance during album creation

## Test Cases

### Test Case 1: Album with Cover, Tracks without Covers
1. Create an album with a cover image
2. Add tracks without individual covers
3. **Expected**: Tracks should inherit the album cover
4. **Expected**: Play mode should display the album cover when playing these tracks

### Test Case 2: Album with Cover, Mixed Track Covers
1. Create an album with a cover image
2. Add some tracks with individual covers, some without
3. **Expected**: Tracks without covers inherit album cover, tracks with covers keep their own
4. **Expected**: Play mode shows appropriate cover for each track

### Test Case 3: Album without Cover, Tracks without Covers
1. Create an album without a cover image
2. Add tracks without individual covers
3. **Expected**: No cover inheritance occurs
4. **Expected**: Play mode shows default gradient/icon

### Test Case 4: Play Mode Display
1. Play any album
2. **Expected**: `currentAlbum` is set in music store
3. **Expected**: Play mode "album" tab shows album cover
4. **Expected**: Play mode "playlist" tab shows album header with cover

### Test Case 5: Global Player Display
1. Play any track from an album
2. **Expected**: Global player shows track cover if available, otherwise album cover
3. **Expected**: No broken images or missing covers

## Validation Steps

### Manual Testing
1. **Sign in** to the application
2. **Upload an album** with a cover image and tracks without individual covers
3. **Play the album** from different locations (AlbumCard, ProfilePage, etc.)
4. **Verify** Play mode displays the album cover
5. **Verify** Global player displays the album cover
6. **Check** that `currentAlbum` is set in React DevTools

### Code Verification
1. **Check** that all album play functions call `setCurrentAlbum()`
2. **Verify** cover inheritance logic in upload and album service
3. **Confirm** display logic uses fallback pattern `currentTrack?.coverUrl || currentAlbum?.coverUrl`

## Success Criteria
- ✅ Album covers are inherited by tracks that don't have individual covers
- ✅ Play mode displays album covers when tracks have no individual covers  
- ✅ Global player displays album covers as fallback
- ✅ `currentAlbum` is consistently set when playing albums
- ✅ No broken images or missing cover displays
- ✅ Clean, maintainable code with no redundancy

## Edge Cases Handled
- Albums without covers
- Tracks with individual covers (should not be overridden)
- Mixed scenarios (some tracks with covers, some without)
- Progressive loading scenarios
- Cached vs uncached album playback
