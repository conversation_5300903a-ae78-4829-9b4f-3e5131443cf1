# Probe Frequently Used Prompts

This document contains commonly used prompts for efficient development workflow and documentation management.

## 📝 Work Summary Prompts

### 1. Complete Work Summary

```
Write an indepth full scope summary of the muliple work done so far within the context of this chat history
```

**Use Case:** Get a comprehensive overview of all work completed in the current session
**When to Use:**

- End of development session
- Before major commits
- Project status updates
- Handoff documentation

---

### 2. Post-Commit Work Summary

```
Write an indepth full scope summary of the muliple work done so far within the context of this chat history After the Last GIt Changes Commit
```

**Use Case:** Summarize work done since the last Git commit
**When to Use:**

- Preparing for next commit
- Tracking incremental progress
- Code review preparation
- Sprint progress trackings

---

## 🔄 Git Workflow Prompts

### 3. Git Commit Generation

```
Please Write an indepth Git Commit into the chat based on our Template @GIT_COMMIT_TEMPLATE.md using the multiple Work done summary above.
```

**Use Case:** Generate properly formatted Git commit messages following project standards
**When to Use:**

- After completing work summary
- Before committing changes
- Ensuring consistent commit message format
- Maintaining project commit history standards

**Prerequisites:**

- Must have work summary from prompts #1 or #2
- References PROBE-GIT-COMMIT-TEMPLATE.md for formatting

---

## 🚀 Prompt Usage Workflow

### Recommended Sequence:

1. **Start with Work Summary**

   ```
   Use Prompt #1 (full scope) or #2 (post-commit)
   ```

2. **Generate Git Commit**

   ```
   Use Prompt #3 with the summary from step 1
   ```

3. **Review and Commit**
   ```
   Review generated commit message
   Apply to actual Git commit
   ```

### Example Workflow:

```bash
# 1. Get work summary (use prompt #1 or #2)
# 2. Generate commit message (use prompt #3)
# 3. Apply to Git
git add .
git commit -m "[Generated commit message from prompt #3]"
git push
```

## 🎯 BREAKTHROUGH PROMPTING TECHNIQUES

### Root Cause Analysis Enforcement

```
Deep dive and find the root cause, don't over complicate it when there is best practice that will work and keep code clean
```

**Key Phrases That Drive Success:**

- "Deep dive and find the root cause"
- "don't over complicate it"
- "best practice that will work"
- "keep code clean"

**Use Case:** Forces AI to avoid surface-level fixes and find actual underlying issues

### Quality Code Enforcement

```
(AI Agent Note: . Ensure quality code clean, simple, readable code implementation . Keep existing code in mind at all times. No Hard coded or mock data as we working in production. Avoid complexity, inconsistency, redundancy, and duplication .Remove, delete bad codes and enforce best practices.)
```

**Critical Keywords:**

- "quality code clean, simple, readable"
- "No Hard coded or mock data"
- "working in production"
- "Avoid complexity, inconsistency, redundancy, and duplication"
- "Remove, delete bad codes and enforce best practices"

**Use Case:** Ensures production-ready code with no shortcuts or workarounds

### Investigation Blocking

```
Dont Just do anything We need to get to root cause
```

**Power Phrase:** "Dont Just do anything We need to get to root cause"

**Use Case:** Stops AI from implementing quick fixes and forces proper investigation

### Breakthrough Discovery Recognition

```
Great work but i got log you didnt see :[ERROR MESSAGE]
```

**Pattern:** Point out specific errors/logs that AI missed to redirect investigation

**Use Case:** Keeps AI focused on actual problems rather than assumed solutions

### Success Amplification

```
Great work, then why do we keep getting console log: [SPECIFIC ERROR]
```

**Pattern:** Acknowledge progress but immediately redirect to remaining issues

**Use Case:** Maintains momentum while ensuring complete problem resolution

## 📋 Additional Useful Prompts

### Documentation Updates

```
Update the documentation to reflect the changes made in this session
```

### Code Review Preparation

```
Prepare a code review summary highlighting the key changes and their impact
```

### Architecture Impact Analysis

```
Analyze how the changes made affect the overall platform architecture
```

### Testing Recommendations

```
Suggest testing strategies for the changes implemented in this session
```

## 🚀 MASTER BREAKTHROUGH PROMPT

### Single Comprehensive Root Cause Analysis Prompt

```
Deep dive and find the root cause, don't over complicate it when there is best practice that will work and keep code clean.

(AI Agent Note: Ensure quality code clean, simple, readable code implementation. Keep existing code in mind at all times. No Hard coded or mock data as we working in production. Avoid complexity, inconsistency, redundancy, and duplication. Remove, delete bad codes and enforce best practices.)

Don't just do anything - we need to get to root cause first. Present specific evidence of what you find, and validate success completely before accepting any solution.
```

**Use Case:** Single prompt that enforces all breakthrough principles
**When to Use:** Complex debugging, performance issues, mysterious errors, production problems

### The CORS Audio Discovery Pattern

**What Made This Work:**

1. **Persistent Root Cause Demand**

   ```
   "Deep dive and find the root cause, don't over complicate it"
   ```

2. **Quality Code Enforcement**

   ```
   "No Hard coded or mock data as we working in production"
   ```

3. **Investigation Blocking**

   ```
   "Dont Just do anything We need to get to root cause"
   ```

4. **Error Evidence Presentation**

   ```
   "Great work but i got log you didnt see: [SPECIFIC ERROR]"
   ```

5. **Success Validation**
   ```
   "Great work, then why do we keep getting console log: [ERROR]"
   ```

**Result:** Single 8-line fix solved multiple "unrelated" issues:

- ✅ Carousel loading delays eliminated
- ✅ CORS errors resolved
- ✅ Real waveform visualization enabled
- ✅ Performance optimized across the app

### Key Success Factors

**1. Demand Root Cause Analysis**

- Never accept surface-level fixes
- Force investigation of underlying issues
- Use phrases like "deep dive" and "root cause"

**2. Enforce Production Standards**

- No mock data or hardcoded values
- Clean, simple, readable code only
- Remove complexity and duplication

**3. Block Quick Fixes**

- Stop AI from implementing workarounds
- Demand proper investigation first
- Use "Don't just do anything" to pause action

**4. Present Specific Evidence**

- Show exact error messages
- Point out what AI missed
- Keep focus on actual problems

**5. Validate Success Completely**

- Acknowledge progress but verify thoroughly
- Point out remaining issues immediately
- Don't accept partial solutions

## 🎯 Best Practices

### When Using Breakthrough Prompts:

- Be specific about errors and logs
- Demand root cause analysis over quick fixes
- Enforce production-quality standards
- Block implementation until investigation is complete

### When Using Work Summary Prompts:

- Use at natural breakpoints in development
- Include context about what you're trying to achieve
- Mention any specific areas of focus or concern

### When Using Git Commit Prompts:

- Ensure work summary is comprehensive
- Reference the Git commit template for consistency
- Review generated message before applying

### General Tips:

- Keep prompts bookmarked for quick access
- Customize prompts based on specific project needs
- Use consistent terminology across prompts
- Document any project-specific variations
- **ALWAYS demand root cause analysis for complex issues**

## 🔗 Related Documentation

- [PROBE-GIT-COMMIT-TEMPLATE.md](./PROBE-GIT-COMMIT-TEMPLATE.md) - Git commit message standards
- [PROBE-DEVELOPMENT-ROADMAP.md](./PROBE-DEVELOPMENT-ROADMAP.md) - Project development roadmap
- [PROBE-ISSUE-DOCUMENTATION-GUIDE.md](./development/PROBE-ISSUE-DOCUMENTATION-GUIDE.md) - Documentation standards

---

**Note:** These prompts are designed to work with AI assistants that have access to chat history and project documentation. Adjust as needed based on your specific development environment and workflow.
