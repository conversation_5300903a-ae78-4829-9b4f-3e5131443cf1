{"firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "storage": {"rules": "storage.rules"}, "hosting": {"public": "dist", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "headers": [{"source": "**", "headers": [{"key": "Cross-Origin-Opener-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Embedder-Policy", "value": "unsafe-none"}]}], "rewrites": [{"source": "**", "destination": "/index.html"}]}, "emulators": {"auth": {"port": 9099}, "firestore": {"port": 8080}, "storage": {"port": 9199}, "ui": {"enabled": true, "port": 4000}}}