/**
 * Utility to clear any cached demo data and reset the application state
 */
export class DemoDataCleaner {
  
  /**
   * Clear all potential demo data from local storage and caches
   */
  static clearAllDemoData(): void {
    try {
      console.log('🧹 Clearing all potential demo data...');
      
      // Clear any vibes-related local storage that might contain demo data
      const keysToCheck = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key) {
          keysToCheck.push(key);
        }
      }
      
      // Check for any keys that might contain demo data
      keysToCheck.forEach(key => {
        try {
          const value = localStorage.getItem(key);
          if (value && (
            value.includes('test-album-1') ||
            value.includes('test-track-') ||
            value.includes('Demo Album') ||
            value.includes('Demo Song')
          )) {
            console.log('🧹 Removing demo data from localStorage key:', key);
            localStorage.removeItem(key);
          }
        } catch (error) {
          // Ignore parsing errors
        }
      });
      
      // Clear session storage as well
      const sessionKeysToCheck = [];
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        if (key) {
          sessionKeysToCheck.push(key);
        }
      }
      
      sessionKeysToCheck.forEach(key => {
        try {
          const value = sessionStorage.getItem(key);
          if (value && (
            value.includes('test-album-1') ||
            value.includes('test-track-') ||
            value.includes('Demo Album') ||
            value.includes('Demo Song')
          )) {
            console.log('🧹 Removing demo data from sessionStorage key:', key);
            sessionStorage.removeItem(key);
          }
        } catch (error) {
          // Ignore parsing errors
        }
      });
      
      console.log('✅ Demo data cleanup completed');
      
    } catch (error) {
      console.error('❌ Failed to clear demo data:', error);
    }
  }
  
  /**
   * Force refresh the page after clearing demo data
   */
  static clearDemoDataAndRefresh(): void {
    this.clearAllDemoData();
    
    // Force a hard refresh to ensure all cached data is cleared
    setTimeout(() => {
      window.location.reload();
    }, 100);
  }
}

// Export for console access during development
if (typeof window !== 'undefined') {
  (window as any).clearDemoData = DemoDataCleaner.clearDemoDataAndRefresh;
}
