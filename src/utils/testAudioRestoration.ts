/**
 * Test utility for validating audio restoration after page refresh
 */

export const testAudioRestoration = () => {
  console.log('🧪 Testing Audio Restoration...');
  
  // Check if we have a current track in the store
  const musicState = localStorage.getItem('vibes-music-state');
  if (!musicState) {
    console.log('❌ No music state found in localStorage');
    return false;
  }
  
  let parsedState;
  try {
    parsedState = JSON.parse(musicState);
  } catch (error) {
    console.error('❌ Failed to parse music state:', error);
    return false;
  }
  
  const currentTrack = parsedState.state?.currentTrack;
  if (!currentTrack) {
    console.log('❌ No current track in persisted state');
    return false;
  }
  
  console.log('📀 Found persisted track:', currentTrack.title, 'by', currentTrack.artist);
  
  // Check if AudioService has a loaded track
  const audioService = (window as any).AudioService;
  if (!audioService) {
    console.log('❌ AudioService not available globally');
    return false;
  }
  
  // Test if we can get duration (indicates track is loaded)
  const duration = audioService.getDuration();
  const isLoading = audioService.isLoadingTrack ? audioService.isLoadingTrack() : false;
  const isReady = audioService.isReadyForPlayback ? audioService.isReadyForPlayback() : false;

  console.log('⏱️ Track duration:', duration);
  console.log('🔄 Is loading:', isLoading);
  console.log('✅ Is ready for playback:', isReady);

  if (duration > 0 && !isLoading) {
    console.log('✅ Track is properly loaded in AudioService');
    return true;
  } else if (isLoading) {
    console.log('⏳ Track is still loading...');
    return false;
  } else {
    console.log('❌ Track is not loaded in AudioService (duration = 0)');
    return false;
  }
};

// Test if we can play the track
export const testPlayback = async () => {
  console.log('🎵 Testing Playback...');

  const audioService = (window as any).AudioService;
  if (!audioService) {
    console.log('❌ AudioService not available');
    return false;
  }

  try {
    const success = await audioService.playWithUserGesture();
    if (success) {
      console.log('✅ Playback started successfully');
      // Stop it immediately for testing
      setTimeout(() => audioService.pause(), 1000);
      return true;
    } else {
      console.log('❌ Playback failed');
      return false;
    }
  } catch (error) {
    console.error('❌ Playback error:', error);
    return false;
  }
};

// Force load the current track from the store
export const forceLoadTrack = async () => {
  console.log('🔧 Force loading track...');

  // Get current track from localStorage
  const musicState = localStorage.getItem('vibes-music-state');
  if (!musicState) {
    console.log('❌ No music state found');
    return false;
  }

  let parsedState;
  try {
    parsedState = JSON.parse(musicState);
  } catch (error) {
    console.error('❌ Failed to parse music state:', error);
    return false;
  }

  const currentTrack = parsedState.state?.currentTrack;
  if (!currentTrack) {
    console.log('❌ No current track in state');
    return false;
  }

  const audioService = (window as any).AudioService;
  if (!audioService) {
    console.log('❌ AudioService not available');
    return false;
  }

  try {
    console.log('🎵 Force loading track:', currentTrack.title);
    await audioService.loadTrack(currentTrack, {
      onLoad: () => {
        const duration = audioService.getDuration();
        console.log('✅ Track force loaded successfully, duration:', duration);
      },
      onLoadError: (error) => {
        console.error('❌ Force load failed:', error);
      }
    });
    return true;
  } catch (error) {
    console.error('❌ Force load error:', error);
    return false;
  }
};

// Comprehensive test that checks the entire restoration flow
export const testFullRestorationFlow = async () => {
  console.log('🧪 Running Full Audio Restoration Test...');

  // Step 1: Check if we have persisted state
  const hasState = testAudioRestoration();
  if (!hasState) {
    console.log('❌ No persisted state found, cannot test restoration');
    return false;
  }

  // Step 2: Wait a moment for loading to complete
  console.log('⏳ Waiting for track loading to complete...');
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Step 3: Re-check if track is loaded
  const isLoaded = testAudioRestoration();
  if (!isLoaded) {
    console.log('❌ Track failed to load after waiting');
    return false;
  }

  // Step 4: Test playback
  const canPlay = await testPlayback();
  if (!canPlay) {
    console.log('❌ Playback test failed');
    return false;
  }

  console.log('✅ Full restoration flow test passed!');
  return true;
};

// Test the play button behavior after refresh
export const testPlayButtonAfterRefresh = async () => {
  console.log('🧪 Testing Play Button After Refresh...');

  // Check if we have a current track
  const hasTrack = testAudioRestoration();
  if (!hasTrack) {
    console.log('❌ No track available for testing');
    return false;
  }

  // Get the music store
  const musicStore = (window as any).useMusicStore?.getState();
  if (!musicStore) {
    console.log('❌ Music store not available');
    return false;
  }

  console.log('🎵 Current isPlaying state:', musicStore.isPlaying);

  // Simulate play button click
  console.log('🎵 Simulating play button click...');
  musicStore.togglePlay();

  // Wait a moment for the effect to process
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Check if audio is actually playing
  const audioService = (window as any).AudioService;
  const isAudioPlaying = audioService?.isPlaying() || false;
  const newIsPlayingState = (window as any).useMusicStore?.getState()?.isPlaying || false;

  console.log('🎵 After click - Store isPlaying:', newIsPlayingState);
  console.log('🎵 After click - Audio isPlaying:', isAudioPlaying);

  if (newIsPlayingState && isAudioPlaying) {
    console.log('✅ Play button test passed - audio is playing!');
    return true;
  } else if (newIsPlayingState && !isAudioPlaying) {
    console.log('❌ Play button test failed - state is playing but no audio');
    return false;
  } else {
    console.log('ℹ️ Play button toggled to pause state');
    return true;
  }
};

// Make functions available globally for testing
if (typeof window !== 'undefined') {
  (window as any).testAudioRestoration = testAudioRestoration;
  (window as any).testPlayback = testPlayback;
  (window as any).forceLoadTrack = forceLoadTrack;
  (window as any).testFullRestorationFlow = testFullRestorationFlow;
  (window as any).testPlayButtonAfterRefresh = testPlayButtonAfterRefresh;
}
