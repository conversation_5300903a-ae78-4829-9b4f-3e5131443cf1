/**
 * Utility to test carousel loading and diagnose Firebase Target ID conflicts
 */
import { CarouselService } from '../services/carouselService';

export const testCarouselLoading = async (): Promise<void> => {
  console.log('🧪 Testing carousel loading...');
  
  const startTime = performance.now();
  
  try {
    // Test 1: Single load
    console.log('🧪 Test 1: Single carousel load');
    const slides1 = await CarouselService.getActiveSlides();
    console.log(`✅ Test 1 passed: ${slides1.length} slides loaded`);
    
    // Test 2: Immediate second load (should use cache)
    console.log('🧪 Test 2: Immediate second load (cache test)');
    const slides2 = await CarouselService.getActiveSlides();
    console.log(`✅ Test 2 passed: ${slides2.length} slides loaded from cache`);
    
    // Test 3: Concurrent loads (Target ID conflict test)
    console.log('🧪 Test 3: Concurrent loads (Target ID conflict test)');
    const [slides3a, slides3b, slides3c] = await Promise.all([
      CarouselService.getActiveSlides(),
      CarouselService.getActiveSlides(),
      CarouselService.getActiveSlides()
    ]);
    console.log(`✅ Test 3 passed: Concurrent loads successful (${slides3a.length}, ${slides3b.length}, ${slides3c.length})`);
    
    const totalTime = performance.now() - startTime;
    console.log(`🎉 All carousel tests passed in ${totalTime.toFixed(2)}ms`);
    
  } catch (error: any) {
    console.error('❌ Carousel test failed:', error.message);
    console.error('🔍 Error details:', error);
  }
};

export const clearCarouselCache = (): void => {
  console.log('🧹 Clearing carousel cache for testing...');
  CarouselService.clearCache();
  console.log('✅ Cache cleared');
};

// Make functions available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).testCarouselLoading = testCarouselLoading;
  (window as any).clearCarouselCache = clearCarouselCache;
}
