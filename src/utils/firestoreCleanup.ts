import { 
  collection, 
  getDocs, 
  doc, 
  updateDoc, 
  writeBatch,
  deleteField 
} from 'firebase/firestore';
import { db } from '../lib/firebase';

/**
 * Utility to clean up Firestore documents that have explicit null values
 * for optional fields, which can cause "INTERNAL ASSERTION FAILED" errors.
 */
export class FirestoreCleanup {
  
  /**
   * Clean up tracks collection - remove null values for optional fields
   */
  static async cleanupTracks(): Promise<void> {
    console.log('Starting tracks cleanup...');
    
    try {
      const tracksSnapshot = await getDocs(collection(db, 'tracks'));
      const batch = writeBatch(db);
      let updateCount = 0;
      
      tracksSnapshot.docs.forEach((docSnapshot) => {
        const data = docSnapshot.data();
        const updates: any = {};
        let needsUpdate = false;
        
        // Check for null values in optional fields
        if (data.albumId === null) {
          updates.albumId = deleteField();
          needsUpdate = true;
        }
        
        if (data.albumTitle === null) {
          updates.albumTitle = deleteField();
          needsUpdate = true;
        }
        
        if (data.trackNumber === null) {
          updates.trackNumber = deleteField();
          needsUpdate = true;
        }
        
        if (data.coverUrl === null) {
          updates.coverUrl = deleteField();
          needsUpdate = true;
        }
        
        if (data.genre === null) {
          updates.genre = deleteField();
          needsUpdate = true;
        }
        
        if (data.mood === null) {
          updates.mood = deleteField();
          needsUpdate = true;
        }
        
        if (data.publishedAt === null) {
          updates.publishedAt = deleteField();
          needsUpdate = true;
        }
        
        if (needsUpdate) {
          batch.update(doc(db, 'tracks', docSnapshot.id), updates);
          updateCount++;
          console.log(`Queued cleanup for track: ${docSnapshot.id}`);
        }
      });
      
      if (updateCount > 0) {
        await batch.commit();
        console.log(`Successfully cleaned up ${updateCount} tracks`);
      } else {
        console.log('No tracks needed cleanup');
      }
      
    } catch (error) {
      console.error('Error cleaning up tracks:', error);
      throw error;
    }
  }
  
  /**
   * Clean up albums collection - remove null values for optional fields
   */
  static async cleanupAlbums(): Promise<void> {
    console.log('Starting albums cleanup...');
    
    try {
      const albumsSnapshot = await getDocs(collection(db, 'albums'));
      const batch = writeBatch(db);
      let updateCount = 0;
      
      albumsSnapshot.docs.forEach((docSnapshot) => {
        const data = docSnapshot.data();
        const updates: any = {};
        let needsUpdate = false;
        
        // Check for null values in optional fields
        if (data.description === null) {
          updates.description = deleteField();
          needsUpdate = true;
        }
        
        if (data.coverUrl === null) {
          updates.coverUrl = deleteField();
          needsUpdate = true;
        }
        
        if (data.genre === null) {
          updates.genre = deleteField();
          needsUpdate = true;
        }
        
        if (data.publishedAt === null) {
          updates.publishedAt = deleteField();
          needsUpdate = true;
        }
        
        if (data.totalDuration === null) {
          updates.totalDuration = deleteField();
          needsUpdate = true;
        }
        
        if (data.trackCount === null) {
          updates.trackCount = deleteField();
          needsUpdate = true;
        }
        
        if (needsUpdate) {
          batch.update(doc(db, 'albums', docSnapshot.id), updates);
          updateCount++;
          console.log(`Queued cleanup for album: ${docSnapshot.id}`);
        }
      });
      
      if (updateCount > 0) {
        await batch.commit();
        console.log(`Successfully cleaned up ${updateCount} albums`);
      } else {
        console.log('No albums needed cleanup');
      }
      
    } catch (error) {
      console.error('Error cleaning up albums:', error);
      throw error;
    }
  }
  
  /**
   * Clean up users collection - remove null values for optional fields
   */
  static async cleanupUsers(): Promise<void> {
    console.log('Starting users cleanup...');
    
    try {
      const usersSnapshot = await getDocs(collection(db, 'users'));
      const batch = writeBatch(db);
      let updateCount = 0;
      
      usersSnapshot.docs.forEach((docSnapshot) => {
        const data = docSnapshot.data();
        const updates: any = {};
        let needsUpdate = false;
        
        // Check for null values in optional fields
        if (data.photoURL === null) {
          updates.photoURL = deleteField();
          needsUpdate = true;
        }
        
        if (data.bio === null) {
          updates.bio = deleteField();
          needsUpdate = true;
        }
        
        if (needsUpdate) {
          batch.update(doc(db, 'users', docSnapshot.id), updates);
          updateCount++;
          console.log(`Queued cleanup for user: ${docSnapshot.id}`);
        }
      });
      
      if (updateCount > 0) {
        await batch.commit();
        console.log(`Successfully cleaned up ${updateCount} users`);
      } else {
        console.log('No users needed cleanup');
      }
      
    } catch (error) {
      console.error('Error cleaning up users:', error);
      throw error;
    }
  }
  
  /**
   * Run complete cleanup for all collections
   */
  static async runCompleteCleanup(): Promise<void> {
    console.log('Starting complete Firestore cleanup...');
    
    try {
      await this.cleanupTracks();
      await this.cleanupAlbums();
      await this.cleanupUsers();
      
      console.log('Complete Firestore cleanup finished successfully!');
    } catch (error) {
      console.error('Error during complete cleanup:', error);
      throw error;
    }
  }
}
