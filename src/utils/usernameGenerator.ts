// Username generation utilities

const adjectives = [
  'Cool', 'Epic', 'Awesome', 'Stellar', 'Cosmic', 'Vibrant', 'Electric', 'Sonic', 
  '<PERSON><PERSON>', 'Neon', 'Digital', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Future', 'Quantum', 'Pulse',
  'Beat', 'Rhythm', 'Melody', 'Harmony', 'Bass', 'Treble', 'Echo', 'Reverb',
  'Synth', 'Wave', 'Flow', 'Groove', 'Vibe', 'Chill', 'Smooth', 'Sharp',
  'Bright', 'Dark', 'Deep', 'High', 'Low', 'Fast', 'Slow', 'Wild', 'Calm',
  'Bold', 'Soft', 'Loud', 'Quiet', 'Pure', 'Raw', 'Fresh', 'Classic'
];

const nouns = [
  'Beats', 'Vibes', 'Waves', 'Sound', 'Music', 'Rhythm', 'Melody', 'Harmony',
  'Bass', 'Treble', 'Echo', 'Reverb', 'Synth', 'Drop', 'Loop', 'Track',
  'Mix', 'Remix', 'Beat', 'Flow', 'Groove', 'Pulse', 'Tempo', 'Note',
  'Chord', 'Scale', 'Key', 'Tune', 'Song', 'Album', 'Playlist', 'Artist',
  'DJ', 'Producer', 'Mixer', 'Studio', 'Stage', 'Concert', 'Festival', 'Club',
  'Dance', 'Party', 'Rave', 'Jam', 'Session', 'Live', 'Record', 'Play'
];

/**
 * Generates a random username based on music-related terms
 */
export function generateRandomUsername(): string {
  const adjective = adjectives[Math.floor(Math.random() * adjectives.length)];
  const noun = nouns[Math.floor(Math.random() * nouns.length)];
  const number = Math.floor(Math.random() * 999) + 1;
  
  return `${adjective}${noun}${number}`;
}

/**
 * Generates a username from display name with fallback to random
 */
export function generateUsernameFromDisplayName(displayName: string): string {
  if (!displayName) {
    return generateRandomUsername();
  }

  // Clean the display name: remove spaces, special chars, convert to lowercase
  const cleanName = displayName
    .toLowerCase()
    .replace(/[^a-z0-9]/g, '')
    .substring(0, 15); // Limit length

  if (cleanName.length < 3) {
    return generateRandomUsername();
  }

  // Add random number to make it unique
  const number = Math.floor(Math.random() * 999) + 1;
  return `${cleanName}${number}`;
}

/**
 * Generates a username from email with fallback to random
 */
export function generateUsernameFromEmail(email: string): string {
  if (!email) {
    return generateRandomUsername();
  }

  // Extract username part from email
  const emailUsername = email.split('@')[0];
  
  // Clean the username: remove special chars, convert to lowercase
  const cleanName = emailUsername
    .toLowerCase()
    .replace(/[^a-z0-9]/g, '')
    .substring(0, 15); // Limit length

  if (cleanName.length < 3) {
    return generateRandomUsername();
  }

  // Add random number to make it unique
  const number = Math.floor(Math.random() * 999) + 1;
  return `${cleanName}${number}`;
}

/**
 * Validates if a username is acceptable
 */
export function isValidUsername(username: string): boolean {
  // Username should be 3-20 characters, alphanumeric only
  const usernameRegex = /^[a-zA-Z0-9]{3,20}$/;
  return usernameRegex.test(username);
}

/**
 * Generates a smart username based on available user data
 */
export function generateSmartUsername(displayName?: string, email?: string): string {
  // Try display name first
  if (displayName && displayName.trim()) {
    return generateUsernameFromDisplayName(displayName.trim());
  }
  
  // Try email if no display name
  if (email) {
    return generateUsernameFromEmail(email);
  }
  
  // Fallback to random
  return generateRandomUsername();
}
