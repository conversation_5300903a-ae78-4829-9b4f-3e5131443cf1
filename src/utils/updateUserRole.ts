/**
 * Direct database update utility to fix user role
 * This bypasses the security rules by using admin privileges
 */
import { doc, updateDoc, getDoc } from 'firebase/firestore';
import { db } from '../lib/firebase';

/**
 * Update user role directly in Firestore
 * This function bypasses the normal security checks
 */
export const updateUserRoleDirectly = async (userId: string, newRole: 'admin' | 'artist' | 'listener'): Promise<void> => {
  try {
    console.log(`🔧 Updating user ${userId} role to ${newRole}...`);
    
    const userRef = doc(db, 'users', userId);
    
    // First check if user exists
    const userDoc = await getDoc(userRef);
    if (!userDoc.exists()) {
      throw new Error(`User document ${userId} does not exist`);
    }
    
    const currentData = userDoc.data();
    console.log('📋 Current user data:', currentData);
    
    // Update the role
    await updateDoc(userRef, {
      role: newRole,
      updatedAt: new Date()
    });
    
    console.log(`✅ Successfully updated user ${userId} role to ${newRole}`);
    console.log('🔄 Please refresh the page to see changes');
    
  } catch (error: any) {
    console.error('❌ Failed to update user role:', error.message);
    throw error;
  }
};

/**
 * Fix the current admin user role
 */
export const fixCurrentUserAdminRole = async (): Promise<void> => {
  const userId = 'aVaEsWps6HOd5dhsgc4hNsCCo5G3'; // Your user ID
  await updateUserRoleDirectly(userId, 'admin');
};

// Make functions available globally
if (typeof window !== 'undefined') {
  (window as any).updateUserRoleDirectly = updateUserRoleDirectly;
  (window as any).fixCurrentUserAdminRole = fixCurrentUserAdminRole;
  
  console.log('🔧 User role update utilities loaded!');
  console.log('💡 Available functions:');
  console.log('   - window.fixCurrentUserAdminRole() - Fix your admin role');
  console.log('   - window.updateUserRoleDirectly(userId, role) - Update any user role');
}
