/**
 * Simple event emitter for cross-component communication
 * Used for real-time updates when tracks are liked/unliked
 */

type EventCallback = (...args: any[]) => void;

class EventEmitter {
  private events: { [key: string]: EventCallback[] } = {};

  /**
   * Subscribe to an event
   */
  on(event: string, callback: EventCallback): void {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(callback);
  }

  /**
   * Unsubscribe from an event
   */
  off(event: string, callback: EventCallback): void {
    if (!this.events[event]) return;
    
    this.events[event] = this.events[event].filter(cb => cb !== callback);
  }

  /**
   * Emit an event
   */
  emit(event: string, ...args: any[]): void {
    if (!this.events[event]) return;
    
    this.events[event].forEach(callback => {
      try {
        callback(...args);
      } catch (error) {
        console.error(`Error in event callback for ${event}:`, error);
      }
    });
  }

  /**
   * Remove all listeners for an event
   */
  removeAllListeners(event?: string): void {
    if (event) {
      delete this.events[event];
    } else {
      this.events = {};
    }
  }
}

// Global event emitter instance
export const eventEmitter = new EventEmitter();

// Event types for type safety
export const EVENTS = {
  TRACK_LIKED: 'track:liked',
  TRACK_UNLIKED: 'track:unliked',
  ALBUM_LIKED: 'album:liked',
  ALBUM_UNLIKED: 'album:unliked',
  TRACK_SAVED: 'track:saved',
  TRACK_UNSAVED: 'track:unsaved',
  ALBUM_SAVED: 'album:saved',
  ALBUM_UNSAVED: 'album:unsaved',
} as const;

export type EventType = typeof EVENTS[keyof typeof EVENTS];
