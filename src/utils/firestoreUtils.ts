import { getFirestore, connectFirestoreEmulator, enableNetwork, disableNetwork } from 'firebase/firestore';
import { app } from '../config/firebase';

let db: any = null;
let isConnected = false;

/**
 * Get a fresh Firestore instance
 */
export const getFreshFirestore = () => {
  try {
    if (!db) {
      db = getFirestore(app);
      isConnected = true;
    }
    return db;
  } catch (error) {
    console.error('Error getting Firestore instance:', error);
    throw error;
  }
};

/**
 * Reset Firestore connection
 */
export const resetFirestoreConnection = async () => {
  try {
    console.log('🔧 Resetting Firestore connection...');
    
    if (db && isConnected) {
      await disableNetwork(db);
      console.log('🔧 Firestore network disabled');
    }
    
    // Wait a moment
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Get fresh instance
    db = getFirestore(app);
    
    // Re-enable network
    await enableNetwork(db);
    isConnected = true;
    
    console.log('🔧 Firestore connection reset successfully');
    return db;
  } catch (error) {
    console.error('🔴 Error resetting Firestore connection:', error);
    throw error;
  }
};

/**
 * Check if Firestore is connected
 */
export const isFirestoreConnected = () => {
  return isConnected;
};

/**
 * Safely execute a Firestore operation with retry
 */
export const safeFirestoreOperation = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3
): Promise<T> => {
  let lastError: any;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🔧 Attempting Firestore operation (attempt ${attempt}/${maxRetries})`);
      return await operation();
    } catch (error: any) {
      lastError = error;
      console.error(`🔴 Firestore operation failed (attempt ${attempt}/${maxRetries}):`, error);
      
      if (attempt < maxRetries) {
        // Reset connection before retry
        try {
          await resetFirestoreConnection();
          console.log('🔧 Connection reset, retrying...');
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt)); // Exponential backoff
        } catch (resetError) {
          console.error('🔴 Failed to reset connection:', resetError);
        }
      }
    }
  }
  
  throw lastError;
};
