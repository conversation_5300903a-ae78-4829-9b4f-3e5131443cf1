/**
 * Test utility for validating music persistence functionality
 * This can be run in the browser console to test the restoration
 */

export const testMusicPersistence = () => {
  console.log('🧪 Testing Music Persistence...');
  
  // Check if localStorage has music state
  const storedState = localStorage.getItem('vibes-music-state');
  
  if (!storedState) {
    console.log('❌ No persisted music state found in localStorage');
    return false;
  }
  
  try {
    const parsed = JSON.parse(storedState);
    console.log('💾 Found persisted state:', parsed);
    
    // Check if state has required properties
    const hasCurrentTrack = parsed.state?.currentTrack;
    const hasQueue = parsed.state?.queue?.length > 0;
    const hasVolume = typeof parsed.state?.volume === 'number';
    
    console.log('📊 State validation:', {
      hasCurrentTrack: !!hasCurrentTrack,
      hasQueue,
      hasVolume,
      queueLength: parsed.state?.queue?.length || 0,
      volume: parsed.state?.volume
    });
    
    if (hasCurrentTrack && hasQueue) {
      console.log('✅ Music persistence is working correctly!');
      console.log('🎵 Current track:', parsed.state.currentTrack.title, 'by', parsed.state.currentTrack.artist);
      return true;
    } else {
      console.log('⚠️ Persisted state is incomplete');
      return false;
    }
  } catch (error) {
    console.error('❌ Failed to parse persisted state:', error);
    return false;
  }
};

// Make it available globally for testing
if (typeof window !== 'undefined') {
  (window as any).testMusicPersistence = testMusicPersistence;
}
