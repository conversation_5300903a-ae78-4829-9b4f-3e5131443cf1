/**
 * Utility functions for Firestore operations
 */

/**
 * Filters out undefined values from an object to prevent Firestore errors
 * Firestore doesn't accept undefined values - they must be omitted or set to null
 * 
 * @param obj - Object that may contain undefined values
 * @returns Object with undefined values filtered out
 */
export const filterUndefinedValues = <T extends Record<string, any>>(obj: T): Partial<T> => {
  return Object.fromEntries(
    Object.entries(obj).filter(([_, value]) => value !== undefined)
  ) as Partial<T>;
};

/**
 * Prepares an update object for Firestore by filtering undefined values
 * and adding updatedAt timestamp
 * 
 * @param updates - Update object that may contain undefined values
 * @returns Clean update object ready for Firestore
 */
export const prepareFirestoreUpdate = <T extends Record<string, any>>(
  updates: T
): Partial<T> & { updatedAt: Date } => {
  const cleanUpdates = filterUndefinedValues(updates);
  return {
    ...cleanUpdates,
    updatedAt: new Date()
  };
};
