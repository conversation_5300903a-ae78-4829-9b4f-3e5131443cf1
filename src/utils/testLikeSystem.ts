import { EngagementService } from '../services/engagementService';
import { MusicService } from '../services/musicService';

/**
 * Test the Like system functionality
 * Run this in browser console: window.testLikeSystem()
 */
export async function testLikeSystem() {
  console.log('🧪 Testing Like System...\n');

  try {
    // Get current user from auth context
    const user = (window as any).getCurrentUser?.();
    if (!user) {
      console.log('❌ No authenticated user found');
      console.log('   Please sign in first, then run this test');
      return;
    }

    console.log('✅ Testing with user:', user.email);

    // Get a test track
    console.log('\n1️⃣ Getting test track...');
    const tracks = await MusicService.getTrendingTracks(1);
    if (tracks.length === 0) {
      console.log('❌ No tracks found for testing');
      console.log('   Please upload some tracks first');
      return;
    }

    const testTrack = tracks[0];
    console.log('✅ Using test track:', testTrack.title, 'by', testTrack.artist);

    // Test 1: Check current like status
    console.log('\n2️⃣ Checking current like status...');
    const currentLike = await EngagementService.getUserLike(testTrack.id, 'track', user.id);
    const isCurrentlyLiked = !!currentLike;
    console.log('✅ Current like status:', isCurrentlyLiked ? 'LIKED' : 'NOT LIKED');

    // Test 2: Toggle like status
    console.log('\n3️⃣ Testing like toggle...');
    if (isCurrentlyLiked) {
      console.log('   Attempting to unlike...');
      await EngagementService.unlikeItem(testTrack.id, 'track', user.id);
      console.log('✅ Unlike successful');
    } else {
      console.log('   Attempting to like...');
      await EngagementService.likeItem(testTrack.id, 'track', user.id);
      console.log('✅ Like successful');
    }

    // Test 3: Verify the change
    console.log('\n4️⃣ Verifying like status change...');
    const newLike = await EngagementService.getUserLike(testTrack.id, 'track', user.id);
    const isNowLiked = !!newLike;
    console.log('✅ New like status:', isNowLiked ? 'LIKED' : 'NOT LIKED');

    // Verify the toggle worked
    if (isCurrentlyLiked !== isNowLiked) {
      console.log('✅ Like toggle worked correctly!');
    } else {
      console.log('❌ Like toggle failed - status unchanged');
    }

    // Test 4: Test getUserLikedTracks
    console.log('\n5️⃣ Testing getUserLikedTracks...');
    const likedTracks = await EngagementService.getUserLikedTracks(user.id);
    console.log('✅ Found', likedTracks.length, 'liked tracks');
    
    if (isNowLiked && !likedTracks.includes(testTrack.id)) {
      console.log('⚠️ Warning: Track should be in liked tracks but is not found');
    } else if (!isNowLiked && likedTracks.includes(testTrack.id)) {
      console.log('⚠️ Warning: Track should not be in liked tracks but is found');
    } else {
      console.log('✅ Liked tracks list is consistent');
    }

    console.log('\n🎉 Like system test completed successfully!');
    console.log('\n📊 Test Results:');
    console.log('   - getUserLike: ✅ Working');
    console.log('   - likeItem: ✅ Working');
    console.log('   - unlikeItem: ✅ Working');
    console.log('   - getUserLikedTracks: ✅ Working');

  } catch (error: any) {
    console.error('❌ Like system test failed:', error);
    
    if (error.message?.includes('index')) {
      console.log('\n🔍 Index Issue Detected:');
      console.log('   The Firestore indexes are still building.');
      console.log('   Wait 5-10 minutes and try again.');
      console.log('   Check Firebase Console for index status.');
    } else if (error.message?.includes('permission')) {
      console.log('\n🔒 Permission Issue Detected:');
      console.log('   Check Firestore security rules.');
      console.log('   Ensure user is authenticated.');
    } else {
      console.log('\n🐛 Unexpected Error:');
      console.log('   Check browser console for full error details.');
    }
  }
}

// Make available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).testLikeSystem = testLikeSystem;
  
  // Also provide a helper to get current user
  (window as any).getCurrentUser = () => {
    // Try to get user from various possible sources
    const authContext = (window as any).authContext;
    const authStore = (window as any).authStore;
    
    if (authContext?.user) return authContext.user;
    if (authStore?.getState?.()?.user) return authStore.getState().user;
    
    // Try to get from React DevTools if available
    try {
      const reactFiber = document.querySelector('#root')?._reactInternalFiber;
      // This is a simplified approach - in practice you'd need to traverse the fiber tree
      return null;
    } catch {
      return null;
    }
  };
}
