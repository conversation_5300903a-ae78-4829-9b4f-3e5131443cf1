/**
 * Query Verification Utility
 * This utility helps verify that our Firestore queries are constructed correctly
 * to avoid the "Target ID already exists" error.
 */

import { 
  collection, 
  query, 
  where, 
  orderBy,
  QueryConstraint
} from 'firebase/firestore';
import { db } from '../lib/firebase';

/**
 * Demonstrates the correct way to build conditional Firestore queries
 * This is the pattern we now use in MusicService and AlbumService
 */
export function buildConditionalQuery(
  collectionName: string,
  userId: string,
  includeStatus?: 'draft' | 'published',
  additionalConstraints?: QueryConstraint[]
) {
  // Build constraints array conditionally
  const constraints: QueryConstraint[] = [
    where('uploadedBy', '==', userId)
  ];

  // Add additional constraints if provided
  if (additionalConstraints) {
    constraints.push(...additionalConstraints);
  }

  // Add status filter if specified
  if (includeStatus) {
    constraints.push(where('status', '==', includeStatus));
  }

  // Add ordering
  constraints.push(orderBy('createdAt', 'desc'));

  // Create the query with all constraints at once
  return query(collection(db, collectionName), ...constraints);
}

/**
 * Example of the OLD problematic pattern that caused "Target ID already exists"
 * DO NOT USE THIS PATTERN
 */
export function buildProblematicQuery(
  collectionName: string,
  userId: string,
  includeStatus?: 'draft' | 'published'
) {
  // This is the OLD way that caused conflicts
  let q = query(
    collection(db, collectionName),
    where('uploadedBy', '==', userId),
    orderBy('createdAt', 'desc')
  );

  if (includeStatus) {
    // Creating a completely new query causes Target ID conflicts
    q = query(
      collection(db, collectionName),
      where('uploadedBy', '==', userId),
      where('status', '==', includeStatus),
      orderBy('createdAt', 'desc')
    );
  }

  return q;
}

/**
 * Verification function to ensure our query building is correct
 */
export function verifyQueryConstruction() {
  console.log('🔍 Verifying query construction patterns...');
  
  try {
    // Test the correct pattern
    const correctQuery = buildConditionalQuery('tracks', 'test-user', 'published');
    console.log('✅ Conditional query built successfully with correct pattern');
    
    // Test with additional constraints (for singles)
    const singlesQuery = buildConditionalQuery(
      'tracks', 
      'test-user', 
      'published',
      [where('albumId', '==', null)]
    );
    console.log('✅ Singles query built successfully with correct pattern');
    
    console.log('🎉 All query patterns verified successfully!');
    return true;
  } catch (error) {
    console.error('❌ Query construction verification failed:', error);
    return false;
  }
}

/**
 * Explanation of the fix:
 * 
 * PROBLEM:
 * The "Target ID already exists: 2" error occurred because we were creating
 * multiple separate query() calls with similar constraints. Firestore assigns
 * internal target IDs to queries, and when similar queries are created in
 * quick succession, they can conflict.
 * 
 * SOLUTION:
 * Instead of creating separate queries, we build an array of constraints
 * conditionally and then create a single query with all constraints at once.
 * This ensures only one query target is created, eliminating the conflict.
 * 
 * BEFORE (problematic):
 * let q = query(collection, where1, orderBy);
 * if (condition) {
 *   q = query(collection, where1, where2, orderBy); // New query = conflict
 * }
 * 
 * AFTER (fixed):
 * const constraints = [where1];
 * if (condition) constraints.push(where2);
 * constraints.push(orderBy);
 * const q = query(collection, ...constraints); // Single query = no conflict
 */
