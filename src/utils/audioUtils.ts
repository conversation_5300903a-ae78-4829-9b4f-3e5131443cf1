/**
 * Utility functions for audio file processing
 */

/**
 * Audio metadata interface
 */
export interface AudioMetadata {
  duration: number;
  bitrate?: number;
  sampleRate?: number;
  channels?: number;
  fileSize: number;
  format?: string;
  title?: string;
  artist?: string;
  album?: string;
  year?: string;
  trackNumber?: number;
  genre?: string;
  hasEmbeddedArt?: boolean;
}

/**
 * Extract comprehensive metadata from an audio file
 * @param file - Audio file to analyze
 * @returns Promise that resolves to audio metadata
 */
export const getAudioMetadata = (file: File): Promise<AudioMetadata> => {
  return new Promise((resolve, reject) => {
    const audio = new Audio();

    audio.addEventListener('loadedmetadata', () => {
      const metadata: AudioMetadata = {
        duration: Math.floor(audio.duration),
        fileSize: file.size,
        format: getAudioFormat(file),
        // Basic file info
        title: extractTitleFromFilename(file.name),
      };

      resolve(metadata);
    });

    audio.addEventListener('error', (error) => {
      reject(new Error(`Failed to load audio metadata: ${error}`));
    });

    // Create object URL for the file
    const objectUrl = URL.createObjectURL(file);
    audio.src = objectUrl;

    // Clean up object URL after loading
    audio.addEventListener('loadedmetadata', () => {
      URL.revokeObjectURL(objectUrl);
    }, { once: true });

    audio.addEventListener('error', () => {
      URL.revokeObjectURL(objectUrl);
    }, { once: true });
  });
};

/**
 * Extract duration from an audio file (legacy function for backward compatibility)
 * @param file - Audio file to analyze
 * @returns Promise that resolves to duration in seconds
 */
export const getAudioDuration = (file: File): Promise<number> => {
  return getAudioMetadata(file).then(metadata => metadata.duration);
};

/**
 * Format duration from seconds to MM:SS format
 * @param seconds - Duration in seconds
 * @returns Formatted duration string (e.g., "3:45")
 */
export const formatDuration = (seconds: number): string => {
  if (!seconds || seconds === 0) return '0:00';
  
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

/**
 * Parse duration from MM:SS format to seconds
 * @param duration - Duration string in MM:SS format
 * @returns Duration in seconds
 */
export const parseDuration = (duration: string): number => {
  if (!duration) return 0;
  
  const parts = duration.split(':');
  if (parts.length !== 2) return 0;
  
  const minutes = parseInt(parts[0], 10);
  const seconds = parseInt(parts[1], 10);
  
  if (isNaN(minutes) || isNaN(seconds)) return 0;
  
  return minutes * 60 + seconds;
};

/**
 * Get audio format from file
 * @param file - Audio file
 * @returns Audio format string
 */
export const getAudioFormat = (file: File): string => {
  const extension = file.name.toLowerCase().split('.').pop();
  const mimeType = file.type.toLowerCase();

  // Map MIME types and extensions to readable formats
  if (mimeType.includes('mpeg') || extension === 'mp3') return 'MP3';
  if (mimeType.includes('wav') || extension === 'wav') return 'WAV';
  if (mimeType.includes('flac') || extension === 'flac') return 'FLAC';
  if (mimeType.includes('ogg') || extension === 'ogg') return 'OGG';
  if (mimeType.includes('aac') || extension === 'aac') return 'AAC';
  if (mimeType.includes('m4a') || extension === 'm4a') return 'M4A';

  return extension?.toUpperCase() || 'Unknown';
};

/**
 * Extract title from filename
 * @param filename - File name
 * @returns Cleaned title
 */
export const extractTitleFromFilename = (filename: string): string => {
  // Remove file extension
  let title = filename.replace(/\.[^/.]+$/, '');

  // Remove common prefixes like track numbers
  title = title.replace(/^\d+[\s\-\.]*/, '');

  // Replace underscores and dashes with spaces
  title = title.replace(/[_\-]/g, ' ');

  // Clean up multiple spaces
  title = title.replace(/\s+/g, ' ').trim();

  // Capitalize first letter of each word
  title = title.replace(/\b\w/g, l => l.toUpperCase());

  return title || filename;
};

/**
 * Format file size to human readable format
 * @param bytes - File size in bytes
 * @returns Formatted file size string
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Estimate bitrate from file size and duration
 * @param fileSize - File size in bytes
 * @param duration - Duration in seconds
 * @returns Estimated bitrate in kbps
 */
export const estimateBitrate = (fileSize: number, duration: number): number => {
  if (duration === 0) return 0;

  // Convert to bits per second, then to kbps
  const bitsPerSecond = (fileSize * 8) / duration;
  return Math.round(bitsPerSecond / 1000);
};

/**
 * Validate if a file is a supported audio format
 * @param file - File to validate
 * @returns True if file is a supported audio format
 */
export const isAudioFile = (file: File): boolean => {
  const supportedTypes = [
    'audio/mpeg',
    'audio/mp3',
    'audio/wav',
    'audio/ogg',
    'audio/aac',
    'audio/m4a',
    'audio/flac'
  ];

  return supportedTypes.includes(file.type) ||
         file.name.toLowerCase().match(/\.(mp3|wav|ogg|aac|m4a|flac)$/);
};
