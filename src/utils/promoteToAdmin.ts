/**
 * Utility script to promote current user to admin
 * This should only be used for initial setup when no admins exist
 */
import { UserManagementService } from '../services/userManagementService';
import { AuthService } from '../services/authService';

/**
 * Promote the current logged-in user to admin status
 * This function is exposed globally for browser console use
 */
export const promoteCurrentUserToAdmin = async (): Promise<void> => {
  try {
    console.log('🔧 Starting admin promotion process...');
    
    // Check if user is authenticated
    const currentUser = await AuthService.getCurrentUser();
    if (!currentUser) {
      throw new Error('No user is currently logged in');
    }
    
    console.log('👤 Current user:', currentUser.email, 'Role:', currentUser.role);
    
    // Check if there are existing admins
    const hasAdmins = await UserManagementService.hasAdminUsers();
    console.log('🔍 Existing admins in system:', hasAdmins);
    
    if (hasAdmins) {
      console.warn('⚠️ Admin users already exist. This function is only for initial setup.');
      console.log('💡 Contact an existing admin to promote your account.');
      return;
    }
    
    // Promote to admin
    console.log('🚀 Promoting user to admin...');
    await UserManagementService.initializeFirstAdmin(currentUser.id);
    
    console.log('✅ Successfully promoted to admin!');
    console.log('🔄 Refreshing user data...');
    
    // Refresh user data
    await AuthService.refreshUserData();
    
    console.log('🎉 Admin promotion complete! Please refresh the page to see changes.');
    
  } catch (error: any) {
    console.error('❌ Failed to promote user to admin:', error.message);
    console.error('🔍 Error details:', error);
    throw error;
  }
};

/**
 * Check current user's admin status
 */
export const checkAdminStatus = async (): Promise<void> => {
  try {
    const currentUser = await AuthService.getCurrentUser();
    if (!currentUser) {
      console.log('❌ No user is currently logged in');
      return;
    }
    
    console.log('👤 Current user:', currentUser.email);
    console.log('🏷️ Current role:', currentUser.role);
    console.log('🔑 Is admin:', currentUser.role === 'admin');
    
    const hasAdmins = await UserManagementService.hasAdminUsers();
    console.log('🔍 System has admin users:', hasAdmins);
    
  } catch (error: any) {
    console.error('❌ Failed to check admin status:', error.message);
  }
};

// Make functions available globally for browser console use
if (typeof window !== 'undefined') {
  (window as any).promoteCurrentUserToAdmin = promoteCurrentUserToAdmin;
  (window as any).checkAdminStatus = checkAdminStatus;
  
  console.log('🔧 Admin promotion utilities loaded!');
  console.log('💡 Available functions:');
  console.log('   - window.checkAdminStatus() - Check current user status');
  console.log('   - window.promoteCurrentUserToAdmin() - Promote to admin (initial setup only)');
}
