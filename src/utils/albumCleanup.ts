import { collection, getDocs, doc, updateDoc } from 'firebase/firestore';
import { db } from '../lib/firebase';
import { Album } from '../types';

/**
 * Utility to clean up albums with duplicate track IDs
 * This fixes the issue where tracks appear twice in albums
 */
export class AlbumCleanupService {
  
  /**
   * Remove duplicate track IDs from all albums
   */
  static async cleanupDuplicateTrackIds(): Promise<void> {
    try {
      console.log('🧹 Starting album cleanup for duplicate track IDs...');
      
      // Get all albums
      const albumsSnapshot = await getDocs(collection(db, 'albums'));
      const albums = albumsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Album[];
      
      console.log(`🔍 Found ${albums.length} albums to check`);
      
      let cleanedCount = 0;
      
      for (const album of albums) {
        // Remove duplicates from trackIds array
        const originalTrackIds = album.trackIds || [];
        const uniqueTrackIds = [...new Set(originalTrackIds)];
        
        if (originalTrackIds.length !== uniqueTrackIds.length) {
          console.log(`🧹 Cleaning album "${album.title}": ${originalTrackIds.length} -> ${uniqueTrackIds.length} tracks`);
          
          // Update the album with unique track IDs
          await updateDoc(doc(db, 'albums', album.id), {
            trackIds: uniqueTrackIds,
            trackCount: uniqueTrackIds.length,
            updatedAt: new Date()
          });
          
          cleanedCount++;
        }
      }
      
      console.log(`✅ Album cleanup completed. ${cleanedCount} albums cleaned.`);
      
    } catch (error) {
      console.error('❌ Failed to cleanup albums:', error);
      throw error;
    }
  }
  
  /**
   * Clean up a specific album by ID
   */
  static async cleanupAlbum(albumId: string): Promise<void> {
    try {
      const albumDoc = await getDocs(collection(db, 'albums'));
      const album = albumDoc.docs.find(doc => doc.id === albumId);
      
      if (!album) {
        throw new Error(`Album ${albumId} not found`);
      }
      
      const albumData = album.data() as Album;
      const originalTrackIds = albumData.trackIds || [];
      const uniqueTrackIds = [...new Set(originalTrackIds)];
      
      if (originalTrackIds.length !== uniqueTrackIds.length) {
        await updateDoc(doc(db, 'albums', albumId), {
          trackIds: uniqueTrackIds,
          trackCount: uniqueTrackIds.length,
          updatedAt: new Date()
        });
        
        console.log(`✅ Cleaned album "${albumData.title}": ${originalTrackIds.length} -> ${uniqueTrackIds.length} tracks`);
      } else {
        console.log(`✅ Album "${albumData.title}" is already clean`);
      }
      
    } catch (error) {
      console.error(`❌ Failed to cleanup album ${albumId}:`, error);
      throw error;
    }
  }
}
