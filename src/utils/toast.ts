/**
 * Simple toast notification utility
 * This provides user feedback for share actions
 */

export interface ToastOptions {
  type?: 'success' | 'error' | 'info';
  duration?: number;
}

export class Toast {
  private static container: HTMLElement | null = null;

  private static createContainer(): HTMLElement {
    if (this.container) return this.container;

    this.container = document.createElement('div');
    this.container.id = 'toast-container';
    this.container.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 9999;
      pointer-events: none;
    `;
    document.body.appendChild(this.container);
    return this.container;
  }

  static show(message: string, options: ToastOptions = {}): void {
    const { type = 'info', duration = 3000 } = options;
    const container = this.createContainer();

    const toast = document.createElement('div');
    toast.style.cssText = `
      background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#6366f1'};
      color: white;
      padding: 12px 16px;
      border-radius: 8px;
      margin-bottom: 8px;
      font-size: 14px;
      font-weight: 500;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transform: translateX(100%);
      transition: transform 0.3s ease-out;
      pointer-events: auto;
      max-width: 300px;
      word-wrap: break-word;
    `;
    toast.textContent = message;

    container.appendChild(toast);

    // Animate in
    requestAnimationFrame(() => {
      toast.style.transform = 'translateX(0)';
    });

    // Auto remove
    setTimeout(() => {
      toast.style.transform = 'translateX(100%)';
      setTimeout(() => {
        if (container.contains(toast)) {
          container.removeChild(toast);
        }
      }, 300);
    }, duration);
  }

  static success(message: string, duration?: number): void {
    this.show(message, { type: 'success', duration });
  }

  static error(message: string, duration?: number): void {
    this.show(message, { type: 'error', duration });
  }

  static info(message: string, duration?: number): void {
    this.show(message, { type: 'info', duration });
  }
}
