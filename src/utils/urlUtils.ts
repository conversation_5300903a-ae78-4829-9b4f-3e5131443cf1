/**
 * Utility functions for URL parameter handling and shared content parsing
 */

export interface SharedContentParams {
  track?: string;
  album?: string;
  playlist?: string;
  liked?: boolean;
}

/**
 * Parse URL search parameters into an object
 */
export function parseUrlParams(search: string): URLSearchParams {
  return new URLSearchParams(search);
}

/**
 * Extract shared content parameters from URL
 */
export function extractSharedContentParams(search: string): SharedContentParams {
  const params = parseUrlParams(search);
  
  return {
    track: params.get('track') || undefined,
    album: params.get('album') || undefined,
    playlist: params.get('playlist') || undefined,
    liked: params.get('liked') === 'true'
  };
}

/**
 * Check if URL contains shared content parameters
 */
export function hasSharedContent(search: string): boolean {
  const params = extractSharedContentParams(search);
  return !!(params.track || params.album || params.playlist || params.liked);
}

/**
 * Get the type of shared content from URL parameters
 */
export function getSharedContentType(search: string): 'track' | 'album' | 'playlist' | 'liked' | null {
  const params = extractSharedContentParams(search);
  
  if (params.track) return 'track';
  if (params.album) return 'album';
  if (params.playlist) return 'playlist';
  if (params.liked) return 'liked';
  
  return null;
}

/**
 * Build URL with parameters
 */
export function buildUrlWithParams(basePath: string, params: Record<string, string | boolean>): string {
  const url = new URL(basePath, window.location.origin);
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      url.searchParams.set(key, String(value));
    }
  });
  
  return url.toString();
}

/**
 * Remove specific parameters from URL
 */
export function removeUrlParams(search: string, paramsToRemove: string[]): string {
  const params = parseUrlParams(search);
  
  paramsToRemove.forEach(param => {
    params.delete(param);
  });
  
  return params.toString();
}

/**
 * Clean shared content parameters from URL (for navigation after loading)
 */
export function cleanSharedContentParams(search: string): string {
  return removeUrlParams(search, ['track', 'album', 'playlist', 'liked']);
}
