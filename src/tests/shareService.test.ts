/**
 * Test file for ShareService functionality
 * This file can be used to manually test share functionality
 */

import { ShareService } from '../services/shareService';
import { Track, Album, Playlist } from '../types';

// Mock data for testing
const mockTrack: Track = {
  id: 'test-track-1',
  title: 'Test Song',
  artist: 'Test Artist',
  duration: 180,
  audioUrl: 'https://example.com/audio.mp3',
  coverUrl: 'https://example.com/cover.jpg',
  albumTitle: 'Test Album',
  genre: 'Pop',
  releaseDate: new Date('2024-01-01'),
  playCount: 1000,
  likeCount: 50
};

const mockAlbum: Album = {
  id: 'test-album-1',
  title: 'Test Album',
  artist: 'Test Artist',
  coverUrl: 'https://example.com/album-cover.jpg',
  releaseDate: new Date('2024-01-01'),
  genre: 'Pop',
  trackIds: ['test-track-1', 'test-track-2'],
  trackCount: 2,
  duration: 360,
  playCount: 2000,
  likeCount: 100
};

const mockPlaylist: Playlist = {
  id: 'test-playlist-1',
  name: 'My Test Playlist',
  description: 'A test playlist for sharing',
  coverUrl: 'https://example.com/playlist-cover.jpg',
  createdBy: 'test-user-1',
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
  isPublic: true,
  tracks: [mockTrack],
  trackCount: 1
};

// Test functions
export const testShareFunctionality = {
  /**
   * Test track sharing
   */
  async testTrackShare(): Promise<void> {
    console.log('🧪 Testing track share...');
    try {
      const shareContent = ShareService.createTrackShareContent(mockTrack);
      console.log('Track share content:', shareContent);
      
      // Verify URL format
      const expectedUrl = `${window.location.origin}/play?track=${mockTrack.id}`;
      if (shareContent.url === expectedUrl) {
        console.log('✅ Track share URL format is correct');
      } else {
        console.error('❌ Track share URL format is incorrect');
        console.log('Expected:', expectedUrl);
        console.log('Actual:', shareContent.url);
      }
    } catch (error) {
      console.error('❌ Track share test failed:', error);
    }
  },

  /**
   * Test album sharing
   */
  async testAlbumShare(): Promise<void> {
    console.log('🧪 Testing album share...');
    try {
      const shareContent = ShareService.createAlbumShareContent(mockAlbum);
      console.log('Album share content:', shareContent);
      
      // Verify URL format
      const expectedUrl = `${window.location.origin}/play?album=${mockAlbum.id}`;
      if (shareContent.url === expectedUrl) {
        console.log('✅ Album share URL format is correct');
      } else {
        console.error('❌ Album share URL format is incorrect');
        console.log('Expected:', expectedUrl);
        console.log('Actual:', shareContent.url);
      }
    } catch (error) {
      console.error('❌ Album share test failed:', error);
    }
  },

  /**
   * Test playlist sharing
   */
  async testPlaylistShare(): Promise<void> {
    console.log('🧪 Testing playlist share...');
    try {
      const shareContent = ShareService.createPlaylistShareContent(mockPlaylist);
      console.log('Playlist share content:', shareContent);
      
      // Verify URL format
      const expectedUrl = `${window.location.origin}/play?playlist=${mockPlaylist.id}`;
      if (shareContent.url === expectedUrl) {
        console.log('✅ Playlist share URL format is correct');
      } else {
        console.error('❌ Playlist share URL format is incorrect');
        console.log('Expected:', expectedUrl);
        console.log('Actual:', shareContent.url);
      }
    } catch (error) {
      console.error('❌ Playlist share test failed:', error);
    }
  },

  /**
   * Test liked songs sharing
   */
  async testLikedSongsShare(): Promise<void> {
    console.log('🧪 Testing liked songs share...');
    try {
      const shareContent = ShareService.createLikedSongsShareContent();
      console.log('Liked songs share content:', shareContent);
      
      // Verify URL format
      const expectedUrl = `${window.location.origin}/play?liked=true`;
      if (shareContent.url === expectedUrl) {
        console.log('✅ Liked songs share URL format is correct');
      } else {
        console.error('❌ Liked songs share URL format is incorrect');
        console.log('Expected:', expectedUrl);
        console.log('Actual:', shareContent.url);
      }
    } catch (error) {
      console.error('❌ Liked songs share test failed:', error);
    }
  },

  /**
   * Test copy link functionality
   */
  async testCopyLinkFunctionality(): Promise<void> {
    console.log('🧪 Testing copy link functionality...');
    try {
      // Test track copy link
      const trackUrl = ShareService.generateTrackShareUrl(mockTrack);
      console.log('Track copy link URL:', trackUrl);

      // Test album copy link
      const albumUrl = ShareService.generateAlbumShareUrl(mockAlbum);
      console.log('Album copy link URL:', albumUrl);

      // Test playlist copy link
      const playlistUrl = ShareService.generatePlaylistShareUrl(mockPlaylist);
      console.log('Playlist copy link URL:', playlistUrl);

      // Test liked songs copy link
      const likedUrl = ShareService.generateLikedSongsShareUrl();
      console.log('Liked songs copy link URL:', likedUrl);

      console.log('✅ Copy link functionality tests completed');
    } catch (error) {
      console.error('❌ Copy link functionality test failed:', error);
    }
  },

  /**
   * Run all tests
   */
  async runAllTests(): Promise<void> {
    console.log('🚀 Starting ShareService tests...');
    await this.testTrackShare();
    await this.testAlbumShare();
    await this.testPlaylistShare();
    await this.testLikedSongsShare();
    await this.testCopyLinkFunctionality();
    console.log('✅ All ShareService tests completed!');
  }
};

// Export for use in browser console
(window as any).testShareFunctionality = testShareFunctionality;
