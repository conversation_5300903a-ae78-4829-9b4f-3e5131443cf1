import { doc, updateDoc, getDoc, setDoc, collection, query, where, getDocs, orderBy, limit } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';
import { db } from '../lib/firebase';
import { User, UserRole } from '../types';
import { RoleService } from './roleService';
import { AuthService } from './authService';

/**
 * User management service for admin operations
 * Handles user role assignments and user management
 */
export class UserManagementService {

  /**
   * Update a user's role (admin only)
   */
  static async updateUserRole(
    currentUser: User,
    targetUserId: string,
    newRole: UserRole
  ): Promise<void> {
    // Validate permission
    const validation = RoleService.validateRoleAssignment(currentUser, targetUserId, newRole);
    if (!validation.valid) {
      throw new Error(validation.message);
    }

    try {
      const userRef = doc(db, 'users', targetUserId);
      await updateDoc(userRef, {
        role: newRole,
        updatedAt: new Date()
      });
    } catch (error: any) {
      throw new Error(`Failed to update user role: ${error.message}`);
    }
  }

  /**
   * Get user by ID
   */
  static async getUserById(userId: string): Promise<User | null> {
    try {
      const userDoc = await getDoc(doc(db, 'users', userId));
      if (userDoc.exists()) {
        const rawData = userDoc.data();
        return {
          id: userDoc.id,
          email: rawData.email || 'no-email',
          displayName: rawData.displayName || rawData.email?.split('@')[0] || 'User',
          username: rawData.username || rawData.email?.split('@')[0] || 'user',
          role: rawData.role || 'listener',
          photoURL: rawData.photoURL,
          bio: rawData.bio,
          // Properly convert Firestore Timestamps to Date objects
          createdAt: rawData.createdAt?.toDate?.() || rawData.createdAt || new Date(),
          updatedAt: rawData.updatedAt?.toDate?.() || rawData.updatedAt || new Date()
        } as User;
      }
      return null;
    } catch (error: any) {
      throw new Error(`Failed to get user: ${error.message}`);
    }
  }

  /**
   * Get all users (admin only)
   */
  static async getAllUsers(currentUser: User): Promise<User[]> {
    if (!RoleService.isAdmin(currentUser)) {
      throw new Error('Only administrators can view all users');
    }

    try {
      // Use simple query without orderBy to avoid issues with missing createdAt fields
      const usersQuery = query(collection(db, 'users'));
      const querySnapshot = await getDocs(usersQuery);

      // Properly convert Firestore data to User objects with date conversion
      const users = querySnapshot.docs.map(doc => {
        const rawData = doc.data();
        return {
          id: doc.id,
          email: rawData.email || 'no-email',
          displayName: rawData.displayName || rawData.email?.split('@')[0] || 'User',
          username: rawData.username || rawData.email?.split('@')[0] || 'user',
          role: rawData.role || 'listener',
          photoURL: rawData.photoURL,
          bio: rawData.bio,
          // Properly convert Firestore Timestamps to Date objects
          createdAt: rawData.createdAt?.toDate?.() || rawData.createdAt || new Date(),
          updatedAt: rawData.updatedAt?.toDate?.() || rawData.updatedAt || new Date()
        } as User;
      });

      // Sort manually by createdAt if it exists, otherwise by email
      users.sort((a, b) => {
        if (a.createdAt && b.createdAt) {
          // Both are now guaranteed to be Date objects
          return b.createdAt.getTime() - a.createdAt.getTime();
        }
        if (a.createdAt && !b.createdAt) return -1;
        if (!a.createdAt && b.createdAt) return 1;
        return (a.email || '').localeCompare(b.email || '');
      });

      return users;
    } catch (error: any) {
      throw new Error(`Failed to get users: ${error.message}`);
    }
  }

  /**
   * Get users by role (admin only)
   */
  static async getUsersByRole(currentUser: User, role: UserRole): Promise<User[]> {
    if (!RoleService.isAdmin(currentUser)) {
      throw new Error('Only administrators can view users by role');
    }

    try {
      // Use simple query without orderBy to avoid issues with missing createdAt fields
      const usersQuery = query(
        collection(db, 'users'),
        where('role', '==', role)
      );
      const querySnapshot = await getDocs(usersQuery);

      // Properly convert Firestore data to User objects with date conversion
      const users = querySnapshot.docs.map(doc => {
        const rawData = doc.data();
        return {
          id: doc.id,
          email: rawData.email || 'no-email',
          displayName: rawData.displayName || rawData.email?.split('@')[0] || 'User',
          username: rawData.username || rawData.email?.split('@')[0] || 'user',
          role: rawData.role || 'listener',
          photoURL: rawData.photoURL,
          bio: rawData.bio,
          // Properly convert Firestore Timestamps to Date objects
          createdAt: rawData.createdAt?.toDate?.() || rawData.createdAt || new Date(),
          updatedAt: rawData.updatedAt?.toDate?.() || rawData.updatedAt || new Date()
        } as User;
      });

      // Sort manually by createdAt if it exists, otherwise by email
      users.sort((a, b) => {
        if (a.createdAt && b.createdAt) {
          // Both are now guaranteed to be Date objects
          return b.createdAt.getTime() - a.createdAt.getTime();
        }
        if (a.createdAt && !b.createdAt) return -1;
        if (!a.createdAt && b.createdAt) return 1;
        return (a.email || '').localeCompare(b.email || '');
      });

      return users;
    } catch (error: any) {
      throw new Error(`Failed to get users by role: ${error.message}`);
    }
  }

  /**
   * Get admin users
   */
  static async getAdminUsers(): Promise<User[]> {
    const requestId = `getAdminUsers-${Date.now()}-${Math.random()}`;
    try {
      console.log('🔧 [' + requestId + '] Querying for admin users...');

      // Use simple query without orderBy to avoid issues with missing createdAt fields
      const adminsQuery = query(
        collection(db, 'users'),
        where('role', '==', 'admin')
      );
      const querySnapshot = await getDocs(adminsQuery);

      // Properly convert Firestore data to User objects with date conversion
      const admins = querySnapshot.docs.map(doc => {
        const rawData = doc.data();
        return {
          id: doc.id,
          email: rawData.email || 'no-email',
          displayName: rawData.displayName || rawData.email?.split('@')[0] || 'User',
          username: rawData.username || rawData.email?.split('@')[0] || 'user',
          role: rawData.role || 'listener',
          photoURL: rawData.photoURL,
          bio: rawData.bio,
          // Properly convert Firestore Timestamps to Date objects
          createdAt: rawData.createdAt?.toDate?.() || rawData.createdAt || new Date(),
          updatedAt: rawData.updatedAt?.toDate?.() || rawData.updatedAt || new Date()
        } as User;
      });

      // Sort manually by createdAt if it exists, otherwise by email
      admins.sort((a, b) => {
        if (a.createdAt && b.createdAt) {
          // Both are now guaranteed to be Date objects
          return b.createdAt.getTime() - a.createdAt.getTime();
        }
        if (a.createdAt && !b.createdAt) return -1;
        if (!a.createdAt && b.createdAt) return 1;
        return (a.email || '').localeCompare(b.email || '');
      });

      console.log('🔧 [' + requestId + '] Query returned', admins.length, 'admin users:', admins.map(a => a.email || 'no-email'));
      return admins;
    } catch (error: any) {
      console.error('🔴 [' + requestId + '] Failed to query admin users:', error);
      throw new Error(`Failed to get admin users: ${error.message}`);
    }
  }

  /**
   * Search users by email or display name (admin only)
   */
  static async searchUsers(
    currentUser: User,
    searchTerm: string,
    maxResults: number = 20
  ): Promise<User[]> {
    if (!RoleService.isAdmin(currentUser)) {
      throw new Error('Only administrators can search users');
    }

    try {
      // Note: Firestore doesn't support full-text search natively
      // This is a basic implementation that searches by email prefix
      const usersQuery = query(
        collection(db, 'users'),
        where('email', '>=', searchTerm.toLowerCase()),
        where('email', '<=', searchTerm.toLowerCase() + '\uf8ff'),
        orderBy('email'),
        limit(maxResults)
      );
      const querySnapshot = await getDocs(usersQuery);
      return querySnapshot.docs.map(doc => doc.data() as User);
    } catch (error: any) {
      throw new Error(`Failed to search users: ${error.message}`);
    }
  }

  /**
   * Get user statistics (admin only)
   */
  static async getUserStatistics(currentUser: User): Promise<{
    total: number;
    admins: number;
    artists: number;
    listeners: number;
  }> {
    if (!RoleService.isAdmin(currentUser)) {
      throw new Error('Only administrators can view user statistics');
    }

    try {
      const [admins, artists, listeners] = await Promise.all([
        this.getUsersByRole(currentUser, 'admin'),
        this.getUsersByRole(currentUser, 'artist'),
        this.getUsersByRole(currentUser, 'listener')
      ]);

      return {
        total: admins.length + artists.length + listeners.length,
        admins: admins.length,
        artists: artists.length,
        listeners: listeners.length
      };
    } catch (error: any) {
      throw new Error(`Failed to get user statistics: ${error.message}`);
    }
  }

  /**
   * Promote user to admin (super admin only - for initial setup)
   */
  static async promoteToAdmin(
    currentUser: User,
    targetUserId: string
  ): Promise<void> {
    // Only existing admins can promote others
    if (!RoleService.isAdmin(currentUser)) {
      throw new Error('Only administrators can promote users to admin');
    }

    await this.updateUserRole(currentUser, targetUserId, 'admin');
  }

  /**
   * Demote admin to artist (admin only)
   */
  static async demoteToArtist(
    currentUser: User,
    targetUserId: string
  ): Promise<void> {
    // Prevent self-demotion
    if (currentUser.id === targetUserId) {
      throw new Error('Administrators cannot demote themselves');
    }

    await this.updateUserRole(currentUser, targetUserId, 'artist');
  }

  /**
   * Set user as listener (admin only)
   */
  static async setAsListener(
    currentUser: User,
    targetUserId: string
  ): Promise<void> {
    // Prevent self-demotion
    if (currentUser.id === targetUserId) {
      throw new Error('Administrators cannot demote themselves');
    }

    await this.updateUserRole(currentUser, targetUserId, 'listener');
  }

  /**
   * Check if there are any admin users in the system
   * Used for initial setup
   */
  static async hasAdminUsers(): Promise<boolean> {
    const requestId = `hasAdminUsers-${Date.now()}-${Math.random()}`;
    try {
      console.log('🔧 [' + requestId + '] Checking for admin users in the system...');
      const admins = await this.getAdminUsers();
      console.log('🔧 [' + requestId + '] Found', admins.length, 'admin users');
      const result = admins.length > 0;
      console.log('🔧 [' + requestId + '] Returning:', result);
      return result;
    } catch (error) {
      console.error('🔴 [' + requestId + '] Failed to check for admin users:', error);
      // Return false to allow setup if we can't check
      // This ensures the system can be initialized even if there are temporary issues
      console.log('🔧 [' + requestId + '] Returning false due to error');
      return false;
    }
  }

  /**
   * Initialize first admin user (for setup) - Direct REST API approach
   * This bypasses the problematic Firestore SDK
   */
  static async initializeFirstAdmin(userId: string): Promise<void> {
    try {
      console.log('🔧 Starting direct REST API admin initialization for user:', userId);

      // Get the current user's auth token
      const auth = getAuth();
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      const token = await currentUser.getIdToken();
      console.log('🔧 Got auth token, making direct REST API call');

      // Make direct REST API call to Firestore
      const projectId = 'probe-vibes';
      const url = `https://firestore.googleapis.com/v1/projects/${projectId}/databases/(default)/documents/users/${userId}`;

      const requestBody = {
        fields: {
          role: { stringValue: 'admin' },
          updatedAt: { timestampValue: new Date().toISOString() }
        }
      };

      console.log('🔧 Making REST API request to:', url);
      console.log('🔧 Request body:', JSON.stringify(requestBody, null, 2));

      const response = await fetch(url, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      console.log('🔧 REST API response status:', response.status);

      if (!response.ok) {
        const errorData = await response.text();
        console.error('🔴 REST API error response:', errorData);
        throw new Error(`REST API call failed: ${response.status} - ${errorData}`);
      }

      const responseData = await response.json();
      console.log('🔧 REST API success response:', responseData);
      console.log('🔧 Admin role successfully assigned using REST API');

      // Force refresh user data to update the local cache
      console.log('🔧 Refreshing user data to update local cache');
      await AuthService.refreshUserData();
      console.log('🔧 User data refreshed successfully');
    } catch (error: any) {
      console.error('🔴 Direct REST API admin initialization failed:', error);
      throw new Error(`Failed to initialize first admin: ${error.message}`);
    }
  }

  /**
   * Initialize first admin user (for setup) - Alternative method
   * Uses a different approach to avoid Firestore issues
   */
  static async initializeFirstAdminAlternative(userId: string): Promise<void> {
    try {
      console.log('🔧 Starting alternative admin initialization for user:', userId);

      const userRef = doc(db, 'users', userId);

      // Get current user data first
      const userDoc = await getDoc(userRef);
      if (!userDoc.exists()) {
        throw new Error('User document does not exist');
      }

      const currentData = userDoc.data();
      console.log('🔧 Current user data:', currentData);

      // Create new data with admin role
      const updatedData = {
        ...currentData,
        role: 'admin',
        updatedAt: new Date()
      };

      // Replace the entire document
      await setDoc(userRef, updatedData);

      console.log('🔧 Admin role successfully assigned using full document replacement');
    } catch (error: any) {
      console.error('🔴 Alternative admin initialization failed:', error);
      throw new Error(`Failed to initialize first admin (alternative): ${error.message}`);
    }
  }
}
