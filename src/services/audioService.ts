import { Howl, Howler } from 'howler';
import { Track } from '../types';

// <PERSON> to support crossOrigin for Web Audio API
const originalCreateAudio = (window as any).Audio;
(window as any).Audio = function(src?: string) {
  const audio = new originalCreateAudio(src);
  audio.crossOrigin = 'anonymous';
  return audio;
};

// Mobile Audio Context Manager for handling mobile browser audio policies
class MobileAudioContextManager {
  private static instance: MobileAudioContextManager;
  private audioContext: AudioContext | null = null;
  private isInitialized: boolean = false;
  private isMobile: boolean = false;

  constructor() {
    // Detect mobile devices
    this.isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                    window.innerWidth < 768;
  }

  static getInstance(): MobileAudioContextManager {
    if (!MobileAudioContextManager.instance) {
      MobileAudioContextManager.instance = new MobileAudioContextManager();
    }
    return MobileAudioContextManager.instance;
  }

  /**
   * Initialize audio context within user gesture (required for mobile)
   * Must be called synchronously within a user interaction event
   */
  initializeAudioContext(): Promise<boolean> {
    return new Promise((resolve) => {
      try {
        if (!this.isMobile) {
          // Desktop doesn't need special handling
          resolve(true);
          return;
        }

        if (this.isInitialized && this.audioContext?.state === 'running') {
          // Already initialized and running
          resolve(true);
          return;
        }

        // Create or resume audio context
        if (!this.audioContext) {
          this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
        }

        if (this.audioContext.state === 'suspended') {
          // Resume audio context (required for mobile browsers)
          console.log('🎵 Resuming suspended AudioContext for mobile...');
          this.audioContext.resume().then(() => {
            this.isInitialized = true;
            console.log('✅ Mobile AudioContext resumed successfully - audio should now work!');
            resolve(true);
          }).catch((error) => {
            console.error('❌ Failed to resume AudioContext:', error);
            resolve(false);
          });
        } else {
          this.isInitialized = true;
          console.log('✅ Mobile AudioContext already running - audio ready!');
          resolve(true);
        }
      } catch (error) {
        console.error('🎵 Failed to initialize AudioContext:', error);
        resolve(false);
      }
    });
  }

  /**
   * Check if audio context is ready for playback
   */
  isReady(): boolean {
    if (!this.isMobile) return true;
    return this.isInitialized && this.audioContext?.state === 'running';
  }

  /**
   * Get the audio context instance
   */
  getAudioContext(): AudioContext | null {
    return this.audioContext;
  }
}

export interface AudioServiceCallbacks {
  onPlay?: () => void;
  onPause?: () => void;
  onStop?: () => void;
  onEnd?: () => void;
  onLoad?: () => void;
  onLoadError?: (error: any) => void;
  onSeek?: () => void;
  onTimeUpdate?: (currentTime: number) => void;
}

class AudioServiceClass {
  private currentHowl: Howl | null = null;
  private callbacks: AudioServiceCallbacks = {};
  private timeUpdateInterval: NodeJS.Timeout | null = null;
  private isPlayRequested: boolean = false; // Prevent multiple play calls
  private isLoading: boolean = false; // Track loading state
  private mobileAudioManager: MobileAudioContextManager;

  constructor() {
    // Set global volume
    Howler.volume(0.8);

    // Configure Howler for HTML5 Audio to avoid CORS issues
    // Increase pool size for mobile devices that need more audio instances
    Howler.html5PoolSize = 20;

    // Initialize mobile audio context manager
    this.mobileAudioManager = MobileAudioContextManager.getInstance();
  }

  /**
   * Load and prepare a track for playback
   */
  loadTrack(track: Track, callbacks: AudioServiceCallbacks = {}): Promise<void> {
    return new Promise((resolve, reject) => {
      // Stop current track if playing
      this.stop();

      // Set loading state
      this.isLoading = true;
      this.callbacks = callbacks;

      // Validate URL
      if (!track.url) {
        const error = new Error('Track URL is missing');
        this.isLoading = false;
        this.callbacks.onLoadError?.(error);
        reject(error);
        return;
      }

      console.log('🎵 AudioService: Starting to load track:', track.title);

      // Create new Howl instance with CORS support
      this.currentHowl = new Howl({
        src: [track.url],
        html5: true, // Force HTML5 Audio
        preload: true, // Enable preload so track loads immediately
        volume: 1.0, // Use full volume, let global Howler volume control overall level
        format: ['mp3', 'mpeg'], // Explicitly specify formats
        xhrWithCredentials: false, // Disable credentials for CORS
        onload: () => {
          console.log('🎵 AudioService: Track loaded successfully:', track.title);
          this.isLoading = false;
          const trackDuration = this.currentHowl?.duration();
          this.callbacks.onLoad?.();
          resolve();
        },
        onloaderror: (id, error) => {
          console.error('🎵 AudioService: Failed to load track:', track.title, error);
          this.isLoading = false;
          this.callbacks.onLoadError?.(error);
          reject(error);
        },
        onplay: () => {
          console.log('🎵 AudioService: Playback started');
          this.isPlayRequested = false; // Reset flag when playback actually starts
          this.callbacks.onPlay?.();
          this.startTimeUpdate();
        },
        onpause: () => {
          console.log('🎵 AudioService: Playback paused');
          this.isPlayRequested = false; // Reset flag when paused
          this.callbacks.onPause?.();
          this.stopTimeUpdate();
        },
        onstop: () => {
          console.log('🎵 AudioService: Playback stopped');
          this.isPlayRequested = false; // Reset flag when stopped
          this.callbacks.onStop?.();
          this.stopTimeUpdate();
        },
        onend: () => {
          this.callbacks.onEnd?.();
          this.stopTimeUpdate();
        },
        onseek: () => {
          this.callbacks.onSeek?.();
        }
      });
    });
  }

  /**
   * Initialize audio context for mobile browsers (must be called within user gesture)
   */
  async initializeAudioContext(): Promise<boolean> {
    return await this.mobileAudioManager.initializeAudioContext();
  }

  /**
   * Check if audio context is ready for playback
   */
  isAudioContextReady(): boolean {
    return this.mobileAudioManager.isReady();
  }

  /**
   * Play the current track with mobile browser support
   */
  async playWithUserGesture(): Promise<boolean> {
    // Check if track is still loading
    if (this.isLoading) {
      console.warn('🎵 Track is still loading, cannot play yet');
      return false;
    }

    if (!this.currentHowl) {
      console.warn('🎵 No audio loaded, cannot play');
      return false;
    }

    // Prevent multiple play calls
    if (this.isPlayRequested || this.currentHowl.playing()) {
      console.log('🎵 Play already requested or audio already playing');
      return true;
    }

    try {
      // Initialize audio context first (required for mobile)
      const audioContextReady = await this.initializeAudioContext();
      if (!audioContextReady) {
        console.warn('🎵 AudioContext not ready, falling back to regular play');
      }

      console.log('🎵 AudioService: Starting playback with user gesture');
      this.isPlayRequested = true;

      this.currentHowl.play();
      return true;
    } catch (error) {
      console.error('🎵 Failed to start playback:', error);
      this.isPlayRequested = false;
      return false;
    }
  }

  /**
   * Play the current track with protection against multiple calls (legacy method)
   */
  play(): void {
    // Check if track is still loading
    if (this.isLoading) {
      console.warn('🎵 Track is still loading, cannot play yet');
      return;
    }

    if (!this.currentHowl) {
      console.warn('🎵 No audio loaded, cannot play');
      return;
    }

    // Prevent multiple play calls
    if (this.isPlayRequested || this.currentHowl.playing()) {
      console.log('🎵 Play already requested or audio already playing');
      return;
    }

    console.log('🎵 AudioService: Starting playback');
    this.isPlayRequested = true;

    try {
      this.currentHowl.play();
    } catch (error) {
      console.error('🎵 Failed to start playback:', error);
      this.isPlayRequested = false;
    }
  }

  /**
   * Pause the current track
   */
  pause(): void {
    if (this.currentHowl) {
      console.log('🎵 AudioService: Pausing playback');
      this.currentHowl.pause();
      this.isPlayRequested = false; // Reset play request flag
    }
  }

  /**
   * Stop the current track
   */
  stop(): void {
    if (this.currentHowl) {
      console.log('🎵 AudioService: Stopping playback');
      this.currentHowl.stop();
      this.currentHowl.unload();
      this.currentHowl = null;
    }
    this.isPlayRequested = false; // Reset play request flag
    this.isLoading = false; // Reset loading state
    this.stopTimeUpdate();
  }

  /**
   * Seek to a specific time (in seconds)
   */
  seek(time: number): void {
    if (this.currentHowl) {
      this.currentHowl.seek(time);
    }
  }

  /**
   * Get current playback time
   */
  getCurrentTime(): number {
    if (this.currentHowl) {
      return this.currentHowl.seek() as number || 0;
    }
    return 0;
  }

  /**
   * Get track duration
   */
  getDuration(): number {
    if (this.currentHowl) {
      return this.currentHowl.duration() || 0;
    }
    return 0;
  }

  /**
   * Check if currently playing
   */
  isPlaying(): boolean {
    if (this.currentHowl) {
      return this.currentHowl.playing();
    }
    return false;
  }

  /**
   * Check if audio is ready for playback (loaded and not loading)
   */
  isReadyForPlayback(): boolean {
    return !this.isLoading && this.currentHowl !== null && this.getDuration() > 0;
  }

  /**
   * Check if currently loading a track
   */
  isLoadingTrack(): boolean {
    return this.isLoading;
  }

  /**
   * Set volume (0-1)
   */
  setVolume(volume: number): void {
    const clampedVolume = Math.max(0, Math.min(1, volume));
    Howler.volume(clampedVolume);
  }

  /**
   * Get current volume
   */
  getVolume(): number {
    return Howler.volume();
  }

  /**
   * Start time update interval
   */
  private startTimeUpdate(): void {
    this.stopTimeUpdate();
    this.timeUpdateInterval = setInterval(() => {
      if (this.currentHowl && this.currentHowl.playing()) {
        const currentTime = this.getCurrentTime();
        this.callbacks.onTimeUpdate?.(currentTime);
      }
    }, 1000); // Update every second
  }

  /**
   * Stop time update interval
   */
  private stopTimeUpdate(): void {
    if (this.timeUpdateInterval) {
      clearInterval(this.timeUpdateInterval);
      this.timeUpdateInterval = null;
    }
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    this.stop();
    this.callbacks = {};
  }
}

// Export singleton instance
export const AudioService = new AudioServiceClass();

// Make AudioService available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).AudioService = AudioService;
}
