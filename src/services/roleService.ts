import { User, UserRole } from '../types';

/**
 * Role-based permission service for the Vibes platform
 * Manages user roles and permissions for different features
 */
export class RoleService {
  
  /**
   * Check if a user has permission to upload music
   */
  static canUploadMusic(user: User | null): boolean {
    if (!user) return false;
    return user.role === 'admin';
  }

  /**
   * Check if a user has admin privileges
   */
  static isAdmin(user: User | null): boolean {
    if (!user) return false;
    return user.role === 'admin';
  }

  /**
   * Check if a user is an artist (can upload music when feature is enabled)
   */
  static isArtist(user: User | null): boolean {
    if (!user) return false;
    return user.role === 'artist' || user.role === 'admin';
  }

  /**
   * Check if a user is a listener (basic user)
   */
  static isListener(user: User | null): boolean {
    if (!user) return false;
    return user.role === 'listener';
  }

  /**
   * Get the default role for new users
   */
  static getDefaultRole(): UserRole {
    return 'listener';
  }

  /**
   * Get all available roles
   */
  static getAllRoles(): UserRole[] {
    return ['admin', 'artist', 'listener'];
  }

  /**
   * Get role display name
   */
  static getRoleDisplayName(role: UserRole): string {
    const roleNames: Record<UserRole, string> = {
      admin: 'Administrator',
      artist: 'Artist',
      listener: 'Listener'
    };
    return roleNames[role];
  }

  /**
   * Get role description
   */
  static getRoleDescription(role: UserRole): string {
    const descriptions: Record<UserRole, string> = {
      admin: 'Full platform access including music upload and content management',
      artist: 'Can upload and manage music content (feature coming soon)',
      listener: 'Can discover, play, and interact with music content'
    };
    return descriptions[role];
  }

  /**
   * Check if a role can be assigned by the current user
   * Only admins can assign roles
   */
  static canAssignRole(currentUser: User | null, targetRole: UserRole): boolean {
    if (!this.isAdmin(currentUser)) return false;
    
    // Admins can assign any role
    return true;
  }

  /**
   * Get permissions for a role
   */
  static getRolePermissions(role: UserRole): string[] {
    const permissions: Record<UserRole, string[]> = {
      admin: [
        'upload_music',
        'manage_content',
        'manage_users',
        'view_analytics',
        'moderate_chat',
        'manage_featured_content'
      ],
      artist: [
        'upload_music_future', // Will be enabled later
        'manage_own_content',
        'view_own_analytics'
      ],
      listener: [
        'play_music',
        'create_playlists',
        'like_content',
        'follow_artists',
        'chat_participate'
      ]
    };
    return permissions[role] || [];
  }

  /**
   * Check if a user has a specific permission
   */
  static hasPermission(user: User | null, permission: string): boolean {
    if (!user) return false;
    const permissions = this.getRolePermissions(user.role);
    return permissions.includes(permission);
  }

  /**
   * Get upload access message for UI
   */
  static getUploadAccessMessage(user: User | null): string {
    if (!user) {
      return 'Please sign in to access upload features';
    }

    if (this.canUploadMusic(user)) {
      return 'You have upload access';
    }

    if (user.role === 'artist') {
      return 'Artist upload access coming soon';
    }

    return 'Upload access is currently limited to administrators';
  }

  /**
   * Validate role assignment
   */
  static validateRoleAssignment(currentUser: User | null, targetUserId: string, newRole: UserRole): {
    valid: boolean;
    message: string;
  } {
    if (!this.isAdmin(currentUser)) {
      return {
        valid: false,
        message: 'Only administrators can assign roles'
      };
    }

    if (currentUser.id === targetUserId && newRole !== 'admin') {
      return {
        valid: false,
        message: 'Administrators cannot remove their own admin privileges'
      };
    }

    return {
      valid: true,
      message: 'Role assignment is valid'
    };
  }
}
