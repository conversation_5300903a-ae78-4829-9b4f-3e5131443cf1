import {
  collection,
  addDoc,
  getDocs,
  doc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  getDoc,
  writeBatch,
  QuerySnapshot,
  DocumentData,
  getDocsFromCache,
  getDocsFromServer
} from 'firebase/firestore';
import { db, deduplicateQuery } from '../lib/firebase';
import { CarouselSlide } from '../types';

// Simple cache for carousel slides
let activeSlidesCache: CarouselSlide[] | null = null;
let cacheTimestamp = 0;
let activeSlidesCachePromise: Promise<CarouselSlide[]> | null = null;
let queryInProgress = false;
const CACHE_DURATION = 30000; // 30 seconds cache for better performance

export class CarouselService {
  // Get all active carousel slides ordered by display order (INSTANT!)
  static async getActiveSlides(): Promise<CarouselSlide[]> {
    const now = Date.now();

    // Return cached data immediately if available and fresh
    if (activeSlidesCache && (now - cacheTimestamp) < CACHE_DURATION) {
      console.log('🎠 Returning cached slides (instant)');
      return activeSlidesCache;
    }

    // Use global query deduplication to prevent Target ID conflicts
    return deduplicateQuery('carousel-active-slides', async () => {
      console.log('🎠 Fetching fresh slides from database');

      const result = await this.fetchActiveSlides();

      // Cache the result
      activeSlidesCache = result;
      cacheTimestamp = now;

      return result;
    });
  }

  // Internal method to actually fetch slides with optimized error handling
  private static async fetchActiveSlides(): Promise<CarouselSlide[]> {
    const startTime = performance.now();

    try {
      console.log('🎠 Fetching carousel slides...');

      // Use cache-first strategy to prevent Target ID conflicts in development
      // Try cache first, then server if cache is empty
      const collectionRef = collection(db, 'carouselSlides');
      let snapshot;

      try {
        // Use simple getDocs to avoid Target ID conflicts
        console.log('🎠 Fetching carousel data with simple query');
        snapshot = await getDocs(collectionRef);
      } catch (queryError: any) {
        console.warn('🎠 Simple query failed, trying alternative approach:', queryError.message);

        // If Target ID conflict, wait and retry with a fresh reference
        if (queryError.message.includes('Target ID')) {
          await new Promise(resolve => setTimeout(resolve, 500));
          console.log('🎠 Retrying with fresh collection reference');
          const freshCollectionRef = collection(db, 'carouselSlides');
          snapshot = await getDocs(freshCollectionRef);
        } else {
          throw queryError;
        }
      }

      if (snapshot.empty) {
        console.log('📊 No carousel slides found in database');
        return [];
      }

      const allSlides = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as CarouselSlide));

      // Filter and sort client-side to avoid complex query conflicts
      const activeSlides = allSlides
        .filter(slide => slide.isActive)
        .sort((a, b) => a.order - b.order);

      const loadTime = performance.now() - startTime;
      console.log(`✅ Successfully fetched ${activeSlides.length} active slides in ${loadTime.toFixed(2)}ms`);

      return activeSlides;

    } catch (error: any) {
      console.error('❌ Failed to fetch carousel slides:', error.message);

      // Clear cache on error to prevent stale data
      activeSlidesCachePromise = null;
      activeSlidesCache = null;
      queryInProgress = false;

      // Handle specific Firebase Target ID conflicts
      if (error.message.includes('Target ID') || error.code === 'failed-precondition') {
        console.warn('🎯 Firebase Target ID conflict detected - this is a known Firebase issue');
        console.warn('🎯 Error details:', {
          message: error.message,
          code: error.code,
          stack: error.stack?.split('\n')[0]
        });
        console.warn('🔄 Trying fallback approach...');

        // Wait a moment and try a different approach
        await new Promise(resolve => setTimeout(resolve, 300));

        try {
          // Fallback: Try with a fresh query reference
          console.log('🔄 Creating fallback query...');
          const fallbackQuery = query(
            collection(db, 'carouselSlides'),
            where('isActive', '==', true),
            orderBy('order', 'asc')
          );

          console.log('🔄 Executing fallback query...');
          const fallbackSnapshot = await getDocs(fallbackQuery);
          const fallbackSlides = fallbackSnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          } as CarouselSlide));

          console.log(`🔄 Fallback successful: ${fallbackSlides.length} slides loaded`);
          return fallbackSlides;

        } catch (fallbackError: any) {
          console.error('🚨 Fallback also failed:', fallbackError.message);
          console.error('🚨 Fallback error details:', {
            message: fallbackError.message,
            code: fallbackError.code
          });
        }
      }

      // Return empty array on any error to prevent app crashes
      console.warn('🚨 Returning empty slides array due to errors');
      return [];
    }
  }

  // Clear cache when slides are modified
  static clearCache(): void {
    console.log('🧹 Clearing carousel cache');
    activeSlidesCache = null;
    cacheTimestamp = 0;
  }

  // Get all carousel slides (for admin management)
  static async getAllSlides(): Promise<CarouselSlide[]> {
    try {
      const q = query(
        collection(db, 'carouselSlides'),
        orderBy('order', 'asc')
      );
      
      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as CarouselSlide));
    } catch (error: any) {
      console.error('Failed to fetch carousel slides:', error);
      throw new Error(`Failed to fetch carousel slides: ${error.message}`);
    }
  }

  // Create a new carousel slide
  static async createSlide(slideData: Omit<CarouselSlide, 'id' | 'createdAt' | 'updatedAt'>): Promise<CarouselSlide> {
    try {
      const docRef = await addDoc(collection(db, 'carouselSlides'), {
        ...slideData,
        createdAt: new Date(),
        updatedAt: new Date()
      });



      // Clear cache since slides have changed
      this.clearCache();

      return {
        id: docRef.id,
        ...slideData,
        createdAt: new Date(),
        updatedAt: new Date()
      };
    } catch (error: any) {
      console.error('Failed to create carousel slide:', error);
      throw new Error(`Failed to create carousel slide: ${error.message}`);
    }
  }

  // Update carousel slide
  static async updateSlide(slideId: string, updates: Partial<Omit<CarouselSlide, 'id' | 'createdAt' | 'createdBy'>>): Promise<void> {
    try {
      await updateDoc(doc(db, 'carouselSlides', slideId), {
        ...updates,
        updatedAt: new Date()
      });

      // Clear cache since slides have changed
      this.clearCache();
    } catch (error: any) {
      console.error('Failed to update carousel slide:', error);
      throw new Error(`Failed to update carousel slide: ${error.message}`);
    }
  }

  // Delete carousel slide
  static async deleteSlide(slideId: string): Promise<void> {
    try {
      await deleteDoc(doc(db, 'carouselSlides', slideId));

      // Clear cache since slides have changed
      this.clearCache();
    } catch (error: any) {
      console.error('Failed to delete carousel slide:', error);
      throw new Error(`Failed to delete carousel slide: ${error.message}`);
    }
  }

  // Reorder slides
  static async reorderSlides(slideIds: string[]): Promise<void> {
    try {
      const batch = writeBatch(db);

      slideIds.forEach((slideId, index) => {
        const slideRef = doc(db, 'carouselSlides', slideId);
        batch.update(slideRef, {
          order: index + 1,
          updatedAt: new Date()
        });
      });

      await batch.commit();

      // Clear cache since slides have changed
      this.clearCache();
    } catch (error: any) {
      console.error('Failed to reorder carousel slides:', error);
      throw new Error(`Failed to reorder carousel slides: ${error.message}`);
    }
  }

  // Toggle slide active status
  static async toggleSlideStatus(slideId: string, isActive: boolean): Promise<void> {
    try {
      await updateDoc(doc(db, 'carouselSlides', slideId), {
        isActive,
        updatedAt: new Date()
      });

      // Clear cache since slides have changed
      this.clearCache();
    } catch (error: any) {
      console.error('Failed to toggle slide status:', error);
      throw new Error(`Failed to toggle slide status: ${error.message}`);
    }
  }

  // Get slide by ID
  static async getSlideById(slideId: string): Promise<CarouselSlide | null> {
    try {
      const docSnap = await getDoc(doc(db, 'carouselSlides', slideId));
      
      if (docSnap.exists()) {
        return {
          id: docSnap.id,
          ...docSnap.data()
        } as CarouselSlide;
      }
      
      return null;
    } catch (error: any) {
      console.error('Failed to fetch carousel slide:', error);
      throw new Error(`Failed to fetch carousel slide: ${error.message}`);
    }
  }

  // Link slide to album
  static async linkSlideToAlbum(slideId: string, albumId: string, albumTitle: string, albumCoverUrl?: string): Promise<void> {
    try {
      await updateDoc(doc(db, 'carouselSlides', slideId), {
        albumId,
        albumTitle,
        albumCoverUrl: albumCoverUrl || null,
        updatedAt: new Date()
      });

      // Clear cache since slides have changed
      this.clearCache();
    } catch (error: any) {
      console.error('Failed to link slide to album:', error);
      throw new Error(`Failed to link slide to album: ${error.message}`);
    }
  }

  // Debug function to clear all slides (for development/debugging)
  static async clearAllSlides(): Promise<void> {
    try {
      const allSlides = await this.getAllSlides();
      const batch = writeBatch(db);

      allSlides.forEach(slide => {
        const slideRef = doc(db, 'carouselSlides', slide.id);
        batch.delete(slideRef);
      });

      await batch.commit();

      // Clear cache since all slides have been deleted
      this.clearCache();

      console.log(`🗑️ Cleared ${allSlides.length} carousel slides from database`);
    } catch (error: any) {
      console.error('Failed to clear carousel slides:', error);
      throw new Error(`Failed to clear carousel slides: ${error.message}`);
    }
  }

  // Debug function to list all slides with details
  static async debugListAllSlides(): Promise<void> {
    try {
      const allSlides = await this.getAllSlides();
      console.log('🎠 DEBUG: All carousel slides in database:');
      console.table(allSlides.map(slide => ({
        id: slide.id,
        title: slide.title,
        isActive: slide.isActive,
        order: slide.order,
        createdBy: slide.createdBy,
        createdAt: slide.createdAt
      })));
    } catch (error: any) {
      console.error('Failed to debug list slides:', error);
    }
  }

  // Unlink slide from album
  static async unlinkSlideFromAlbum(slideId: string): Promise<void> {
    try {
      await updateDoc(doc(db, 'carouselSlides', slideId), {
        albumId: null,
        albumTitle: null,
        albumCoverUrl: null,
        updatedAt: new Date()
      });

      // Clear cache since slides have changed
      this.clearCache();
    } catch (error: any) {
      console.error('Failed to unlink slide from album:', error);
      throw new Error(`Failed to unlink slide from album: ${error.message}`);
    }
  }
}
