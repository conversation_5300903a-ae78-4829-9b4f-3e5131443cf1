import {
  collection,
  addDoc,
  getDocs,
  getDoc,
  doc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  writeBatch,
  increment,
  Timestamp
} from 'firebase/firestore';
import { db } from '../lib/firebase';
import { PlayEvent, LikeEvent, SaveEvent, TrendingMetrics } from '../types';

export class EngagementService {
  // Generate a session ID for anonymous users
  static getSessionId(): string {
    let sessionId = localStorage.getItem('vibes_session_id');
    if (!sessionId) {
      sessionId = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
      localStorage.setItem('vibes_session_id', sessionId);
    }
    return sessionId;
  }

  // Track a play event
  static async trackPlay(
    trackId: string,
    albumId?: string,
    userId?: string,
    duration: number = 0,
    completed: boolean = false,
    skipTime?: number
  ): Promise<void> {
    try {
      const sessionId = this.getSessionId();
      
      const playEvent: Omit<PlayEvent, 'id'> = {
        trackId,
        ...(albumId && { albumId }),
        ...(userId && { userId }),
        sessionId,
        playedAt: new Date(),
        duration,
        completed,
        ...(skipTime !== undefined && { skipTime })
      };

      // Add play event to collection
      console.log('🎯 Creating play event for track:', trackId, 'by user:', userId);
      const playEventDoc = await addDoc(collection(db, 'playEvents'), playEvent);
      console.log('✅ Play event created with ID:', playEventDoc.id);

      // Update track play count
      console.log('📈 Updating track play count for:', trackId);
      const trackRef = doc(db, 'tracks', trackId);
      await updateDoc(trackRef, {
        playCount: increment(1),
        updatedAt: new Date()
      });
      console.log('✅ Track play count updated');

      // Update album play count if applicable
      if (albumId) {
        console.log('📈 Updating album play count for:', albumId);
        const albumRef = doc(db, 'albums', albumId);
        await updateDoc(albumRef, {
          playCount: increment(1),
          updatedAt: new Date()
        });
        console.log('✅ Album play count updated');
      }

      console.log('🎉 Play event tracked successfully');
    } catch (error: any) {
      console.error('Failed to track play event:', error);
      // Don't throw error to avoid disrupting user experience
    }
  }

  // Like a track or album
  static async likeItem(
    itemId: string,
    type: 'track' | 'album',
    userId: string
  ): Promise<void> {
    try {
      // Check if already liked
      const existingLike = await this.getUserLike(itemId, type, userId);
      if (existingLike) {
        console.log(`💚 ${type} already liked by user`);
        return; // Silently return instead of throwing error
      }

      const likeEvent: Omit<LikeEvent, 'id'> = {
        [type === 'track' ? 'trackId' : 'albumId']: itemId,
        userId,
        likedAt: new Date(),
        type
      };

      // Add like event
      console.log(`💚 Creating like event for ${type}:`, itemId, 'by user:', userId);
      const likeEventDoc = await addDoc(collection(db, 'likeEvents'), likeEvent);
      console.log('✅ Like event created with ID:', likeEventDoc.id);

      // Update item like count
      console.log(`📈 Updating ${type} like count for:`, itemId);
      const itemRef = doc(db, type === 'track' ? 'tracks' : 'albums', itemId);
      await updateDoc(itemRef, {
        likeCount: increment(1),
        updatedAt: new Date()
      });
      console.log(`✅ ${type} like count updated`);

      console.log(`🎉 ${type} liked successfully`);
    } catch (error: any) {
      throw new Error(`Failed to like ${type}: ${error.message}`);
    }
  }

  // Unlike a track or album
  static async unlikeItem(
    itemId: string,
    type: 'track' | 'album',
    userId: string
  ): Promise<void> {
    try {
      // Find existing like
      const existingLike = await this.getUserLike(itemId, type, userId);
      if (!existingLike) {
        console.warn(`💔 ${type} not liked by user:`, { itemId, userId });
        return; // Silently return instead of throwing error
      }

      // Remove like event
      console.log(`💔 Removing like event for ${type}:`, itemId, 'by user:', userId);
      await deleteDoc(doc(db, 'likeEvents', existingLike.id));
      console.log('✅ Like event removed');

      // Update item like count
      console.log(`📉 Updating ${type} like count for:`, itemId);
      const itemRef = doc(db, type === 'track' ? 'tracks' : 'albums', itemId);
      await updateDoc(itemRef, {
        likeCount: increment(-1),
        updatedAt: new Date()
      });
      console.log(`✅ ${type} like count updated`);

      console.log(`🎉 ${type} unliked successfully`);
    } catch (error: any) {
      throw new Error(`Failed to unlike ${type}: ${error.message}`);
    }
  }

  // Save a track or album
  static async saveItem(
    itemId: string,
    type: 'track' | 'album',
    userId: string
  ): Promise<void> {
    try {
      // Check if already saved
      const existingSave = await this.getUserSave(itemId, type, userId);
      if (existingSave) {
        throw new Error('Item already saved');
      }

      const saveEvent: Omit<SaveEvent, 'id'> = {
        [type === 'track' ? 'trackId' : 'albumId']: itemId,
        userId,
        savedAt: new Date(),
        type
      };

      // Add save event
      await addDoc(collection(db, 'saveEvents'), saveEvent);

      // Update item save count
      const itemRef = doc(db, type === 'track' ? 'tracks' : 'albums', itemId);
      await updateDoc(itemRef, {
        saveCount: increment(1),
        updatedAt: new Date()
      });

      console.log(`${type} saved successfully`);
    } catch (error: any) {
      throw new Error(`Failed to save ${type}: ${error.message}`);
    }
  }

  // Unsave a track or album
  static async unsaveItem(
    itemId: string,
    type: 'track' | 'album',
    userId: string
  ): Promise<void> {
    try {
      // Find existing save
      const existingSave = await this.getUserSave(itemId, type, userId);
      if (!existingSave) {
        throw new Error('Item not saved');
      }

      // Remove save event
      await deleteDoc(doc(db, 'saveEvents', existingSave.id));

      // Update item save count
      const itemRef = doc(db, type === 'track' ? 'tracks' : 'albums', itemId);
      await updateDoc(itemRef, {
        saveCount: increment(-1),
        updatedAt: new Date()
      });

      console.log(`${type} unsaved successfully`);
    } catch (error: any) {
      throw new Error(`Failed to unsave ${type}: ${error.message}`);
    }
  }

  // Check if user has liked an item
  static async getUserLike(
    itemId: string,
    type: 'track' | 'album',
    userId: string
  ): Promise<LikeEvent | null> {
    try {
      const q = query(
        collection(db, 'likeEvents'),
        where(type === 'track' ? 'trackId' : 'albumId', '==', itemId),
        where('userId', '==', userId),
        where('type', '==', type),
        limit(1)
      );

      const querySnapshot = await getDocs(q);
      if (querySnapshot.empty) {
        return null;
      }

      const doc = querySnapshot.docs[0];
      return {
        id: doc.id,
        ...doc.data()
      } as LikeEvent;
    } catch (error: any) {
      console.error('Failed to check user like:', error);
      return null;
    }
  }

  // Check if user has saved an item
  static async getUserSave(
    itemId: string,
    type: 'track' | 'album',
    userId: string
  ): Promise<SaveEvent | null> {
    try {
      const q = query(
        collection(db, 'saveEvents'),
        where(type === 'track' ? 'trackId' : 'albumId', '==', itemId),
        where('userId', '==', userId),
        where('type', '==', type),
        limit(1)
      );

      const querySnapshot = await getDocs(q);
      if (querySnapshot.empty) {
        return null;
      }

      const doc = querySnapshot.docs[0];
      return {
        id: doc.id,
        ...doc.data()
      } as SaveEvent;
    } catch (error: any) {
      console.error('Failed to check user save:', error);
      return null;
    }
  }

  // Get user's liked tracks
  static async getUserLikedTracks(userId: string): Promise<string[]> {
    try {
      console.log('🔍 Fetching liked tracks for user:', userId);
      const q = query(
        collection(db, 'likeEvents'),
        where('userId', '==', userId),
        where('type', '==', 'track'),
        orderBy('likedAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const likedTrackIds = querySnapshot.docs.map(doc => doc.data().trackId);
      console.log('💚 Found', likedTrackIds.length, 'liked tracks:', likedTrackIds);
      return likedTrackIds;
    } catch (error: any) {
      console.error('Failed to get user liked tracks:', error);

      // If index is still building, try a simpler query
      if (error.message?.includes('index') || error.message?.includes('building')) {
        console.log('🔄 Index still building, trying fallback query...');
        try {
          const fallbackQuery = query(
            collection(db, 'likeEvents'),
            where('userId', '==', userId),
            where('type', '==', 'track')
            // No orderBy to avoid index requirement
          );
          const fallbackSnapshot = await getDocs(fallbackQuery);
          const tracks = fallbackSnapshot.docs.map(doc => doc.data().trackId);
          // Sort in JavaScript instead
          return tracks.reverse(); // Simple reverse for most recent first
        } catch (fallbackError) {
          console.error('Fallback query also failed:', fallbackError);
        }
      }

      return [];
    }
  }

  // Get user's saved tracks
  static async getUserSavedTracks(userId: string): Promise<string[]> {
    try {
      const q = query(
        collection(db, 'saveEvents'),
        where('userId', '==', userId),
        where('type', '==', 'track'),
        orderBy('savedAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => doc.data().trackId);
    } catch (error: any) {
      console.error('Failed to get user saved tracks:', error);
      return [];
    }
  }

  // Get user's recently played albums
  static async getUserRecentAlbums(userId: string, limitCount: number = 10): Promise<string[]> {
    try {
      // Fix: Remove inequality filter to avoid multiple range filter error
      // We'll filter out null albumIds in JavaScript instead
      const q = query(
        collection(db, 'playEvents'),
        where('userId', '==', userId),
        orderBy('playedAt', 'desc'),
        limit(limitCount * 5) // Get more to account for duplicates and null filtering
      );

      const querySnapshot = await getDocs(q);
      const albumIds: string[] = [];
      const seenAlbums = new Set<string>();

      // Filter out null albumIds and remove duplicates
      for (const doc of querySnapshot.docs) {
        const data = doc.data();
        // Only include events that have an albumId (not null/undefined)
        if (data.albumId && data.albumId !== null && !seenAlbums.has(data.albumId)) {
          seenAlbums.add(data.albumId);
          albumIds.push(data.albumId);
          if (albumIds.length >= limitCount) break;
        }
      }

      return albumIds;
    } catch (error: any) {
      console.error('Failed to get user recent albums:', error);
      return [];
    }
  }

  // Get user's saved albums
  static async getUserSavedAlbums(userId: string): Promise<string[]> {
    try {
      const q = query(
        collection(db, 'saveEvents'),
        where('userId', '==', userId),
        where('type', '==', 'album'),
        orderBy('savedAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => doc.data().albumId);
    } catch (error: any) {
      console.error('Failed to get user saved albums:', error);
      return [];
    }
  }

  // Debug function to test Firestore permissions
  static async testFirestorePermissions(userId: string): Promise<void> {
    try {
      console.log('🧪 Testing Firestore permissions for user:', userId);

      // Test creating a like event
      const testLikeEvent = {
        trackId: 'test-track-id',
        userId: userId,
        likedAt: new Date(),
        type: 'track' as const
      };

      console.log('🧪 Attempting to create test like event...');
      const docRef = await addDoc(collection(db, 'likeEvents'), testLikeEvent);
      console.log('✅ Test like event created successfully:', docRef.id);

      // Test deleting the like event
      console.log('🧪 Attempting to delete test like event...');
      await deleteDoc(doc(db, 'likeEvents', docRef.id));
      console.log('✅ Test like event deleted successfully');

      console.log('✅ All Firestore permission tests passed!');
    } catch (error) {
      console.error('❌ Firestore permission test failed:', error);
      throw error;
    }
  }
}

// Make debug function available globally
if (typeof window !== 'undefined') {
  (window as any).testFirestorePermissions = EngagementService.testFirestorePermissions;
}
