import {
  collection,
  addDoc,
  getDocs,
  doc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  getDoc,
  writeBatch,
  deleteField,
  getDocsFromServer
} from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { db, storage, deduplicateQuery } from '../lib/firebase';
import { Album, Track } from '../types';

export class AlbumService {
  // Cache management for fresh data after updates - use timestamp to avoid race conditions
  private static cacheInvalidatedAt = 0;

  // Clear cache and force next queries to fetch from server
  static clearCache(): void {
    console.log('🧹 Clearing AlbumService cache - forcing server fetch');
    this.cacheInvalidatedAt = Date.now();
  }

  // Check if cache was invalidated and should force server fetch
  private static shouldForceServerFetch(): boolean {
    // Force server fetch if cache was invalidated in the last 5 seconds
    const timeSinceInvalidation = Date.now() - this.cacheInvalidatedAt;
    return timeSinceInvalidation < 5000; // 5 second window
  }

  // Create a new album
  static async createAlbum(albumData: Omit<Album, 'id' | 'createdAt' | 'updatedAt' | 'trackCount' | 'totalDuration'>): Promise<Album> {
    try {
      const docRef = await addDoc(collection(db, 'albums'), {
        ...albumData,
        createdAt: new Date(),
        updatedAt: new Date(),
        trackCount: albumData.trackIds.length,
        totalDuration: 0 // Will be calculated when tracks are added
      });

      return {
        id: docRef.id,
        ...albumData,
        createdAt: new Date(),
        updatedAt: new Date(),
        trackCount: albumData.trackIds.length,
        totalDuration: 0
      };
    } catch (error: any) {
      throw new Error(`Failed to create album: ${error.message}`);
    }
  }

  // Upload album cover image
  static async uploadAlbumCover(file: File, userId: string, albumId: string): Promise<string> {
    try {
      const fileName = `albums/${userId}/${albumId}/cover_${Date.now()}_${file.name}`;
      const storageRef = ref(storage, fileName);

      const snapshot = await uploadBytes(storageRef, file);
      const downloadURL = await getDownloadURL(snapshot.ref);

      return downloadURL;
    } catch (error: any) {
      throw new Error(`Failed to upload album cover: ${error.message}`);
    }
  }

  // Add tracks to album and update album metadata
  static async addTracksToAlbum(albumId: string, trackIds: string[]): Promise<void> {
    try {
      const batch = writeBatch(db);
      
      // Get album document
      const albumRef = doc(db, 'albums', albumId);
      const albumDoc = await getDoc(albumRef);
      
      if (!albumDoc.exists()) {
        throw new Error('Album not found');
      }
      
      const albumData = albumDoc.data() as Album;
      const updatedTrackIds = [...albumData.trackIds, ...trackIds];
      
      // Calculate total duration by fetching all tracks
      let totalDuration = 0;
      for (const trackId of updatedTrackIds) {
        const trackDoc = await getDoc(doc(db, 'tracks', trackId));
        if (trackDoc.exists()) {
          const track = trackDoc.data() as Track;
          totalDuration += track.duration;
        }
      }
      
      // Update album
      batch.update(albumRef, {
        trackIds: updatedTrackIds,
        trackCount: updatedTrackIds.length,
        totalDuration,
        updatedAt: new Date()
      });
      
      // Update tracks to reference this album and inherit cover if needed
      for (let i = 0; i < trackIds.length; i++) {
        const trackRef = doc(db, 'tracks', trackIds[i]);
        const trackDoc = await getDoc(trackRef);

        if (trackDoc.exists()) {
          const trackData = trackDoc.data();
          const updateData: any = {
            albumId,
            albumTitle: albumData.title,
            trackNumber: albumData.trackIds.length + i + 1,
            updatedAt: new Date()
          };

          // If track doesn't have a cover but album does, inherit album cover
          if (!trackData.coverUrl && albumData.coverUrl) {
            updateData.coverUrl = albumData.coverUrl;
          }

          batch.update(trackRef, updateData);
        }
      }
      
      await batch.commit();
    } catch (error: any) {
      throw new Error(`Failed to add tracks to album: ${error.message}`);
    }
  }

  // Get albums by user
  static async getAlbumsByUser(userId: string, includeStatus?: 'draft' | 'published'): Promise<Album[]> {
    try {
      console.log('Fetching albums for user:', userId, 'with status:', includeStatus);

      // Use simple query with only uploadedBy filter to avoid index issues
      const q = query(
        collection(db, 'albums'),
        where('uploadedBy', '==', userId)
      );

      // Force server fetch if cache was recently invalidated
      const shouldForceServer = this.shouldForceServerFetch();
      const querySnapshot = shouldForceServer
        ? await getDocsFromServer(q)
        : await getDocs(q);

      if (shouldForceServer) {
        console.log('🔄 Fetched fresh album data from server after cache invalidation');
      }

      console.log('Raw albums fetched:', querySnapshot.docs.length);

      const albums = querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate ? data.createdAt.toDate() : data.createdAt,
          updatedAt: data.updatedAt?.toDate ? data.updatedAt.toDate() : data.updatedAt,
          publishedAt: data.publishedAt?.toDate ? data.publishedAt.toDate() : data.publishedAt
        } as Album;
      });

      // Apply status filter in memory if specified
      let filteredAlbums = albums;
      if (includeStatus) {
        filteredAlbums = albums.filter(album => album.status === includeStatus);
        console.log('Albums after status filter:', filteredAlbums.length);
      }

      // Sort in memory by createdAt descending (newest first)
      filteredAlbums.sort((a, b) => {
        const aTime = a.createdAt instanceof Date ? a.createdAt.getTime() : 0;
        const bTime = b.createdAt instanceof Date ? b.createdAt.getTime() : 0;
        return bTime - aTime;
      });

      console.log('Final albums count:', filteredAlbums.length);
      return filteredAlbums;
    } catch (error: any) {
      console.error('Error in getAlbumsByUser:', error);
      throw new Error(`Failed to fetch user albums: ${error.message}`);
    }
  }

  // Get album by ID with tracks (optimized for fast first track)
  static async getAlbumWithTracks(albumId: string): Promise<{ album: Album; tracks: Track[] } | null> {
    try {
      const albumDoc = await getDoc(doc(db, 'albums', albumId));

      if (!albumDoc.exists()) {
        return null;
      }

      const album = { id: albumDoc.id, ...albumDoc.data() } as Album;

      // Fetch tracks in parallel for better performance
      const trackPromises = album.trackIds.map(async (trackId) => {
        const trackDoc = await getDoc(doc(db, 'tracks', trackId));
        if (trackDoc.exists()) {
          return { id: trackDoc.id, ...trackDoc.data() } as Track;
        }
        return null;
      });

      const trackResults = await Promise.all(trackPromises);
      const tracks = trackResults.filter((track): track is Track => track !== null);

      // Sort tracks by track number
      tracks.sort((a, b) => (a.trackNumber || 0) - (b.trackNumber || 0));

      return { album, tracks };
    } catch (error: any) {
      throw new Error(`Failed to fetch album with tracks: ${error.message}`);
    }
  }

  // Get album with first track only (for instant playback)
  static async getAlbumWithFirstTrack(albumId: string): Promise<{ album: Album; firstTrack: Track | null; remainingTrackIds: string[] } | null> {
    try {
      const albumDoc = await getDoc(doc(db, 'albums', albumId));

      if (!albumDoc.exists()) {
        return null;
      }

      const album = { id: albumDoc.id, ...albumDoc.data() } as Album;

      if (album.trackIds.length === 0) {
        return { album, firstTrack: null, remainingTrackIds: [] };
      }

      // Get first track immediately
      const firstTrackId = album.trackIds[0];
      const firstTrackDoc = await getDoc(doc(db, 'tracks', firstTrackId));

      const firstTrack = firstTrackDoc.exists()
        ? { id: firstTrackDoc.id, ...firstTrackDoc.data() } as Track
        : null;

      const remainingTrackIds = album.trackIds.slice(1);

      return { album, firstTrack, remainingTrackIds };
    } catch (error: any) {
      throw new Error(`Failed to fetch album with first track: ${error.message}`);
    }
  }



  // Publish album (and all its tracks)
  static async publishAlbum(albumId: string): Promise<void> {
    try {
      console.log('Publishing album:', albumId);

      // Get album
      const albumDoc = await getDoc(doc(db, 'albums', albumId));
      if (!albumDoc.exists()) {
        throw new Error(`Album with ID ${albumId} not found`);
      }

      const album = albumDoc.data() as Album;
      console.log('Album data before publishing:', { id: albumId, status: album.status, trackCount: album.trackIds.length });

      const batch = writeBatch(db);
      const publishedAt = new Date();

      // Update album status
      batch.update(doc(db, 'albums', albumId), {
        status: 'published',
        publishedAt,
        updatedAt: new Date()
      });

      // Update all tracks in the album
      console.log('Publishing tracks in album:', album.trackIds);
      for (const trackId of album.trackIds) {
        batch.update(doc(db, 'tracks', trackId), {
          status: 'published',
          publishedAt,
          updatedAt: new Date()
        });
      }

      await batch.commit();
      console.log('Album and tracks published successfully:', albumId);

      // Verify the update was successful
      const updatedAlbumDoc = await getDoc(doc(db, 'albums', albumId));
      const updatedAlbumData = updatedAlbumDoc.data();
      console.log('Album data after publishing:', { id: albumId, status: updatedAlbumData?.status });

      if (updatedAlbumData?.status !== 'published') {
        throw new Error('Failed to update album status - status not changed');
      }

      // Clear cache to ensure fresh data on next fetch
      this.clearCache();
    } catch (error: any) {
      console.error('Failed to publish album:', error);
      throw new Error(`Failed to publish album: ${error.message}`);
    }
  }

  // Update album metadata
  static async updateAlbum(albumId: string, updates: Partial<Omit<Album, 'id' | 'createdAt' | 'trackIds'>>): Promise<void> {
    try {
      await updateDoc(doc(db, 'albums', albumId), {
        ...updates,
        updatedAt: new Date()
      });

      // Clear cache to ensure fresh data on next fetch
      this.clearCache();
    } catch (error: any) {
      throw new Error(`Failed to update album: ${error.message}`);
    }
  }

  // Update album cover
  static async updateAlbumCover(albumId: string, coverFile: File, userId: string): Promise<string> {
    try {
      // Upload new cover
      const coverUrl = await this.uploadAlbumCover(coverFile, userId, albumId);

      // Update album document with new cover URL
      await updateDoc(doc(db, 'albums', albumId), {
        coverUrl,
        updatedAt: new Date()
      });

      // Clear cache to ensure fresh data on next fetch
      this.clearCache();

      return coverUrl;
    } catch (error: any) {
      throw new Error(`Failed to update album cover: ${error.message}`);
    }
  }

  // Remove album cover
  static async removeAlbumCover(albumId: string): Promise<void> {
    try {
      await updateDoc(doc(db, 'albums', albumId), {
        coverUrl: deleteField(),
        updatedAt: new Date()
      });

      // Clear cache to ensure fresh data on next fetch
      this.clearCache();
    } catch (error: any) {
      throw new Error(`Failed to remove album cover: ${error.message}`);
    }
  }

  // Delete album and optionally its tracks
  static async deleteAlbum(albumId: string, deleteTracks: boolean = false): Promise<void> {
    try {
      // Check if we're online
      if (!navigator.onLine) {
        throw new Error('Cannot delete album while offline. Please check your internet connection and try again.');
      }

      const batch = writeBatch(db);

      if (deleteTracks) {
        // Get album to find track IDs
        try {
          const albumDoc = await getDoc(doc(db, 'albums', albumId));
          if (albumDoc.exists()) {
            const album = albumDoc.data() as Album;

            // Delete all tracks
            for (const trackId of album.trackIds) {
              batch.delete(doc(db, 'tracks', trackId));
            }
          }
        } catch (docError: any) {
          // If we can't get the album document, still try to delete the album itself
          console.warn('Could not fetch album tracks for deletion:', docError.message);
          if (docError.code === 'unavailable' || docError.message.includes('offline')) {
            throw new Error('Cannot delete album while offline. Please check your internet connection and try again.');
          }
        }
      } else {
        // Just remove album reference from tracks
        try {
          const albumDoc = await getDoc(doc(db, 'albums', albumId));
          if (albumDoc.exists()) {
            const album = albumDoc.data() as Album;

            for (const trackId of album.trackIds) {
              // Remove album references by deleting the fields entirely
              // Using deleteField() to properly remove optional fields
              batch.update(doc(db, 'tracks', trackId), {
                albumId: deleteField(),
                albumTitle: deleteField(),
                trackNumber: deleteField(),
                updatedAt: new Date()
              });
            }
          }
        } catch (docError: any) {
          // If we can't get the album document, still try to delete the album itself
          console.warn('Could not fetch album tracks for reference removal:', docError.message);
          if (docError.code === 'unavailable' || docError.message.includes('offline')) {
            throw new Error('Cannot delete album while offline. Please check your internet connection and try again.');
          }
        }
      }

      // Delete album
      batch.delete(doc(db, 'albums', albumId));

      await batch.commit();

      // Clear cache to ensure fresh data on next fetch
      this.clearCache();
    } catch (error: any) {
      // Provide more specific error messages
      if (error.code === 'unavailable' || error.message.includes('offline')) {
        throw new Error('Cannot delete album while offline. Please check your internet connection and try again.');
      }
      if (error.code === 'permission-denied') {
        throw new Error('You do not have permission to delete this album.');
      }
      if (error.code === 'not-found') {
        throw new Error('Album not found. It may have already been deleted.');
      }

      throw new Error(`Failed to delete album: ${error.message}`);
    }
  }

  // Get published albums (for discovery)
  static async getPublishedAlbums(limitCount: number = 20): Promise<Album[]> {
    try {
      const q = query(
        collection(db, 'albums'),
        where('status', '==', 'published'),
        orderBy('publishedAt', 'desc'),
        limit(limitCount)
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Album));
    } catch (error: any) {
      throw new Error(`Failed to fetch published albums: ${error.message}`);
    }
  }



  // Get remaining tracks (for background loading) with offline handling
  static async getRemainingTracks(trackIds: string[]): Promise<Track[]> {
    // Use query deduplication to prevent Target ID conflicts
    const queryKey = `getRemainingTracks-${trackIds.join(',')}`;

    return deduplicateQuery(queryKey, async () => {
      try {
        if (trackIds.length === 0) return [];

        // Check if we're online before attempting to fetch
        if (!navigator.onLine) {
          console.warn('🌐 Device is offline, cannot fetch remaining tracks');
          return [];
        }

        // Fetch remaining tracks in parallel with proper error handling
        const trackPromises = trackIds.map(async (trackId) => {
          try {
            const trackDoc = await getDoc(doc(db, 'tracks', trackId));
            if (trackDoc.exists()) {
              return { id: trackDoc.id, ...trackDoc.data() } as Track;
            }
            return null;
          } catch (trackError: any) {
            // Handle individual track fetch errors gracefully
            if (trackError.code === 'unavailable' || trackError.message.includes('offline')) {
              console.warn(`🌐 Track ${trackId} unavailable (offline), skipping`);
              return null;
            }
            console.warn(`⚠️ Failed to fetch track ${trackId}:`, trackError.message);
            return null;
          }
        });

        const trackResults = await Promise.all(trackPromises);
        const tracks = trackResults.filter((track): track is Track => track !== null);

        // Sort by track number (accounting for the first track being index 0)
        tracks.sort((a, b) => (a.trackNumber || 0) - (b.trackNumber || 0));

        console.log(`✅ Successfully fetched ${tracks.length}/${trackIds.length} remaining tracks`);
        return tracks;
      } catch (error: any) {
        // Handle offline errors gracefully - don't throw, just return empty array
        if (error.code === 'unavailable' || error.message.includes('offline')) {
          console.warn('🌐 Cannot fetch remaining tracks - client is offline');
          return [];
        }

        // For other errors, log but don't crash the app
        console.error('❌ Failed to fetch remaining tracks:', error.message);
        return [];
      }
    });
  }


}
