import {
  collection,
  addDoc,
  query,
  orderBy,
  limit,
  onSnapshot,
  Timestamp,
  startAfter,
  QueryDocumentSnapshot,
  DocumentData,
  where,
  serverTimestamp,
  doc,
  setDoc,
  deleteDoc
} from 'firebase/firestore';
import { db } from '../lib/firebase';
import { ChatMessage } from '../types';

const MESSAGES_COLLECTION = 'globalChat'; // DEPRECATED - keeping for migration
const TRACK_CHATS_COLLECTION = 'trackChats'; // NEW - track-based chats
const ONLINE_USERS_COLLECTION = 'onlineUsers';
const MESSAGES_LIMIT = 15; // Further reduced for faster cold start
const CACHE_TIMEOUT = 3000; // 3 seconds timeout for cache-first strategy
const PRESENCE_TIMEOUT = 30000; // 30 seconds before user is considered offline

export class ChatService {
  private unsubscribe: (() => void) | null = null;
  private presenceUnsubscribe: (() => void) | null = null;
  private presenceInterval: NodeJS.Timeout | null = null;

  /**
   * Send a new message to the global chat (DEPRECATED)
   */
  async sendMessage(
    content: string,
    userId: string,
    userName: string,
    userAvatar?: string,
    trackId?: string
  ): Promise<void> {
    try {
      if (!content.trim()) {
        throw new Error('Message content cannot be empty');
      }

      const messageData = {
        content: content.trim(),
        userId,
        userName,
        userAvatar: userAvatar || null,
        timestamp: serverTimestamp(),
        trackId: trackId || null,
        reactions: {}
      };

      await addDoc(collection(db, MESSAGES_COLLECTION), messageData);
    } catch (error) {
      console.error('Error sending message:', error);
      throw new Error('Failed to send message. Please try again.');
    }
  }

  /**
   * Send a new message to a track-specific chat
   */
  async sendTrackMessage(
    trackId: string,
    content: string,
    userId: string,
    userName: string,
    userAvatar?: string
  ): Promise<void> {
    try {
      if (!content.trim()) {
        throw new Error('Message content cannot be empty');
      }

      if (!trackId) {
        throw new Error('Track ID is required for track messages');
      }

      const messageData = {
        content: content.trim(),
        userId,
        userName,
        userAvatar: userAvatar || null,
        timestamp: serverTimestamp(),
        trackId,
        reactions: {}
      };

      // Send to track-specific collection: trackChats/{trackId}/messages
      const trackChatRef = collection(db, TRACK_CHATS_COLLECTION, trackId, 'messages');
      await addDoc(trackChatRef, messageData);
    } catch (error) {
      console.error('Error sending track message:', error);
      throw new Error('Failed to send message. Please try again.');
    }
  }

  /**
   * Subscribe to real-time messages with instant cache-first loading (DEPRECATED)
   */
  subscribeToMessages(
    onMessagesUpdate: (messages: ChatMessage[]) => void,
    onError: (error: string) => void
  ): () => void {
    console.log('🚀 ChatService: Starting message subscription...');
    console.log('🌐 ChatService: Network status:', navigator.onLine ? 'ONLINE' : 'OFFLINE');
    const startTime = performance.now();

    // Check network connectivity
    if (!navigator.onLine) {
      console.warn('⚠️ ChatService: Device appears to be offline');
      onError('No internet connection. Please check your network.');
      return () => {};
    }

    try {
      // Unsubscribe from any existing subscription
      if (this.unsubscribe) {
        console.log('🔄 ChatService: Cleaning up existing subscription');
        this.unsubscribe();
      }

      console.log('📊 ChatService: Creating Firestore query...');
      const messagesQuery = query(
        collection(db, MESSAGES_COLLECTION),
        orderBy('timestamp', 'desc'),
        limit(MESSAGES_LIMIT)
      );

      console.log('👂 ChatService: Setting up onSnapshot listener with cache-first strategy...');

      // Track if we've received any data yet
      let hasReceivedData = false;

      // Set up timeout for cache-first strategy
      const cacheTimeout = setTimeout(() => {
        if (!hasReceivedData) {
          console.log('⏰ ChatService: Cache timeout - showing empty state for instant UI');
          onMessagesUpdate([]); // Show empty chat immediately
          hasReceivedData = true;
        }
      }, 100); // Very short timeout for instant UI

      // Firebase onSnapshot with cache-first strategy for instant loading
      this.unsubscribe = onSnapshot(
        messagesQuery,
        {
          // Include metadata to detect cache vs server data
          includeMetadataChanges: true,
          // Try cache first, then server - this should eliminate cold start delay
          source: 'default' // Uses cache when available, falls back to server
        },
        (snapshot) => {
          const callbackTime = performance.now();
          const elapsed = callbackTime - startTime;

          console.log(`⚡ ChatService: onSnapshot callback fired in ${elapsed.toFixed(2)}ms`);
          console.log(`📦 ChatService: Received ${snapshot.docs.length} documents`);
          console.log(`💾 ChatService: From cache: ${snapshot.metadata.fromCache}`);
          console.log(`🔄 ChatService: Has pending writes: ${snapshot.metadata.hasPendingWrites}`);

          // Clear timeout since we got data
          clearTimeout(cacheTimeout);
          hasReceivedData = true;

          const messages: ChatMessage[] = [];

          snapshot.forEach((doc) => {
            const data = doc.data();

            // Convert Firestore timestamp to Date
            const timestamp = data.timestamp?.toDate() || new Date();

            messages.push({
              id: doc.id,
              content: data.content,
              userId: data.userId,
              userName: data.userName,
              userAvatar: data.userAvatar,
              timestamp,
              trackId: data.trackId,
              reactions: data.reactions || {}
            });
          });

          console.log(`✅ ChatService: Processed ${messages.length} messages, calling update callback`);
          // Firebase provides immediate callback - no loading delay needed
          onMessagesUpdate(messages);
        },
        (error) => {
          const errorTime = performance.now();
          const elapsed = errorTime - startTime;
          console.error(`🔴 ChatService: Error after ${elapsed.toFixed(2)}ms:`, error);

          // Clear timeout on error
          clearTimeout(cacheTimeout);
          hasReceivedData = true;

          onError('Failed to load messages. Please refresh the page.');
        }
      );

      console.log('✅ ChatService: onSnapshot listener set up successfully');

      // Return cleanup function that also clears timeout
      return () => {
        clearTimeout(cacheTimeout);
        if (this.unsubscribe) {
          this.unsubscribe();
        }
      };
    } catch (error) {
      const errorTime = performance.now();
      const elapsed = errorTime - startTime;
      console.error(`🔴 ChatService: Setup error after ${elapsed.toFixed(2)}ms:`, error);
      onError('Failed to connect to chat. Please refresh the page.');
      return () => {};
    }
  }

  /**
   * Subscribe to real-time track-specific messages
   */
  subscribeToTrackMessages(
    trackId: string,
    onMessagesUpdate: (messages: ChatMessage[]) => void,
    onError: (error: string) => void
  ): () => void {
    console.log('🎵 ChatService: Starting track message subscription for:', trackId);
    const startTime = performance.now();

    // Check network connectivity
    if (!navigator.onLine) {
      console.warn('⚠️ ChatService: Device appears to be offline');
      onError('No internet connection. Please check your network.');
      return () => {};
    }

    try {
      let hasReceivedData = false;
      let cacheTimeout: NodeJS.Timeout;

      // Set up cache timeout for instant UI
      cacheTimeout = setTimeout(() => {
        if (!hasReceivedData) {
          console.log('⏰ ChatService: Cache timeout reached, showing empty state');
          onMessagesUpdate([]);
          hasReceivedData = true;
        }
      }, CACHE_TIMEOUT);

      // Query track-specific messages
      const trackMessagesQuery = query(
        collection(db, TRACK_CHATS_COLLECTION, trackId, 'messages'),
        orderBy('timestamp', 'desc'),
        limit(MESSAGES_LIMIT)
      );

      console.log('📞 ChatService: Setting up onSnapshot for track:', trackId);

      this.unsubscribe = onSnapshot(
        trackMessagesQuery,
        (snapshot) => {
          const callbackTime = performance.now();
          const elapsed = callbackTime - startTime;
          console.log(`🔥 ChatService: onSnapshot callback triggered after ${elapsed.toFixed(2)}ms for track ${trackId}`);

          // Clear timeout since we got data
          clearTimeout(cacheTimeout);
          hasReceivedData = true;

          const messages: ChatMessage[] = [];

          snapshot.forEach((doc) => {
            const data = doc.data();
            const timestamp = data.timestamp?.toDate() || new Date();

            messages.push({
              id: doc.id,
              content: data.content,
              userId: data.userId,
              userName: data.userName,
              userAvatar: data.userAvatar,
              timestamp,
              trackId: data.trackId,
              reactions: data.reactions || {}
            });
          });

          console.log(`✅ ChatService: Processed ${messages.length} track messages, calling update callback`);
          onMessagesUpdate(messages);
        },
        (error) => {
          const errorTime = performance.now();
          const elapsed = errorTime - startTime;
          console.error(`🔴 ChatService: Track messages error after ${elapsed.toFixed(2)}ms:`, error);

          clearTimeout(cacheTimeout);
          hasReceivedData = true;

          onError('Failed to load track messages. Please refresh the page.');
        }
      );

      console.log('✅ ChatService: Track message subscription set up successfully');

      return () => {
        clearTimeout(cacheTimeout);
        if (this.unsubscribe) {
          this.unsubscribe();
        }
      };
    } catch (error) {
      const errorTime = performance.now();
      const elapsed = errorTime - startTime;
      console.error(`🔴 ChatService: Track subscription setup error after ${elapsed.toFixed(2)}ms:`, error);
      onError('Failed to connect to track chat. Please refresh the page.');
      return () => {};
    }
  }

  /**
   * Load older messages for pagination
   */
  async loadOlderMessages(
    lastMessageTimestamp: Date,
    onMessagesUpdate: (messages: ChatMessage[], hasMore: boolean) => void,
    onError: (error: string) => void
  ): Promise<void> {
    try {
      const messagesQuery = query(
        collection(db, MESSAGES_COLLECTION),
        orderBy('timestamp', 'desc'),
        startAfter(Timestamp.fromDate(lastMessageTimestamp)),
        limit(MESSAGES_LIMIT)
      );

      // Use a one-time query for pagination
      const snapshot = await messagesQuery.get();
      const messages: ChatMessage[] = [];
      
      snapshot.forEach((doc) => {
        const data = doc.data();
        const timestamp = data.timestamp?.toDate() || new Date();
        
        messages.push({
          id: doc.id,
          content: data.content,
          userId: data.userId,
          userName: data.userName,
          userAvatar: data.userAvatar,
          timestamp,
          trackId: data.trackId,
          reactions: data.reactions || {}
        });
      });

      // Check if there are more messages
      const hasMore = messages.length === MESSAGES_LIMIT;
      
      onMessagesUpdate(messages, hasMore);
    } catch (error) {
      console.error('Error loading older messages:', error);
      onError('Failed to load older messages. Please try again.');
    }
  }

  /**
   * Add reaction to a message
   */
  async addReaction(messageId: string, emoji: string, userId: string): Promise<void> {
    try {
      // This will be implemented in Phase 2
      console.log('Reaction feature coming soon:', { messageId, emoji, userId });
    } catch (error) {
      console.error('Error adding reaction:', error);
      throw new Error('Failed to add reaction. Please try again.');
    }
  }

  /**
   * Remove reaction from a message
   */
  async removeReaction(messageId: string, emoji: string, userId: string): Promise<void> {
    try {
      // This will be implemented in Phase 2
      console.log('Reaction feature coming soon:', { messageId, emoji, userId });
    } catch (error) {
      console.error('Error removing reaction:', error);
      throw new Error('Failed to remove reaction. Please try again.');
    }
  }

  /**
   * Delete a message from track chat (only by the author)
   */
  async deleteTrackMessage(trackId: string, messageId: string): Promise<void> {
    try {
      const messageRef = doc(db, TRACK_CHATS_COLLECTION, trackId, 'messages', messageId);
      await deleteDoc(messageRef);
      console.log('✅ Track message deleted successfully');
    } catch (error) {
      console.error('Error deleting track message:', error);
      throw new Error('Failed to delete message. Please try again.');
    }
  }

  /**
   * Delete a message (legacy method - tries both collections)
   */
  async deleteMessage(messageId: string): Promise<void> {
    try {
      // Try deleting from global chat first (for backward compatibility)
      try {
        const globalMessageRef = doc(db, MESSAGES_COLLECTION, messageId);
        await globalMessageRef.delete();
        console.log('✅ Message deleted from global chat');
        return;
      } catch (globalError) {
        console.log('Message not found in global chat');
      }

      // For track messages, we need the track context
      throw new Error('Track message deletion requires track context. Please refresh and try again.');

    } catch (error) {
      console.error('Error deleting message:', error);
      throw new Error('Failed to delete message. Please try again.');
    }
  }

  /**
   * Track user presence for online count
   */
  async trackUserPresence(userId: string, userName: string): Promise<void> {
    try {
      const presenceRef = doc(db, ONLINE_USERS_COLLECTION, userId);

      // Update user presence
      await setDoc(presenceRef, {
        userId,
        userName,
        lastSeen: serverTimestamp(),
        isOnline: true
      }, { merge: true });

      // Set up periodic presence updates
      this.presenceInterval = setInterval(async () => {
        try {
          await setDoc(presenceRef, {
            lastSeen: serverTimestamp(),
            isOnline: true
          }, { merge: true });
        } catch (error) {
          console.error('Error updating presence:', error);
        }
      }, 15000); // Update every 15 seconds

    } catch (error) {
      console.error('Error tracking user presence:', error);
    }
  }

  /**
   * Subscribe to online users count
   */
  subscribeToOnlineUsers(
    onOnlineCountUpdate: (count: number) => void,
    onError: (error: string) => void
  ): () => void {
    try {
      const onlineUsersQuery = query(
        collection(db, ONLINE_USERS_COLLECTION),
        where('isOnline', '==', true),
        where('lastSeen', '>', new Date(Date.now() - PRESENCE_TIMEOUT))
      );

      this.presenceUnsubscribe = onSnapshot(
        onlineUsersQuery,
        (snapshot) => {
          const onlineCount = snapshot.docs.length;
          console.log(`👥 ChatService: ${onlineCount} users online`);
          onOnlineCountUpdate(onlineCount);
        },
        (error) => {
          console.error('Error subscribing to online users:', error);
          onError('Failed to load online users count.');
        }
      );

      return this.presenceUnsubscribe;
    } catch (error) {
      console.error('Error setting up online users subscription:', error);
      onError('Failed to connect to presence system.');
      return () => {};
    }
  }

  /**
   * Mark user as offline
   */
  async markUserOffline(userId: string): Promise<void> {
    try {
      const presenceRef = doc(db, ONLINE_USERS_COLLECTION, userId);
      await setDoc(presenceRef, {
        isOnline: false,
        lastSeen: serverTimestamp()
      }, { merge: true });
    } catch (error) {
      console.error('Error marking user offline:', error);
    }
  }

  /**
   * Disconnect from chat service
   */
  disconnect(): void {
    if (this.unsubscribe) {
      this.unsubscribe();
      this.unsubscribe = null;
    }

    if (this.presenceUnsubscribe) {
      this.presenceUnsubscribe();
      this.presenceUnsubscribe = null;
    }

    if (this.presenceInterval) {
      clearInterval(this.presenceInterval);
      this.presenceInterval = null;
    }
  }
}

// Export a singleton instance
export const chatService = new ChatService();
