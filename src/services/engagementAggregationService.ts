import {
  collection,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  doc,
  getDoc
} from 'firebase/firestore';
import { db } from '../lib/firebase';
import { Track, Album } from '../types';

export interface UserEngagementStats {
  totalPlays: number;
  totalLikes: number;
  totalSaves: number;
  totalTracksUploaded: number;
  totalAlbumsUploaded: number;
  mostPlayedTrack?: {
    track: Track;
    playCount: number;
  };
  mostLikedTrack?: {
    track: Track;
    likeCount: number;
  };
}

export interface PlatformEngagementStats {
  totalPlays: number;
  totalLikes: number;
  totalSaves: number;
  totalTracks: number;
  totalAlbums: number;
  totalUsers: number;
  topTracks: Array<{
    track: Track;
    playCount: number;
    likeCount: number;
  }>;
  recentActivity: Array<{
    type: 'play' | 'like' | 'save';
    trackTitle: string;
    artist: string;
    timestamp: Date;
  }>;
  // Time-based analytics
  periodStats?: {
    period: 'hour' | 'day' | 'week' | 'month';
    plays: number;
    likes: number;
    saves: number;
    startDate: Date;
    endDate: Date;
  };
}

export class EngagementAggregationService {
  // Get engagement stats for a specific user's uploaded content
  static async getUserEngagementStats(userId: string): Promise<UserEngagementStats> {
    try {
      // Get user's uploaded tracks
      const tracksQuery = query(
        collection(db, 'tracks'),
        where('uploadedBy', '==', userId)
      );
      const tracksSnapshot = await getDocs(tracksQuery);
      const userTracks = tracksSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Track));

      // Get user's uploaded albums
      const albumsQuery = query(
        collection(db, 'albums'),
        where('uploadedBy', '==', userId)
      );
      const albumsSnapshot = await getDocs(albumsQuery);
      const userAlbums = albumsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Album));

      // Calculate totals from tracks
      let totalPlays = 0;
      let totalLikes = 0;
      let totalSaves = 0;
      let mostPlayedTrack: { track: Track; playCount: number } | undefined;
      let mostLikedTrack: { track: Track; likeCount: number } | undefined;

      userTracks.forEach(track => {
        const playCount = track.playCount || 0;
        const likeCount = track.likeCount || 0;
        const saveCount = track.saveCount || 0;

        totalPlays += playCount;
        totalLikes += likeCount;
        totalSaves += saveCount;

        if (!mostPlayedTrack || playCount > mostPlayedTrack.playCount) {
          mostPlayedTrack = { track, playCount };
        }

        if (!mostLikedTrack || likeCount > mostLikedTrack.likeCount) {
          mostLikedTrack = { track, likeCount };
        }
      });

      // Add album engagement
      userAlbums.forEach(album => {
        totalPlays += album.playCount || 0;
        totalLikes += album.likeCount || 0;
        totalSaves += album.saveCount || 0;
      });

      return {
        totalPlays,
        totalLikes,
        totalSaves,
        totalTracksUploaded: userTracks.length,
        totalAlbumsUploaded: userAlbums.length,
        mostPlayedTrack,
        mostLikedTrack
      };
    } catch (error: any) {
      console.error('Failed to get user engagement stats:', error);
      throw new Error(`Failed to get user engagement stats: ${error.message}`);
    }
  }

  // Get platform-wide engagement statistics
  static async getPlatformEngagementStats(): Promise<PlatformEngagementStats> {
    try {
      // Get all published tracks
      const tracksQuery = query(
        collection(db, 'tracks'),
        where('status', '==', 'published')
      );
      const tracksSnapshot = await getDocs(tracksQuery);
      const allTracks = tracksSnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate ? data.createdAt.toDate() : data.createdAt,
          updatedAt: data.updatedAt?.toDate ? data.updatedAt.toDate() : data.updatedAt,
          publishedAt: data.publishedAt?.toDate ? data.publishedAt.toDate() : data.publishedAt,
          playCount: data.playCount || 0,
          likeCount: data.likeCount || 0,
          saveCount: data.saveCount || 0
        } as Track;
      });

      // Get all published albums
      const albumsQuery = query(
        collection(db, 'albums'),
        where('status', '==', 'published')
      );
      const albumsSnapshot = await getDocs(albumsQuery);
      const allAlbums = albumsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Album));

      // Calculate platform totals
      let totalPlays = 0;
      let totalLikes = 0;
      let totalSaves = 0;

      allTracks.forEach(track => {
        totalPlays += track.playCount || 0;
        totalLikes += track.likeCount || 0;
        totalSaves += track.saveCount || 0;
      });

      allAlbums.forEach(album => {
        totalPlays += album.playCount || 0;
        totalLikes += album.likeCount || 0;
        totalSaves += album.saveCount || 0;
      });

      // Get top tracks by engagement
      const topTracks = allTracks
        .map(track => ({
          track,
          playCount: track.playCount || 0,
          likeCount: track.likeCount || 0,
          engagementScore: (track.playCount || 0) + (track.likeCount || 0) * 3
        }))
        .sort((a, b) => b.engagementScore - a.engagementScore)
        .slice(0, 10)
        .map(({ track, playCount, likeCount }) => ({ track, playCount, likeCount }));

      // Get recent play events for activity feed (handle empty collection gracefully)
      let recentActivity: Array<{
        type: 'play' | 'like' | 'save';
        trackTitle: string;
        artist: string;
        timestamp: Date;
      }> = [];

      try {
        const recentPlaysQuery = query(
          collection(db, 'playEvents'),
          orderBy('playedAt', 'desc'),
          limit(20)
        );
        const recentPlaysSnapshot = await getDocs(recentPlaysQuery);

        const activityPromises = recentPlaysSnapshot.docs.map(async (playDoc) => {
          const playData = playDoc.data();
          try {
            const trackDoc = await getDoc(doc(db, 'tracks', playData.trackId));
            if (trackDoc.exists()) {
              const trackData = trackDoc.data();
              return {
                type: 'play' as const,
                trackTitle: trackData.title,
                artist: trackData.artist,
                timestamp: playData.playedAt?.toDate ? playData.playedAt.toDate() : new Date()
              };
            }
          } catch (error) {
            console.error('Error fetching track for activity:', error);
          }
          return null;
        });

        const activityResults = await Promise.all(activityPromises);
        recentActivity = activityResults.filter(Boolean) as Array<{
          type: 'play' | 'like' | 'save';
          trackTitle: string;
          artist: string;
          timestamp: Date;
        }>;
      } catch (error) {
        console.warn('No play events found or insufficient permissions, using empty activity feed:', error);
        recentActivity = [];
      }

      // Get user count (simplified - you might want to implement proper user counting)
      const usersQuery = query(collection(db, 'users'), limit(1000));
      const usersSnapshot = await getDocs(usersQuery);

      return {
        totalPlays,
        totalLikes,
        totalSaves,
        totalTracks: allTracks.length,
        totalAlbums: allAlbums.length,
        totalUsers: usersSnapshot.size,
        topTracks,
        recentActivity: recentActivity.filter(Boolean) as Array<{
          type: 'play' | 'like' | 'save';
          trackTitle: string;
          artist: string;
          timestamp: Date;
        }>
      };
    } catch (error: any) {
      console.error('Failed to get platform engagement stats:', error);
      throw new Error(`Failed to get platform engagement stats: ${error.message}`);
    }
  }

  // Get engagement stats for a specific track
  static async getTrackEngagementStats(trackId: string) {
    try {
      const trackDoc = await getDoc(doc(db, 'tracks', trackId));
      if (!trackDoc.exists()) {
        throw new Error('Track not found');
      }

      const trackData = trackDoc.data();
      return {
        playCount: trackData.playCount || 0,
        likeCount: trackData.likeCount || 0,
        saveCount: trackData.saveCount || 0
      };
    } catch (error: any) {
      console.error('Failed to get track engagement stats:', error);
      throw new Error(`Failed to get track engagement stats: ${error.message}`);
    }
  }

  // Get engagement stats for a specific album
  static async getAlbumEngagementStats(albumId: string) {
    try {
      const albumDoc = await getDoc(doc(db, 'albums', albumId));
      if (!albumDoc.exists()) {
        throw new Error('Album not found');
      }

      const albumData = albumDoc.data();
      return {
        playCount: albumData.playCount || 0,
        likeCount: albumData.likeCount || 0,
        saveCount: albumData.saveCount || 0
      };
    } catch (error: any) {
      console.error('Failed to get album engagement stats:', error);
      throw new Error(`Failed to get album engagement stats: ${error.message}`);
    }
  }

  // Get platform engagement stats for a specific time period
  static async getPlatformEngagementStatsForPeriod(
    period: 'hour' | 'day' | 'week' | 'month'
  ): Promise<PlatformEngagementStats> {
    try {
      const now = new Date();
      let startDate: Date;

      // Calculate start date based on period
      switch (period) {
        case 'hour':
          startDate = new Date(now.getTime() - (60 * 60 * 1000)); // 1 hour ago
          break;
        case 'day':
          startDate = new Date(now.getTime() - (24 * 60 * 60 * 1000)); // 1 day ago
          break;
        case 'week':
          startDate = new Date(now.getTime() - (7 * 24 * 60 * 60 * 1000)); // 1 week ago
          break;
        case 'month':
          startDate = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000)); // 30 days ago
          break;
        default:
          startDate = new Date(now.getTime() - (24 * 60 * 60 * 1000)); // Default to 1 day
      }

      // Get base platform stats
      const baseStats = await this.getPlatformEngagementStats();

      // Get period-specific events (handle empty collections gracefully)
      let periodPlaysSnapshot: any = { size: 0, docs: [] };
      let periodLikesSnapshot: any = { size: 0, docs: [] };
      let periodSavesSnapshot: any = { size: 0, docs: [] };

      try {
        const periodPlaysQuery = query(
          collection(db, 'playEvents'),
          where('playedAt', '>=', startDate),
          where('playedAt', '<=', now),
          orderBy('playedAt', 'desc')
        );
        periodPlaysSnapshot = await getDocs(periodPlaysQuery);
      } catch (error) {
        console.warn('No play events found for period, using empty data:', error);
      }

      try {
        const periodLikesQuery = query(
          collection(db, 'likeEvents'),
          where('likedAt', '>=', startDate),
          where('likedAt', '<=', now),
          orderBy('likedAt', 'desc')
        );
        periodLikesSnapshot = await getDocs(periodLikesQuery);
      } catch (error) {
        console.warn('No like events found for period, using empty data:', error);
      }

      try {
        const periodSavesQuery = query(
          collection(db, 'saveEvents'),
          where('savedAt', '>=', startDate),
          where('savedAt', '<=', now),
          orderBy('savedAt', 'desc')
        );
        periodSavesSnapshot = await getDocs(periodSavesQuery);
      } catch (error) {
        console.warn('No save events found for period, using empty data:', error);
      }

      // Calculate period stats
      const periodStats = {
        period,
        plays: periodPlaysSnapshot.size,
        likes: periodLikesSnapshot.size,
        saves: periodSavesSnapshot.size,
        startDate,
        endDate: now
      };

      // Get recent activity from the period (handle gracefully)
      let recentActivity: Array<{
        type: 'play' | 'like' | 'save';
        trackTitle: string;
        artist: string;
        timestamp: Date;
      }> = [];

      try {
        if (periodPlaysSnapshot.docs && periodPlaysSnapshot.docs.length > 0) {
          const activityPromises = periodPlaysSnapshot.docs.slice(0, 20).map(async (playDoc: any) => {
            const playData = playDoc.data();
            try {
              const trackDoc = await getDoc(doc(db, 'tracks', playData.trackId));
              if (trackDoc.exists()) {
                const trackData = trackDoc.data();
                return {
                  type: 'play' as const,
                  trackTitle: trackData.title,
                  artist: trackData.artist,
                  timestamp: playData.playedAt?.toDate ? playData.playedAt.toDate() : new Date()
                };
              }
            } catch (error) {
              console.error('Error fetching track for period activity:', error);
            }
            return null;
          });

          const activityResults = await Promise.all(activityPromises);
          recentActivity = activityResults.filter(Boolean) as Array<{
            type: 'play' | 'like' | 'save';
            trackTitle: string;
            artist: string;
            timestamp: Date;
          }>;
        }
      } catch (error) {
        console.warn('Error getting recent activity for period:', error);
        recentActivity = [];
      }

      return {
        ...baseStats,
        recentActivity: recentActivity.filter(Boolean) as Array<{
          type: 'play' | 'like' | 'save';
          trackTitle: string;
          artist: string;
          timestamp: Date;
        }>,
        periodStats
      };
    } catch (error: any) {
      console.error('Failed to get platform engagement stats for period:', error);
      throw new Error(`Failed to get platform engagement stats for period: ${error.message}`);
    }
  }

  // Get trending tracks for a specific period
  static async getTrendingTracksForPeriod(
    period: 'hour' | 'day' | 'week' | 'month',
    limitCount: number = 10
  ): Promise<Array<{ track: Track; playCount: number; likeCount: number; periodScore: number }>> {
    try {
      const now = new Date();
      let startDate: Date;

      switch (period) {
        case 'hour':
          startDate = new Date(now.getTime() - (60 * 60 * 1000));
          break;
        case 'day':
          startDate = new Date(now.getTime() - (24 * 60 * 60 * 1000));
          break;
        case 'week':
          startDate = new Date(now.getTime() - (7 * 24 * 60 * 60 * 1000));
          break;
        case 'month':
          startDate = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));
          break;
      }

      // Get all published tracks
      const tracksQuery = query(
        collection(db, 'tracks'),
        where('status', '==', 'published')
      );
      const tracksSnapshot = await getDocs(tracksQuery);
      const tracks = tracksSnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate ? data.createdAt.toDate() : data.createdAt,
          updatedAt: data.updatedAt?.toDate ? data.updatedAt.toDate() : data.updatedAt,
          publishedAt: data.publishedAt?.toDate ? data.publishedAt.toDate() : data.publishedAt,
          playCount: data.playCount || 0,
          likeCount: data.likeCount || 0,
          saveCount: data.saveCount || 0
        } as Track;
      });

      // Get period events grouped by track (handle empty collections gracefully)
      const periodPlayCounts: { [trackId: string]: number } = {};
      const periodLikeCounts: { [trackId: string]: number } = {};

      try {
        const periodPlaysQuery = query(
          collection(db, 'playEvents'),
          where('playedAt', '>=', startDate),
          where('playedAt', '<=', now)
        );
        const periodPlaysSnapshot = await getDocs(periodPlaysQuery);

        // Count plays per track in this period
        periodPlaysSnapshot.docs.forEach(doc => {
          const data = doc.data();
          periodPlayCounts[data.trackId] = (periodPlayCounts[data.trackId] || 0) + 1;
        });
      } catch (error) {
        console.warn('No play events found for trending period analysis:', error);
      }

      try {
        const periodLikesQuery = query(
          collection(db, 'likeEvents'),
          where('likedAt', '>=', startDate),
          where('likedAt', '<=', now),
          where('type', '==', 'track')
        );
        const periodLikesSnapshot = await getDocs(periodLikesQuery);

        // Count likes per track in this period
        periodLikesSnapshot.docs.forEach(doc => {
          const data = doc.data();
          if (data.trackId) {
            periodLikeCounts[data.trackId] = (periodLikeCounts[data.trackId] || 0) + 1;
          }
        });
      } catch (error) {
        console.warn('No like events found for trending period analysis:', error);
      }

      // Calculate period trending scores
      const trendingTracks = tracks
        .map(track => {
          const periodPlays = periodPlayCounts[track.id] || 0;
          const periodLikes = periodLikeCounts[track.id] || 0;
          const periodScore = periodPlays + (periodLikes * 3); // Weight likes more heavily

          return {
            track,
            playCount: track.playCount || 0,
            likeCount: track.likeCount || 0,
            periodScore
          };
        })
        .filter(item => item.periodScore > 0) // Only include tracks with activity in the period
        .sort((a, b) => b.periodScore - a.periodScore)
        .slice(0, limitCount);

      return trendingTracks;
    } catch (error: any) {
      console.error('Failed to get trending tracks for period:', error);
      throw new Error(`Failed to get trending tracks for period: ${error.message}`);
    }
  }
}
