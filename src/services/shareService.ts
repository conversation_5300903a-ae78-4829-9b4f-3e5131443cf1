import { Track, Album, Playlist } from '../types';
import { Toast } from '../utils/toast';

export interface ShareContent {
  title: string;
  text: string;
  url: string;
}

export class ShareService {
  private static getBaseUrl(): string {
    return window.location.origin;
  }

  /**
   * Generate share URL for a track
   */
  static generateTrackShareUrl(track: Track): string {
    const baseUrl = this.getBaseUrl();
    return `${baseUrl}/play?track=${track.id}`;
  }

  /**
   * Generate share URL for an album
   */
  static generateAlbumShareUrl(album: Album): string {
    const baseUrl = this.getBaseUrl();
    return `${baseUrl}/play?album=${album.id}`;
  }

  /**
   * Generate share URL for a playlist
   */
  static generatePlaylistShareUrl(playlist: Playlist): string {
    const baseUrl = this.getBaseUrl();
    return `${baseUrl}/play?playlist=${playlist.id}`;
  }

  /**
   * Generate share URL for liked songs
   */
  static generateLikedSongsShareUrl(): string {
    const baseUrl = this.getBaseUrl();
    return `${baseUrl}/play?liked=true`;
  }

  /**
   * Create share content for a track
   */
  static createTrackShareContent(track: Track): ShareContent {
    const url = this.generateTrackShareUrl(track);
    return {
      title: `${track.title} by ${track.artist}`,
      text: `Check out "${track.title}" by ${track.artist} on Vibes! 🎵`,
      url
    };
  }

  /**
   * Create share content for an album
   */
  static createAlbumShareContent(album: Album): ShareContent {
    const url = this.generateAlbumShareUrl(album);
    return {
      title: `${album.title} by ${album.artist}`,
      text: `Check out the album "${album.title}" by ${album.artist} on Vibes! 🎵`,
      url
    };
  }

  /**
   * Create share content for a playlist
   */
  static createPlaylistShareContent(playlist: Playlist): ShareContent {
    const url = this.generatePlaylistShareUrl(playlist);
    return {
      title: playlist.name,
      text: `Check out the playlist "${playlist.name}" on Vibes! 🎵`,
      url
    };
  }

  /**
   * Create share content for liked songs
   */
  static createLikedSongsShareContent(): ShareContent {
    const url = this.generateLikedSongsShareUrl();
    return {
      title: 'My Liked Songs',
      text: 'Check out my liked songs on Vibes! 🎵',
      url
    };
  }

  /**
   * Share content using Web Share API (native sharing)
   */
  static async shareContent(content: ShareContent): Promise<boolean> {
    try {
      // Try Web Share API first (mobile devices)
      if (navigator.share) {
        await navigator.share(content);
        return true;
      }

      // If Web Share API is not available, fall back to copy link
      return this.copyLinkToClipboard(content.url);
    } catch (error) {
      console.error('Failed to share content:', error);
      Toast.error('Failed to share content. Please try again.');
      return false;
    }
  }

  /**
   * Copy link directly to clipboard
   */
  static async copyLinkToClipboard(url: string): Promise<boolean> {
    try {
      // Try modern clipboard API first
      await navigator.clipboard.writeText(url);
      Toast.success('Link copied to clipboard!');
      return true;
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);

      // Fallback to manual copy method
      try {
        const textArea = document.createElement('textarea');
        textArea.value = url;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);

        Toast.success('Link copied to clipboard!');
        return true;
      } catch (fallbackError) {
        console.error('All copy methods failed:', fallbackError);
        Toast.error('Failed to copy link. Please try again.');
        return false;
      }
    }
  }

  /**
   * Share a track
   */
  static async shareTrack(track: Track): Promise<boolean> {
    const content = this.createTrackShareContent(track);
    return this.shareContent(content);
  }

  /**
   * Share an album
   */
  static async shareAlbum(album: Album): Promise<boolean> {
    const content = this.createAlbumShareContent(album);
    return this.shareContent(content);
  }

  /**
   * Share a playlist
   */
  static async sharePlaylist(playlist: Playlist): Promise<boolean> {
    const content = this.createPlaylistShareContent(playlist);
    return this.shareContent(content);
  }

  /**
   * Share liked songs
   */
  static async shareLikedSongs(): Promise<boolean> {
    const content = this.createLikedSongsShareContent();
    return this.shareContent(content);
  }

  // Copy Link Methods (direct clipboard copy)

  /**
   * Copy track link to clipboard
   */
  static async copyTrackLink(track: Track): Promise<boolean> {
    const url = this.generateTrackShareUrl(track);
    return this.copyLinkToClipboard(url);
  }

  /**
   * Copy album link to clipboard
   */
  static async copyAlbumLink(album: Album): Promise<boolean> {
    const url = this.generateAlbumShareUrl(album);
    return this.copyLinkToClipboard(url);
  }

  /**
   * Copy playlist link to clipboard
   */
  static async copyPlaylistLink(playlist: Playlist): Promise<boolean> {
    const url = this.generatePlaylistShareUrl(playlist);
    return this.copyLinkToClipboard(url);
  }

  /**
   * Copy liked songs link to clipboard
   */
  static async copyLikedSongsLink(): Promise<boolean> {
    const url = this.generateLikedSongsShareUrl();
    return this.copyLinkToClipboard(url);
  }
}
