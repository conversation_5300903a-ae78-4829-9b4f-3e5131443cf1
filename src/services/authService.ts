import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signInWithPopup,
  signInWithRedirect,
  getRedirectResult,
  signOut,
  onAuthStateChanged,
  User as FirebaseUser,
  updateP<PERSON>file,
  GoogleAuthProvider,
  OAuthProvider,
  TwitterAuthProvider
} from 'firebase/auth';
import { doc, setDoc, getDoc, enableNetwork, disableNetwork, serverTimestamp, Timestamp, getDocFromCache, getDocFromServer } from 'firebase/firestore';
import { auth, db, deduplicateQuery } from '../lib/firebase';
import { User } from '../types';
import { generateSmartUsername } from '../utils/usernameGenerator';
import { RoleService } from './roleService';

// Local storage keys for offline user data
const USER_STORAGE_KEY = 'vibes_user_data';
const PENDING_WRITES_KEY = 'vibes_pending_writes';

// Request tracking for Firestore operations
let currentFirestoreRequest: string | null = null;

export class AuthService {
  private static authInProgress = false; // Prevent multiple simultaneous auth attempts

  // Local storage helpers
  private static saveUserToLocal(user: User): void {
    try {
      // Validate user data before saving
      if (!user || !user.id || !user.email) {
        console.warn('🔴 Attempted to save invalid user data to local storage:', user);
        return;
      }

      localStorage.setItem(USER_STORAGE_KEY, JSON.stringify(user));
      console.log('✅ User data saved to local storage:', user.id);
    } catch (error) {
      console.warn('Failed to save user to local storage:', error);
    }
  }

  private static getUserFromLocal(): User | null {
    try {
      const userData = localStorage.getItem(USER_STORAGE_KEY);
      if (!userData) return null;

      const user = JSON.parse(userData);

      // Validate retrieved user data
      if (!user || !user.id || !user.email) {
        console.warn('🔴 Invalid user data in local storage, clearing:', user);
        this.clearUserFromLocal();
        return null;
      }

      console.log('✅ Valid user data retrieved from local storage:', user.id);
      return user;
    } catch (error) {
      console.warn('Failed to get user from local storage:', error);
      // Clear corrupted data
      this.clearUserFromLocal();
      return null;
    }
  }

  private static clearUserFromLocal(): void {
    try {
      localStorage.removeItem(USER_STORAGE_KEY);
      localStorage.removeItem(PENDING_WRITES_KEY); // Also clear pending writes
    } catch (error) {
      console.warn('Failed to clear user from local storage:', error);
    }
  }

  private static addPendingWrite(userData: User): void {
    try {
      const pending = JSON.parse(localStorage.getItem(PENDING_WRITES_KEY) || '[]');
      pending.push({ type: 'user', data: userData, timestamp: Date.now() });
      localStorage.setItem(PENDING_WRITES_KEY, JSON.stringify(pending));
    } catch (error) {
      console.warn('Failed to add pending write:', error);
    }
  }

  private static async processPendingWrites(): Promise<void> {
    try {
      // Check if user is authenticated before processing writes
      const currentUser = auth.currentUser;
      if (!currentUser) {
        console.log('No authenticated user, skipping pending writes');
        return;
      }

      const pending = JSON.parse(localStorage.getItem(PENDING_WRITES_KEY) || '[]');
      if (pending.length === 0) return;

      for (const write of pending) {
        if (write.type === 'user') {
          // Only process writes for the current authenticated user
          if (write.data.id !== currentUser.uid) {
            console.warn('Skipping pending write for different user');
            continue;
          }

          try {
            // Ensure dates are properly formatted for Firestore
            const userData = {
              ...write.data,
              createdAt: write.data.createdAt instanceof Date ? write.data.createdAt : new Date(write.data.createdAt),
              updatedAt: write.data.updatedAt instanceof Date ? write.data.updatedAt : new Date(write.data.updatedAt)
            };

            await setDoc(doc(db, 'users', write.data.id), userData);
            console.log('Successfully synced user data to Firestore');
          } catch (error: any) {
            console.warn('Failed to sync user data, will retry later:', {
              error: error.message,
              code: error.code,
              userData: write.data
            });
            return; // Stop processing if we can't sync
          }
        }
      }

      // Clear pending writes if all successful
      localStorage.removeItem(PENDING_WRITES_KEY);
    } catch (error) {
      console.warn('Failed to process pending writes:', error);
    }
  }
  static async signUp(email: string, password: string, displayName: string): Promise<User> {
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const firebaseUser = userCredential.user;

      // Update Firebase Auth profile
      await updateProfile(firebaseUser, { displayName });

      // Create user document in Firestore
      const now = new Date();
      const userData: User = {
        id: firebaseUser.uid,
        email: firebaseUser.email!,
        displayName,
        username: generateSmartUsername(displayName, firebaseUser.email),
        role: RoleService.getDefaultRole(),
        createdAt: now,
        updatedAt: now
      };

      await setDoc(doc(db, 'users', firebaseUser.uid), userData);

      return userData;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to create account');
    }
  }

  static async signIn(email: string, password: string): Promise<User> {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const firebaseUser = userCredential.user;

      // Get user data from Firestore
      try {
        const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));

        if (userDoc.exists()) {
          const rawData = userDoc.data();

          // Properly construct User object with all required fields
          const userData: User = {
            id: firebaseUser.uid,
            email: rawData?.email || firebaseUser.email!,
            displayName: rawData?.displayName || firebaseUser.displayName || firebaseUser.email?.split('@')[0] || 'User',
            username: rawData?.username || generateSmartUsername(firebaseUser.displayName, firebaseUser.email),
            role: rawData?.role || RoleService.getDefaultRole(),
            photoURL: rawData?.photoURL || firebaseUser.photoURL || undefined,
            createdAt: rawData?.createdAt?.toDate?.() || rawData?.createdAt || new Date(),
            updatedAt: rawData?.updatedAt?.toDate?.() || rawData?.updatedAt || new Date()
          };

          return userData;
        }
      } catch (firestoreError: any) {
        console.warn('Firestore connection issue during sign-in:', firestoreError.message);
      }

      // If Firestore is offline or user doesn't exist, create from Firebase Auth
      const now = new Date();
      const userData: User = {
        id: firebaseUser.uid,
        email: firebaseUser.email!,
        displayName: firebaseUser.displayName || firebaseUser.email?.split('@')[0] || 'User',
        username: generateSmartUsername(firebaseUser.displayName, firebaseUser.email),
        role: RoleService.getDefaultRole(),
        photoURL: firebaseUser.photoURL || undefined,
        createdAt: now,
        updatedAt: now
      };

      // Try to save to Firestore for future use
      try {
        await setDoc(doc(db, 'users', firebaseUser.uid), userData);
      } catch (saveError) {
        console.warn('Could not save user to Firestore during sign-in:', saveError);
      }

      return userData;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to sign in');
    }
  }

  static async signOut(): Promise<void> {
    try {
      await signOut(auth);
      this.clearUserFromLocal();
    } catch (error: any) {
      throw new Error(error.message || 'Failed to sign out');
    }
  }

  /**
   * Get user data from Firestore with query deduplication and Target ID conflict handling
   */
  static async getUserData(userId: string): Promise<User | null> {
    // Use query deduplication to prevent Target ID conflicts
    return deduplicateQuery(`getUserData-${userId}`, async () => {
      try {
        console.log('🔄 Fetching user data from Firestore for:', userId);

        // Use cache-first strategy to prevent Target ID conflicts
        const userDocRef = doc(db, 'users', userId);
        let userDoc;

        try {
          // Try cache first
          userDoc = await getDocFromCache(userDocRef);
          console.log('🔄 Using cached user data');
        } catch (cacheError) {
          // If cache fails, get from server
          console.log('🔄 Cache miss, fetching user from server');
          userDoc = await getDocFromServer(userDocRef);
        }

        if (userDoc.exists()) {
          const rawData = userDoc.data();
          console.log('🔄 Found user data with role:', rawData?.role);

          // Get Firebase user for fallback data
          const firebaseUser = auth.currentUser;

          // Properly construct User object with all required fields
          const userData: User = {
            id: userId,
            email: rawData?.email || firebaseUser?.email || 'no-email',
            displayName: rawData?.displayName || firebaseUser?.displayName || rawData?.email?.split('@')[0] || 'User',
            username: rawData?.username || generateSmartUsername(rawData?.displayName || firebaseUser?.displayName, rawData?.email || firebaseUser?.email),
            role: rawData?.role || RoleService.getDefaultRole(),
            photoURL: rawData?.photoURL || firebaseUser?.photoURL || undefined,
            createdAt: rawData?.createdAt?.toDate?.() || rawData?.createdAt || new Date(),
            updatedAt: rawData?.updatedAt?.toDate?.() || rawData?.updatedAt || new Date()
          };

          console.log('✅ Constructed complete user object from getUserData:', userData);
          return userData;
        } else {
          console.warn('🔴 User document not found in Firestore');
          return null;
        }
      } catch (error: any) {
        // Handle Target ID conflicts gracefully
        if (error.message?.includes('Target ID already exists')) {
          console.warn('🎯 Target ID conflict detected, retrying with delay...', error.message);
          // Wait a moment and retry once
          await new Promise(resolve => setTimeout(resolve, 500));
          try {
            const retryDoc = await getDocFromServer(doc(db, 'users', userId));
            if (retryDoc.exists()) {
              const rawData = retryDoc.data();
              const userData: User = {
                id: retryDoc.id,
                email: rawData.email,
                displayName: rawData.displayName,
                username: rawData.username,
                role: rawData.role || 'user',
                createdAt: rawData.createdAt?.toDate ? rawData.createdAt.toDate() : new Date(rawData.createdAt),
                updatedAt: rawData.updatedAt?.toDate ? rawData.updatedAt.toDate() : new Date(rawData.updatedAt),
                profilePicture: rawData.profilePicture || null,
                bio: rawData.bio || null,
                isOnline: rawData.isOnline || false,
                lastSeen: rawData.lastSeen?.toDate ? rawData.lastSeen.toDate() : null
              };
              console.log('✅ Retry successful after Target ID conflict');
              return userData;
            }
            return null;
          } catch (retryError) {
            console.error('🔴 Retry failed after Target ID conflict:', retryError);
            throw retryError;
          }
        }

        console.error('🔴 Failed to get user data from Firestore:', error);
        throw error;
      }
    });
  }

  /**
   * Force refresh user data from Firestore
   * Useful after role changes or other updates
   */
  static async refreshUserData(): Promise<User | null> {
    try {
      const firebaseUser = auth.currentUser;
      if (!firebaseUser) return null;

      console.log('🔄 Forcing user data refresh from Firestore');

      // Clear local storage to force fresh fetch
      this.clearUserFromLocal();

      // Get fresh data from Firestore
      const userData = await this.getUserData(firebaseUser.uid);
      if (userData) {
        console.log('🔄 Fresh user data retrieved with role:', userData.role);
        this.saveUserToLocal(userData);
        return userData;
      } else {
        console.warn('🔴 No user data found during refresh');
        return null;
      }
    } catch (error: any) {
      console.error('🔴 Failed to refresh user data:', error);
      throw error;
    }
  }

  static onAuthStateChange(callback: (user: User | null) => void): () => void {
    return onAuthStateChanged(auth, async (firebaseUser: FirebaseUser | null) => {
      if (firebaseUser) {
        console.log('🔍 Auth state change - Firebase user found:', firebaseUser.uid);

        // First, try to use local storage for instant loading
        const localUser = this.getUserFromLocal();
        if (localUser && localUser.id === firebaseUser.uid) {
          console.log('✅ Using cached user data for instant loading');
          callback(localUser);

          // Optionally sync with Firestore in background (non-blocking)
          this.syncUserDataInBackground(firebaseUser.uid, callback);
          return;
        }

        // If no local data, fetch from Firestore
        console.log('🔍 No local data, fetching from Firestore...');
        try {
          const freshUser = await this.getUserData(firebaseUser.uid);
          if (freshUser) {
            console.log('✅ Fresh user data retrieved:', freshUser.id, 'Role:', freshUser.role);
            this.saveUserToLocal(freshUser);
            callback(freshUser);
          } else {
            console.warn('🔴 No user data found in Firestore for:', firebaseUser.uid);
            callback(null);
          }
        } catch (error) {
          console.error('🔴 Failed to fetch user data:', error);
          callback(null);
        }
      } else {
        console.log('🔍 Auth state change - No Firebase user, clearing local data');
        this.clearUserFromLocal();
        callback(null);
      }
    });
  }

  // Background sync to keep data fresh without blocking UI
  private static async syncUserDataInBackground(userId: string, callback: (user: User | null) => void): Promise<void> {
    try {
      const freshUser = await this.getUserData(userId);
      if (freshUser) {
        const localUser = this.getUserFromLocal();

        // Only update if data has actually changed
        if (!localUser || JSON.stringify(localUser) !== JSON.stringify(freshUser)) {
          console.log('🔄 Background sync: User data updated');
          this.saveUserToLocal(freshUser);
          callback(freshUser);
        }
      }
    } catch (error: any) {
      // Handle Target ID conflicts more gracefully in background sync
      if (error.message?.includes('Target ID already exists')) {
        console.warn('🎯 Background sync: Target ID conflict (non-critical, will retry later)');
      } else {
        console.warn('🔄 Background sync failed (non-critical):', error);
      }
    }
  }

  // Helper method to sync user with Firestore with debouncing
  private static async syncUserWithFirestore(
    userId: string,
    userData: User,
    callback: (user: User | null) => void
  ): Promise<void> {
    // Create unique request ID to prevent duplicate requests
    const requestId = `${userId}-${Date.now()}`;

    // Prevent multiple concurrent requests
    if (currentFirestoreRequest) {
      console.log('Firestore sync already in progress, skipping duplicate request');
      return;
    }

    currentFirestoreRequest = requestId;

    try {
      const userDoc = await getDoc(doc(db, 'users', userId));

      // Only process if this is still the current request
      if (currentFirestoreRequest !== requestId) {
        console.log('Firestore sync request outdated, skipping');
        return;
      }

      if (userDoc.exists()) {
        const rawData = userDoc.data();

        // Properly construct User object with all required fields
        const firestoreUser: User = {
          id: userId,
          email: rawData?.email || userData.email,
          displayName: rawData?.displayName || userData.displayName,
          username: rawData?.username || userData.username,
          role: rawData?.role || userData.role,
          photoURL: rawData?.photoURL || userData.photoURL,
          createdAt: rawData?.createdAt?.toDate?.() || rawData?.createdAt || userData.createdAt,
          updatedAt: rawData?.updatedAt?.toDate?.() || rawData?.updatedAt || userData.updatedAt
        };

        console.log('🔄 Valid user data from Firestore, updating local storage');
        this.saveUserToLocal(firestoreUser);
        callback(firestoreUser);
      } else {
        console.log('🔄 User document not found in Firestore, creating new one');
        await setDoc(doc(db, 'users', userId), userData);
        console.log('🔄 User document created successfully');
      }
    } catch (error: any) {
      // Only log error if this is still the current request
      if (currentFirestoreRequest === requestId) {
        console.warn('Firestore sync failed in auth state change:', error.message);
        this.addPendingWrite(userData);
        // Don't call callback on error to prevent undefined state
      }
    } finally {
      // Clear the current request if it's still ours
      if (currentFirestoreRequest === requestId) {
        currentFirestoreRequest = null;
      }
    }
  }

  static getCurrentUser(): FirebaseUser | null {
    return auth.currentUser;
  }

  // Initialize the service and try to reconnect to Firestore
  static async initialize(): Promise<void> {
    try {
      // Try to enable network connection
      await enableNetwork(db);
      console.log('Firestore network enabled');

      // Only process pending writes if there's an authenticated user
      if (auth.currentUser) {
        await this.processPendingWrites();
      }
    } catch (error) {
      console.warn('Failed to initialize Firestore connection:', error);
    }
  }

  // Force offline mode for testing
  static async goOffline(): Promise<void> {
    try {
      await disableNetwork(db);
      console.log('Firestore offline mode enabled');
    } catch (error) {
      console.warn('Failed to disable Firestore network:', error);
    }
  }

  // Force online mode
  static async goOnline(): Promise<void> {
    try {
      await enableNetwork(db);
      console.log('Firestore online mode enabled');
      await this.processPendingWrites();
    } catch (error) {
      console.warn('Failed to enable Firestore network:', error);
    }
  }

  // Clear pending writes (for debugging)
  static clearPendingWrites(): void {
    try {
      localStorage.removeItem(PENDING_WRITES_KEY);
      console.log('Pending writes cleared');
    } catch (error) {
      console.warn('Failed to clear pending writes:', error);
    }
  }

  // Reset Firestore request state (for debugging/testing)
  static resetFirestoreRequestState(): void {
    currentFirestoreRequest = null;
    console.log('Firestore request state reset');
  }

  // Clear all local storage and force fresh authentication
  static async clearAllLocalDataAndRefresh(): Promise<void> {
    try {
      console.log('🧹 Clearing all local storage data...');

      // Clear all auth-related local storage
      this.clearUserFromLocal();
      this.clearPendingWrites();

      // Clear any other vibes-related local storage
      const keysToRemove = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('vibes_')) {
          keysToRemove.push(key);
        }
      }

      keysToRemove.forEach(key => {
        localStorage.removeItem(key);
        console.log('🧹 Removed local storage key:', key);
      });

      // Force refresh user data if user is authenticated
      if (auth.currentUser) {
        console.log('🔄 Forcing fresh user data fetch...');
        await this.refreshUserData();
      }

      console.log('✅ Local storage cleared and data refreshed');
    } catch (error) {
      console.error('🔴 Failed to clear local storage and refresh:', error);
      throw error;
    }
  }

  // Helper method to create or get user data for social sign-in
  private static async createOrGetSocialUser(firebaseUser: FirebaseUser): Promise<User> {
    // Try to get from Firestore first (prioritize server data)
    try {
      console.log('🔄 Fetching social user data from Firestore for:', firebaseUser.uid);
      const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));
      if (userDoc.exists()) {
        const rawData = userDoc.data();
        console.log('🔄 Found social user in Firestore with role:', rawData?.role);

        // Properly construct User object with all required fields
        const firestoreUser: User = {
          id: firebaseUser.uid,
          email: rawData?.email || firebaseUser.email!,
          displayName: rawData?.displayName || firebaseUser.displayName || firebaseUser.email?.split('@')[0] || 'User',
          username: rawData?.username || generateSmartUsername(firebaseUser.displayName, firebaseUser.email),
          role: rawData?.role || RoleService.getDefaultRole(),
          photoURL: rawData?.photoURL || firebaseUser.photoURL || undefined,
          createdAt: rawData?.createdAt?.toDate?.() || rawData?.createdAt || new Date(),
          updatedAt: rawData?.updatedAt?.toDate?.() || rawData?.updatedAt || new Date()
        };

        console.log('✅ Constructed complete user object:', firestoreUser);
        this.saveUserToLocal(firestoreUser);
        return firestoreUser;
      }
    } catch (firestoreError: any) {
      console.warn('Firestore read failed for social user, falling back to local/create:', firestoreError.message);
    }

    // Check local storage as fallback
    const localUser = this.getUserFromLocal();
    if (localUser && localUser.id === firebaseUser.uid) {
      console.log('Using cached social user data');
      this.processPendingWrites().catch(console.warn);
      return localUser;
    }

    // Create new user data from Firebase Auth
    console.log('🔄 Creating new social user data from Firebase Auth');
    const now = new Date();
    const userData: User = {
      id: firebaseUser.uid,
      email: firebaseUser.email!,
      displayName: firebaseUser.displayName || firebaseUser.email?.split('@')[0] || 'User',
      username: generateSmartUsername(firebaseUser.displayName, firebaseUser.email),
      role: RoleService.getDefaultRole(),
      photoURL: firebaseUser.photoURL || undefined,
      createdAt: now,
      updatedAt: now
    };

    // Save to local storage immediately
    this.saveUserToLocal(userData);

    // Try to save to Firestore, but don't fail if it's offline
    try {
      await setDoc(doc(db, 'users', firebaseUser.uid), userData);
      console.log('Social user data saved to Firestore');
    } catch (saveError: any) {
      console.warn('Could not save social user to Firestore, adding to pending writes:', saveError.message);
      this.addPendingWrite(userData);
    }

    return userData;
  }

  // Google Sign-In with improved popup handling
  static async signInWithGoogle(): Promise<User> {
    // Prevent multiple simultaneous authentication attempts
    if (this.authInProgress) {
      throw new Error('Authentication already in progress. Please wait.');
    }

    this.authInProgress = true;

    try {
      const provider = new GoogleAuthProvider();
      provider.addScope('email');
      provider.addScope('profile');

      // Try popup first for better UX
      try {
        console.log('🔄 Attempting Google sign-in with popup...');
        const result = await signInWithPopup(auth, provider);
        console.log('✅ Google popup sign-in successful');
        return await this.createOrGetSocialUser(result.user);
      } catch (popupError: any) {
        console.log('❌ Popup sign-in failed:', popupError.code, popupError.message);

        // Only fallback to redirect for specific popup-related errors
        const isPopupBlocked = popupError.code === 'auth/popup-blocked';
        const isPopupClosed = popupError.code === 'auth/popup-closed-by-user';
        const isCOOPError = popupError.message?.includes('Cross-Origin-Opener-Policy');
        const isPopupError = popupError.message?.toLowerCase().includes('popup');

        if (isPopupBlocked || isCOOPError || isPopupError) {
          console.log('🔄 Popup blocked or COOP issue, falling back to redirect method');
          await signInWithRedirect(auth, provider);
          throw new Error('redirect_initiated');
        } else if (isPopupClosed) {
          // User closed popup - don't fallback, just throw a user-friendly error
          throw new Error('Sign-in was cancelled. Please try again.');
        }

        // For other errors, don't fallback to redirect
        throw popupError;
      }
    } catch (error: any) {
      if (error.message === 'redirect_initiated') {
        // Don't clear authInProgress for redirects - it will be cleared on page reload
        throw error; // Re-throw redirect signal
      }
      this.authInProgress = false; // Clear flag for non-redirect errors
      throw new Error(error.message || 'Failed to sign in with Google');
    } finally {
      // Clear authInProgress for successful popup sign-ins
      if (this.authInProgress) {
        this.authInProgress = false;
      }
    }
  }

  // Handle redirect result (call this on app initialization)
  static async handleRedirectResult(): Promise<User | null> {
    try {
      // Reset auth progress flag on page load (in case of redirects)
      this.authInProgress = false;

      const result = await getRedirectResult(auth);
      if (result && result.user) {
        console.log('✅ Successfully handled redirect result for:', result.user.email);
        return await this.createOrGetSocialUser(result.user);
      }
      return null;
    } catch (error: any) {
      console.error('❌ Error handling redirect result:', error);
      this.authInProgress = false; // Ensure flag is cleared on error
      throw new Error(error.message || 'Failed to handle sign-in redirect');
    }
  }

  // Apple Sign-In with improved popup handling
  static async signInWithApple(): Promise<User> {
    try {
      const provider = new OAuthProvider('apple.com');
      provider.addScope('email');
      provider.addScope('name');

      // Try popup first for better UX
      try {
        console.log('🔄 Attempting Apple sign-in with popup...');
        const result = await signInWithPopup(auth, provider);
        console.log('✅ Apple popup sign-in successful');
        return await this.createOrGetSocialUser(result.user);
      } catch (popupError: any) {
        console.log('❌ Apple popup sign-in failed:', popupError.code, popupError.message);

        // Only fallback to redirect for specific popup-related errors
        const isPopupBlocked = popupError.code === 'auth/popup-blocked';
        const isPopupClosed = popupError.code === 'auth/popup-closed-by-user';
        const isCOOPError = popupError.message?.includes('Cross-Origin-Opener-Policy');
        const isPopupError = popupError.message?.toLowerCase().includes('popup');

        if (isPopupBlocked || isCOOPError || isPopupError) {
          console.log('🔄 Apple popup blocked or COOP issue, falling back to redirect method');
          await signInWithRedirect(auth, provider);
          throw new Error('redirect_initiated');
        } else if (isPopupClosed) {
          // User closed popup - don't fallback, just throw a user-friendly error
          throw new Error('Sign-in was cancelled. Please try again.');
        }

        // For other errors, don't fallback to redirect
        throw popupError;
      }
    } catch (error: any) {
      if (error.message === 'redirect_initiated') {
        throw error; // Re-throw redirect signal
      }
      throw new Error(error.message || 'Failed to sign in with Apple');
    }
  }

  // Twitter Sign-In with improved popup handling
  static async signInWithTwitter(): Promise<User> {
    try {
      const provider = new TwitterAuthProvider();

      // Try popup first for better UX
      try {
        console.log('🔄 Attempting Twitter sign-in with popup...');
        const result = await signInWithPopup(auth, provider);
        console.log('✅ Twitter popup sign-in successful');
        return await this.createOrGetSocialUser(result.user);
      } catch (popupError: any) {
        console.log('❌ Twitter popup sign-in failed:', popupError.code, popupError.message);

        // Only fallback to redirect for specific popup-related errors
        const isPopupBlocked = popupError.code === 'auth/popup-blocked';
        const isPopupClosed = popupError.code === 'auth/popup-closed-by-user';
        const isCOOPError = popupError.message?.includes('Cross-Origin-Opener-Policy');
        const isPopupError = popupError.message?.toLowerCase().includes('popup');

        if (isPopupBlocked || isCOOPError || isPopupError) {
          console.log('🔄 Twitter popup blocked or COOP issue, falling back to redirect method');
          await signInWithRedirect(auth, provider);
          throw new Error('redirect_initiated');
        } else if (isPopupClosed) {
          // User closed popup - don't fallback, just throw a user-friendly error
          throw new Error('Sign-in was cancelled. Please try again.');
        }

        // For other errors, don't fallback to redirect
        throw popupError;
      }
    } catch (error: any) {
      if (error.message === 'redirect_initiated') {
        throw error; // Re-throw redirect signal
      }
      throw new Error(error.message || 'Failed to sign in with Twitter');
    }
  }
}