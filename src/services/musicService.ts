import {
  collection,
  addDoc,
  getDocs,
  getDoc,
  doc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  DocumentSnapshot,
  clearIndexedDbPersistence,
  writeBatch,
  getDocsFromServer,
  deleteField
} from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { db, storage } from '../lib/firebase';
import { Track, Playlist, Album } from '../types';

export class MusicService {
  // Cache management for fresh data after updates - use timestamp to avoid race conditions
  private static cacheInvalidatedAt = 0;

  // Clear cache and force next queries to fetch from server
  static clearCache(): void {
    console.log('🧹 Clearing MusicService cache - forcing server fetch');
    this.cacheInvalidatedAt = Date.now();
  }

  // Check if cache was invalidated and should force server fetch
  private static shouldForceServerFetch(): boolean {
    // Force server fetch if cache was invalidated in the last 5 seconds
    const timeSinceInvalidation = Date.now() - this.cacheInvalidatedAt;
    return timeSinceInvalidation < 5000; // 5 second window
  }

  // Upload track file to Firebase Storage
  static async uploadTrackFile(file: File, userId: string): Promise<string> {
    try {
      const fileName = `tracks/${userId}/${Date.now()}_${file.name}`;
      const storageRef = ref(storage, fileName);

      const snapshot = await uploadBytes(storageRef, file);
      const downloadURL = await getDownloadURL(snapshot.ref);

      return downloadURL;
    } catch (error: any) {
      throw new Error(`Failed to upload file: ${error.message}`);
    }
  }

  // Upload cover image to Firebase Storage
  static async uploadCoverImage(file: File, userId: string): Promise<string> {
    try {
      const fileName = `covers/${userId}/${Date.now()}_${file.name}`;
      const storageRef = ref(storage, fileName);

      const snapshot = await uploadBytes(storageRef, file);
      const downloadURL = await getDownloadURL(snapshot.ref);

      return downloadURL;
    } catch (error: any) {
      throw new Error(`Failed to upload cover: ${error.message}`);
    }
  }

  // Get track by ID
  static async getTrackById(trackId: string): Promise<Track | null> {
    try {
      const trackDoc = await getDoc(doc(db, 'tracks', trackId));

      if (!trackDoc.exists()) {
        return null;
      }

      const data = trackDoc.data();
      return {
        id: trackDoc.id,
        ...data,
        createdAt: data.createdAt?.toDate ? data.createdAt.toDate() : data.createdAt,
        updatedAt: data.updatedAt?.toDate ? data.updatedAt.toDate() : data.updatedAt,
        publishedAt: data.publishedAt?.toDate ? data.publishedAt.toDate() : data.publishedAt,
        playCount: data.playCount || 0,
        likeCount: data.likeCount || 0,
        saveCount: data.saveCount || 0
      } as Track;
    } catch (error: any) {
      console.error('Failed to get track by ID:', error);
      return null;
    }
  }

  // Create a new track
  static async createTrack(trackData: Omit<Track, 'id' | 'createdAt' | 'updatedAt'>): Promise<Track> {
    try {
      const docRef = await addDoc(collection(db, 'tracks'), {
        ...trackData,
        createdAt: new Date(),
        updatedAt: new Date()
      });

      return {
        id: docRef.id,
        ...trackData,
        createdAt: new Date(),
        updatedAt: new Date()
      };
    } catch (error: any) {
      throw new Error(`Failed to create track: ${error.message}`);
    }
  }

  // Get tracks by user
  static async getTracksByUser(userId: string, includeStatus?: 'draft' | 'published'): Promise<Track[]> {
    try {
      // Build query constraints conditionally
      const constraints = [
        where('uploadedBy', '==', userId)
      ];

      // Add status filter if specified
      if (includeStatus) {
        constraints.push(where('status', '==', includeStatus));
      }

      // Add ordering
      constraints.push(orderBy('createdAt', 'desc'));

      // Create the query with all constraints
      const q = query(collection(db, 'tracks'), ...constraints);

      // Force server fetch if cache was recently invalidated
      const shouldForceServer = this.shouldForceServerFetch();
      const querySnapshot = shouldForceServer
        ? await getDocsFromServer(q)
        : await getDocs(q);

      if (shouldForceServer) {
        console.log('🔄 Fetched fresh track data from server after cache invalidation');
      }
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Track));
    } catch (error: any) {
      throw new Error(`Failed to fetch user tracks: ${error.message}`);
    }
  }

  // Get trending tracks (only published) with engagement-based sorting
  static async getTrendingTracks(limitCount: number = 20): Promise<Track[]> {
    try {
      // Get all published tracks first
      const q = query(
        collection(db, 'tracks'),
        where('status', '==', 'published'),
        limit(50) // Get more tracks to calculate trending scores
      );

      const querySnapshot = await getDocs(q);
      const tracks = querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate ? data.createdAt.toDate() : data.createdAt,
          updatedAt: data.updatedAt?.toDate ? data.updatedAt.toDate() : data.updatedAt,
          publishedAt: data.publishedAt?.toDate ? data.publishedAt.toDate() : data.publishedAt,
          // Ensure engagement metrics have default values
          playCount: data.playCount || 0,
          likeCount: data.likeCount || 0,
          saveCount: data.saveCount || 0
        } as Track;
      });

      // Calculate trending scores and sort
      const tracksWithScores = tracks.map(track => {
        const trendingScore = this.calculateTrendingScore(track);
        return {
          ...track,
          trendingScore
        };
      });

      // Sort by trending score (highest first) and limit results
      return tracksWithScores
        .sort((a, b) => (b.trendingScore || 0) - (a.trendingScore || 0))
        .slice(0, limitCount);
    } catch (error: any) {
      throw new Error(`Failed to fetch trending tracks: ${error.message}`);
    }
  }

  // Calculate trending score based on engagement metrics and recency
  private static calculateTrendingScore(track: Track): number {
    const now = new Date();
    const publishedAt = track.publishedAt || track.createdAt;
    const daysSincePublished = (now.getTime() - publishedAt.getTime()) / (1000 * 60 * 60 * 24);

    // Weights for different metrics
    const playWeight = 1;
    const likeWeight = 3;
    const saveWeight = 5;
    const recencyWeight = 0.1;

    // Base engagement score
    const engagementScore =
      (track.playCount || 0) * playWeight +
      (track.likeCount || 0) * likeWeight +
      (track.saveCount || 0) * saveWeight;

    // Recency bonus (newer tracks get higher scores)
    const recencyBonus = Math.max(0, (30 - daysSincePublished) * recencyWeight);

    // Final trending score
    return engagementScore + recencyBonus;
  }

  // Search tracks
  static async searchTracks(searchTerm: string): Promise<Track[]> {
    try {
      // Note: Firestore doesn't support full-text search natively
      // This is a basic implementation - consider using Algolia or similar for production
      const q = query(
        collection(db, 'tracks'),
        orderBy('title'),
        limit(50)
      );
      
      const querySnapshot = await getDocs(q);
      const allTracks = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Track));

      // Client-side filtering (not ideal for production)
      return allTracks.filter(track => 
        track.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        track.artist.toLowerCase().includes(searchTerm.toLowerCase()) ||
        track.album?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    } catch (error: any) {
      throw new Error(`Failed to search tracks: ${error.message}`);
    }
  }

  // Get tracks by genre
  static async getTracksByGenre(genre: string): Promise<Track[]> {
    try {
      const q = query(
        collection(db, 'tracks'),
        where('genre', '==', genre),
        where('status', '==', 'published'),
        orderBy('createdAt', 'desc'),
        limit(20)
      );

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate ? data.createdAt.toDate() : data.createdAt,
          updatedAt: data.updatedAt?.toDate ? data.updatedAt.toDate() : data.updatedAt,
          publishedAt: data.publishedAt?.toDate ? data.publishedAt.toDate() : data.publishedAt,
          playCount: data.playCount || 0,
          likeCount: data.likeCount || 0,
          saveCount: data.saveCount || 0
        } as Track;
      });
    } catch (error: any) {
      throw new Error(`Failed to fetch tracks by genre: ${error.message}`);
    }
  }

  // Get published singles (tracks not part of any album)
  static async getPublishedSingles(limitCount: number = 20): Promise<Track[]> {
    try {
      const q = query(
        collection(db, 'tracks'),
        where('status', '==', 'published'),
        orderBy('publishedAt', 'desc'),
        limit(limitCount)
      );

      const querySnapshot = await getDocs(q);
      const tracks = querySnapshot.docs
        .map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            ...data,
            createdAt: data.createdAt?.toDate ? data.createdAt.toDate() : data.createdAt,
            updatedAt: data.updatedAt?.toDate ? data.updatedAt.toDate() : data.updatedAt,
            publishedAt: data.publishedAt?.toDate ? data.publishedAt.toDate() : data.publishedAt,
            playCount: data.playCount || 0,
            likeCount: data.likeCount || 0,
            saveCount: data.saveCount || 0
          } as Track;
        })
        .filter(track => !track.albumId); // Only tracks without albumId (singles)

      return tracks;
    } catch (error: any) {
      throw new Error(`Failed to fetch published singles: ${error.message}`);
    }
  }

  // Get new releases (recently published tracks and albums)
  static async getNewReleases(limitCount: number = 20): Promise<Track[]> {
    try {
      const q = query(
        collection(db, 'tracks'),
        where('status', '==', 'published'),
        orderBy('publishedAt', 'desc'),
        limit(limitCount)
      );

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate ? data.createdAt.toDate() : data.createdAt,
          updatedAt: data.updatedAt?.toDate ? data.updatedAt.toDate() : data.updatedAt,
          publishedAt: data.publishedAt?.toDate ? data.publishedAt.toDate() : data.publishedAt,
          playCount: data.playCount || 0,
          likeCount: data.likeCount || 0,
          saveCount: data.saveCount || 0
        } as Track;
      });
    } catch (error: any) {
      throw new Error(`Failed to fetch new releases: ${error.message}`);
    }
  }

  // Get tracks by album ID
  static async getTracksByAlbum(albumId: string): Promise<Track[]> {
    try {
      const q = query(
        collection(db, 'tracks'),
        where('albumId', '==', albumId),
        where('status', '==', 'published'),
        orderBy('trackNumber', 'asc')
      );

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate ? data.createdAt.toDate() : data.createdAt,
          updatedAt: data.updatedAt?.toDate ? data.updatedAt.toDate() : data.updatedAt,
          publishedAt: data.publishedAt?.toDate ? data.publishedAt.toDate() : data.publishedAt,
          playCount: data.playCount || 0,
          likeCount: data.likeCount || 0,
          saveCount: data.saveCount || 0
        } as Track;
      });
    } catch (error: any) {
      throw new Error(`Failed to fetch tracks by album: ${error.message}`);
    }
  }

  // Create playlist
  static async createPlaylist(playlistData: Omit<Playlist, 'id' | 'createdAt' | 'updatedAt'>): Promise<Playlist> {
    try {
      const now = new Date();

      // Clean the data to remove undefined fields
      const cleanData: any = {
        name: playlistData.name,
        tracks: playlistData.tracks || [],
        createdBy: playlistData.createdBy,
        isPublic: playlistData.isPublic,
        collaborators: playlistData.collaborators || [],
        createdAt: now,
        updatedAt: now
      };

      // Only add optional fields if they have values
      if (playlistData.description) {
        cleanData.description = playlistData.description;
      }
      if (playlistData.coverUrl) {
        cleanData.coverUrl = playlistData.coverUrl;
      }

      const docRef = await addDoc(collection(db, 'playlists'), cleanData);

      return {
        id: docRef.id,
        ...playlistData,
        createdAt: now,
        updatedAt: now
      };
    } catch (error: any) {
      throw new Error(`Failed to create playlist: ${error.message}`);
    }
  }

  // Get playlists by user
  static async getPlaylistsByUser(userId: string): Promise<Playlist[]> {
    try {
      // Use simple query without orderBy to avoid index requirement
      const q = query(
        collection(db, 'playlists'),
        where('createdBy', '==', userId)
      );

      const querySnapshot = await getDocs(q);
      const playlists = querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate ? data.createdAt.toDate() : data.createdAt,
          updatedAt: data.updatedAt?.toDate ? data.updatedAt.toDate() : data.updatedAt,
        } as Playlist;
      });

      // Sort by updatedAt descending in memory
      return playlists.sort((a, b) => {
        const aTime = a.updatedAt instanceof Date ? a.updatedAt.getTime() : 0;
        const bTime = b.updatedAt instanceof Date ? b.updatedAt.getTime() : 0;
        return bTime - aTime;
      });
    } catch (error: any) {
      throw new Error(`Failed to fetch user playlists: ${error.message}`);
    }
  }

  // Add track to playlist
  static async addTrackToPlaylist(playlistId: string, trackId: string): Promise<void> {
    try {
      const currentTracks = await this.getPlaylistTracks(playlistId);

      // Check if track already exists in playlist
      if (currentTracks.includes(trackId)) {
        throw new Error('Track already exists in playlist');
      }

      const playlistRef = doc(db, 'playlists', playlistId);
      await updateDoc(playlistRef, {
        tracks: [...currentTracks, trackId],
        updatedAt: new Date()
      });
    } catch (error: any) {
      throw new Error(`Failed to add track to playlist: ${error.message}`);
    }
  }

  // Get playlist by ID
  static async getPlaylistById(playlistId: string): Promise<Playlist | null> {
    try {
      const playlistDoc = await getDoc(doc(db, 'playlists', playlistId));
      if (!playlistDoc.exists()) {
        return null;
      }

      const data = playlistDoc.data();
      return {
        id: playlistDoc.id,
        ...data,
        createdAt: data.createdAt?.toDate ? data.createdAt.toDate() : data.createdAt,
        updatedAt: data.updatedAt?.toDate ? data.updatedAt.toDate() : data.updatedAt,
      } as Playlist;
    } catch (error: any) {
      throw new Error(`Failed to fetch playlist: ${error.message}`);
    }
  }

  // Get playlist tracks
  static async getPlaylistTracks(playlistId: string): Promise<string[]> {
    try {
      const playlist = await this.getPlaylistById(playlistId);
      return playlist?.tracks || [];
    } catch (error: any) {
      throw new Error(`Failed to fetch playlist tracks: ${error.message}`);
    }
  }

  // Get playlist tracks with full track data
  static async getPlaylistTracksWithData(playlistId: string): Promise<Track[]> {
    try {
      const trackIds = await this.getPlaylistTracks(playlistId);
      if (trackIds.length === 0) return [];

      const trackPromises = trackIds.map(async (trackId) => {
        try {
          return await this.getTrackById(trackId);
        } catch (error) {
          console.warn(`Failed to load track ${trackId}:`, error);
          return null;
        }
      });

      const tracks = await Promise.all(trackPromises);
      return tracks.filter((track): track is Track => track !== null);
    } catch (error: any) {
      throw new Error(`Failed to fetch playlist tracks with data: ${error.message}`);
    }
  }

  // Update playlist
  static async updatePlaylist(playlistId: string, updates: Partial<Omit<Playlist, 'id' | 'createdAt' | 'updatedAt'>>): Promise<void> {
    try {
      await updateDoc(doc(db, 'playlists', playlistId), {
        ...updates,
        updatedAt: new Date()
      });
    } catch (error: any) {
      throw new Error(`Failed to update playlist: ${error.message}`);
    }
  }

  // Remove track from playlist
  static async removeTrackFromPlaylist(playlistId: string, trackId: string): Promise<void> {
    try {
      const currentTracks = await this.getPlaylistTracks(playlistId);
      const updatedTracks = currentTracks.filter(id => id !== trackId);

      await updateDoc(doc(db, 'playlists', playlistId), {
        tracks: updatedTracks,
        updatedAt: new Date()
      });
    } catch (error: any) {
      throw new Error(`Failed to remove track from playlist: ${error.message}`);
    }
  }

  // Delete playlist
  static async deletePlaylist(playlistId: string, userId: string): Promise<void> {
    try {
      // Verify ownership
      const playlist = await this.getPlaylistById(playlistId);
      if (!playlist) {
        throw new Error('Playlist not found');
      }

      if (playlist.createdBy !== userId) {
        throw new Error('Unauthorized: You can only delete your own playlists');
      }

      await deleteDoc(doc(db, 'playlists', playlistId));
    } catch (error: any) {
      throw new Error(`Failed to delete playlist: ${error.message}`);
    }
  }

  // Publish a track
  static async publishTrack(trackId: string): Promise<void> {
    try {
      console.log('Publishing track:', trackId);

      // First verify the track exists
      const trackRef = doc(db, 'tracks', trackId);
      const trackDoc = await getDoc(trackRef);

      if (!trackDoc.exists()) {
        throw new Error(`Track with ID ${trackId} not found`);
      }

      const trackData = trackDoc.data();
      console.log('Track data before publishing:', { id: trackId, status: trackData.status });

      // Update the track status to published
      await updateDoc(trackRef, {
        status: 'published',
        publishedAt: new Date(),
        updatedAt: new Date()
      });

      // Verify the update was successful
      const updatedDoc = await getDoc(trackRef);
      const updatedData = updatedDoc.data();
      console.log('Track data after publishing:', { id: trackId, status: updatedData?.status });

      if (updatedData?.status !== 'published') {
        throw new Error('Failed to update track status - status not changed');
      }

      console.log('Track successfully published:', trackId);

      // Clear cache to ensure fresh data on next fetch
      this.clearCache();
    } catch (error: any) {
      console.error('Failed to publish track:', error);
      throw new Error(`Failed to publish track: ${error.message}`);
    }
  }

  // Get singles by user (tracks not part of any album)
  static async getSinglesByUser(userId: string, includeStatus?: 'draft' | 'published'): Promise<Track[]> {
    try {
      console.log('Fetching singles for user:', userId, 'with status:', includeStatus);

      // Use simple query with only uploadedBy filter to avoid index issues
      const q = query(
        collection(db, 'tracks'),
        where('uploadedBy', '==', userId)
      );

      // Force server fetch if cache was recently invalidated
      const shouldForceServer = this.shouldForceServerFetch();
      const querySnapshot = shouldForceServer
        ? await getDocsFromServer(q)
        : await getDocs(q);

      if (shouldForceServer) {
        console.log('🔄 Fetched fresh singles data from server after cache invalidation');
      }
      console.log('Raw tracks fetched:', querySnapshot.docs.length);

      const userTracks = querySnapshot.docs
        .map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            ...data,
            createdAt: data.createdAt?.toDate ? data.createdAt.toDate() : data.createdAt,
            updatedAt: data.updatedAt?.toDate ? data.updatedAt.toDate() : data.updatedAt,
            publishedAt: data.publishedAt?.toDate ? data.publishedAt.toDate() : data.publishedAt
          } as Track;
        })
        .filter(track => !track.albumId); // Only tracks without albumId (singles)

      console.log('Singles after album filter:', userTracks.length);

      // Apply status filter if specified
      let filteredTracks = userTracks;
      if (includeStatus) {
        console.log('🔵 Applying status filter:', includeStatus);
        console.log('🔵 All tracks before filter:', userTracks.map(t => ({ id: t.id, title: t.title, status: t.status })));
        filteredTracks = userTracks.filter(track => track.status === includeStatus);
        console.log('🔵 Tracks after status filter:', filteredTracks.map(t => ({ id: t.id, title: t.title, status: t.status })));
        console.log('🔵 Singles after status filter count:', filteredTracks.length);
      }

      // Sort by createdAt descending (newest first)
      const sortedTracks = filteredTracks.sort((a, b) => {
        const aTime = a.createdAt instanceof Date ? a.createdAt.getTime() : 0;
        const bTime = b.createdAt instanceof Date ? b.createdAt.getTime() : 0;
        return bTime - aTime;
      });

      console.log('Final singles count:', sortedTracks.length);
      return sortedTracks;
    } catch (error: any) {
      console.error('Error in getSinglesByUser:', error);
      throw new Error(`Failed to fetch user singles: ${error.message}`);
    }
  }

  // Update track metadata
  static async updateTrack(trackId: string, updates: Partial<Omit<Track, 'id' | 'createdAt'>>): Promise<void> {
    try {
      await updateDoc(doc(db, 'tracks', trackId), {
        ...updates,
        updatedAt: new Date()
      });

      // Clear cache to ensure fresh data on next fetch
      this.clearCache();
    } catch (error: any) {
      throw new Error(`Failed to update track: ${error.message}`);
    }
  }

  // Update track cover
  static async updateTrackCover(trackId: string, coverFile: File, userId: string): Promise<string> {
    try {
      // Upload new cover
      const coverUrl = await this.uploadCoverImage(coverFile, userId);

      // Update track document with new cover URL
      await updateDoc(doc(db, 'tracks', trackId), {
        coverUrl,
        updatedAt: new Date()
      });

      // Clear cache to ensure fresh data on next fetch
      this.clearCache();

      return coverUrl;
    } catch (error: any) {
      throw new Error(`Failed to update track cover: ${error.message}`);
    }
  }

  // Remove track cover
  static async removeTrackCover(trackId: string): Promise<void> {
    try {
      await updateDoc(doc(db, 'tracks', trackId), {
        coverUrl: deleteField(),
        updatedAt: new Date()
      });

      // Clear cache to ensure fresh data on next fetch
      this.clearCache();
    } catch (error: any) {
      throw new Error(`Failed to remove track cover: ${error.message}`);
    }
  }

  // Delete track
  static async deleteTrack(trackId: string, userId: string): Promise<void> {
    try {
      // Get track info first to check if it's part of an album
      const trackDoc = await getDoc(doc(db, 'tracks', trackId));
      if (!trackDoc.exists()) {
        throw new Error('Track not found');
      }

      const track = trackDoc.data() as Track;

      // Verify ownership
      if (track.uploadedBy !== userId) {
        throw new Error('Unauthorized: You can only delete your own tracks');
      }

      const batch = writeBatch(db);

      // If track is part of an album, update the album
      if (track.albumId) {
        const albumRef = doc(db, 'albums', track.albumId);
        const albumDoc = await getDoc(albumRef);

        if (albumDoc.exists()) {
          const album = albumDoc.data() as Album;
          const updatedTrackIds = album.trackIds.filter(id => id !== trackId);

          // Update album track count and track IDs
          batch.update(albumRef, {
            trackIds: updatedTrackIds,
            trackCount: updatedTrackIds.length,
            updatedAt: new Date()
          });
        }
      }

      // Delete the track
      batch.delete(doc(db, 'tracks', trackId));

      await batch.commit();

      // Clear cache to ensure fresh data on next fetch
      this.clearCache();

      // Note: You'd also want to delete the file from Storage
      // This requires storing the storage path in the track document
    } catch (error: any) {
      throw new Error(`Failed to delete track: ${error.message}`);
    }
  }
}