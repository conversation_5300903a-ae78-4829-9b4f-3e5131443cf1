import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore, clearIndexedDbPersistence, enableNetwork, disableNetwork, enableIndexedDbPersistence } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';

const firebaseConfig = {
  // Add your Firebase config here
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID
};

// Initialize Firebase
console.log('🔥 Firebase: Initializing app...');
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
console.log('🔥 Firebase: Initializing services...');
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);

console.log('✅ Firebase: All services initialized successfully');
console.log('🔥 Firebase Config:', {
  projectId: firebaseConfig.projectId,
  authDomain: firebaseConfig.authDomain
});

// Initialize Firebase with proper persistence handling
const isDevelopment = import.meta.env.DEV;

// Global query deduplication to prevent Target ID conflicts
const activeQueries = new Map<string, Promise<any>>();
const queryTimeouts = new Map<string, NodeJS.Timeout>();

// Helper function to deduplicate queries with improved cleanup
export const deduplicateQuery = <T>(key: string, queryFn: () => Promise<T>): Promise<T> => {
  if (activeQueries.has(key)) {
    console.log(`🔄 Reusing existing query: ${key}`);
    return activeQueries.get(key)!;
  }

  // Clear any existing timeout for this key
  const existingTimeout = queryTimeouts.get(key);
  if (existingTimeout) {
    clearTimeout(existingTimeout);
    queryTimeouts.delete(key);
  }

  const promise = queryFn()
    .finally(() => {
      // Set a timeout to clean up the query after a delay
      // This prevents immediate re-creation of the same query
      const timeout = setTimeout(() => {
        activeQueries.delete(key);
        queryTimeouts.delete(key);
      }, 1000); // 1 second delay

      queryTimeouts.set(key, timeout);
    });

  activeQueries.set(key, promise);
  return promise;
};

if (!isDevelopment) {
  console.log('💾 Firebase: Enabling offline persistence for production...');

  enableIndexedDbPersistence(db, {
    forceOwningTab: false, // Allow multiple tabs
    synchronizeTabs: true  // Enable tab synchronization
  })
    .then(() => {
      console.log('✅ Firebase: Offline persistence enabled with tab sync!');
    })
    .catch((err) => {
      if (err.code === 'failed-precondition') {
        console.warn('⚠️ Firebase: Persistence conflict, using memory cache');
      } else if (err.code === 'unimplemented') {
        console.warn('⚠️ Firebase: Browser doesn\'t support persistence');
      } else {
        console.warn('⚠️ Firebase: Persistence error:', err.message);
      }
      // App continues to work with memory cache
    });
} else {
  console.log('🔧 Firebase: Development mode - using memory cache to prevent Target ID conflicts');
  // In development, don't manipulate network state to avoid conflicts
}

// Function to clear Firestore persistence and reinitialize
export const clearFirestorePersistence = async (): Promise<void> => {
  try {
    console.log('Clearing Firestore persistence...');
    await disableNetwork(db);
    await clearIndexedDbPersistence(db);
    await enableNetwork(db);
    console.log('Firestore persistence cleared and network re-enabled');
  } catch (error) {
    console.error('Failed to clear Firestore persistence:', error);
    // If clearing fails, try to re-enable network anyway
    try {
      await enableNetwork(db);
    } catch (networkError) {
      console.error('Failed to re-enable network:', networkError);
    }
  }
};

// Function to clear all active queries and timeouts
export const clearActiveQueries = (): void => {
  console.log('🧹 Clearing all active queries and timeouts...');

  // Clear all timeouts
  queryTimeouts.forEach((timeout) => {
    clearTimeout(timeout);
  });
  queryTimeouts.clear();

  // Clear all active queries
  activeQueries.clear();

  console.log('✅ All active queries cleared');
};

// Function to reset Firebase connection to resolve Target ID conflicts
export const resetFirebaseConnection = async (): Promise<void> => {
  try {
    console.log('🔄 Resetting Firebase connection to resolve conflicts...');

    // Clear active queries first
    clearActiveQueries();

    await disableNetwork(db);
    // Wait a moment for cleanup
    await new Promise(resolve => setTimeout(resolve, 200));
    await enableNetwork(db);
    console.log('✅ Firebase connection reset completed');
  } catch (error) {
    console.error('❌ Failed to reset Firebase connection:', error);
  }
};

// Debug function to test query deduplication
export const testQueryDeduplication = async (): Promise<void> => {
  console.log('🧪 Testing query deduplication...');

  // Test multiple concurrent calls to the same query
  const promises = Array.from({ length: 5 }, (_, i) =>
    deduplicateQuery('test-query', async () => {
      console.log(`🔄 Executing test query ${i + 1}`);
      await new Promise(resolve => setTimeout(resolve, 100));
      return `Result ${i + 1}`;
    })
  );

  const results = await Promise.all(promises);
  console.log('🧪 Test results:', results);
  console.log('✅ Query deduplication test completed');
};

// Make debug functions available globally for testing
if (typeof window !== 'undefined') {
  (window as any).testQueryDeduplication = testQueryDeduplication;
  (window as any).resetFirebaseConnection = resetFirebaseConnection;
  (window as any).clearFirestorePersistence = clearFirestorePersistence;
  (window as any).clearActiveQueries = clearActiveQueries;
}

export default app;