import React from 'react';
import { IndexTrigger } from '../components/debug/IndexTrigger';
import { CarouselCleanup } from '../components/debug/CarouselCleanup';
import { LikeSystemDebug } from '../components/debug/LikeSystemDebug';
import { MusicPersistenceDebug } from '../components/debug/MusicPersistenceDebug';
import { FirebaseDebug } from '../components/debug/FirebaseDebug';

export const DebugIndexPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-background p-4">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">
            🛠️ Admin Debug Tools
          </h1>
          <p className="text-muted-foreground">
            Debug tools for music persistence, like system, Firestore indexes, and carousel management
          </p>
        </div>

        <div className="space-y-8">
          <div>
            <h2 className="text-xl font-semibold text-foreground mb-4">Firebase Debug Tools</h2>
            <FirebaseDebug />
          </div>

          <div>
            <h2 className="text-xl font-semibold text-foreground mb-4">Music Persistence Debug</h2>
            <MusicPersistenceDebug />
          </div>

          <div>
            <h2 className="text-xl font-semibold text-foreground mb-4">Like System Debug</h2>
            <LikeSystemDebug />
          </div>

          <div>
            <h2 className="text-xl font-semibold text-foreground mb-4">Carousel Cleanup</h2>
            <CarouselCleanup />
          </div>

          <div>
            <h2 className="text-xl font-semibold text-foreground mb-4">Firestore Index Generator</h2>
            <IndexTrigger />
          </div>
        </div>
        
        <div className="mt-8 text-center">
          <p className="text-xs text-muted-foreground">
            Admin debug tools for troubleshooting and maintenance.
          </p>
        </div>
      </div>
    </div>
  );
};
