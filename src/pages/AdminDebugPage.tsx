import React from 'react';
import { Navigate } from 'react-router-dom';
import { AdminDebug } from '../components/debug/AdminDebug';
import { useAuth } from '../hooks/useAuth';
import { RoleService } from '../services/roleService';
import { Card } from '../components/atoms/Card';
import { Shield, AlertTriangle } from 'lucide-react';

export const AdminDebugPage: React.FC = () => {
  const { user, loading } = useAuth();

  // Show loading state
  if (loading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin w-8 h-8 border-2 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // Redirect non-authenticated users to home
  if (!user) {
    return <Navigate to="/" replace />;
  }

  // Only allow admin users to access this page
  if (!RoleService.isAdmin(user)) {
    return (
      <div className="h-full overflow-y-auto">
        <div className="max-w-4xl mx-auto px-4 py-6">
          <Card className="w-full p-6 text-center">
            <div className="flex flex-col items-center space-y-4">
              <div className="w-16 h-16 bg-red-500/10 rounded-full flex items-center justify-center">
                <AlertTriangle className="w-8 h-8 text-red-500" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-foreground mb-2">Access Denied</h1>
                <p className="text-muted-foreground mb-4">
                  This page is restricted to administrators only.
                </p>
                <p className="text-sm text-muted-foreground">
                  Current role: <span className="font-medium">{user.role}</span>
                </p>
              </div>
            </div>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-foreground mb-2">Debug Tools</h1>
        <p className="text-muted-foreground">
          Debug your admin status and permissions. This page helps troubleshoot admin setup issues.
        </p>
      </div>

      <AdminDebug />
    </div>
  );
};
