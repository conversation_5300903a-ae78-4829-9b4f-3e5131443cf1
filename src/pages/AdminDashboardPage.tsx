import React, { useState, useEffect } from 'react';
import { Users, Presentation, Music, TrendingUp, Eye, Settings, RefreshCw } from 'lucide-react';
import { Card } from '../components/atoms/Card';
import { Button } from '../components/atoms/Button';
import { CarouselService } from '../services/carouselService';
import { UserManagementService } from '../services/userManagementService';
import { AlbumService } from '../services/albumService';
import { MusicService } from '../services/musicService';
import { EngagementAggregationService, PlatformEngagementStats } from '../services/engagementAggregationService';
import { EngagementService } from '../services/engagementService';
import { useNavigate } from 'react-router-dom';
import { cn } from '../utils/cn';
import { formatNumber, formatRelativeTime } from '../utils/formatters';
import { useAuth } from '../contexts/AuthContext';

interface DashboardStats {
  totalUsers: number;
  totalCarouselSlides: number;
  activeCarouselSlides: number;
  publishedAlbums: number;
  totalTracks: number;
  totalPlays: number;
  totalLikes: number;
  totalSaves: number;
}

export const AdminDashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [platformStats, setPlatformStats] = useState<PlatformEngagementStats | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState<'hour' | 'day' | 'week' | 'month'>('day');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      loadDashboardStats();
    }
  }, [user, selectedPeriod]);

  const loadDashboardStats = async () => {
    if (!user) {
      setError('User not authenticated');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Load basic stats first
      const [
        users,
        allSlides,
        activeSlides,
        publishedAlbums
      ] = await Promise.all([
        UserManagementService.getAllUsers(user),
        CarouselService.getAllSlides(),
        CarouselService.getActiveSlides(),
        AlbumService.getPublishedAlbums()
      ]);

      // Try to load engagement stats, but don't fail if they don't exist yet
      let engagementStats = null;
      try {
        engagementStats = await EngagementAggregationService.getPlatformEngagementStatsForPeriod(selectedPeriod);
      } catch (error) {
        console.warn('Engagement stats not available yet (this is normal for new installations):', error);
        // Create empty engagement stats
        engagementStats = {
          totalPlays: 0,
          totalLikes: 0,
          totalSaves: 0,
          totalTracks: publishedAlbums.reduce((acc, album) => acc + (album.trackCount || 0), 0),
          totalAlbums: publishedAlbums.length,
          totalUsers: users.length,
          topTracks: [],
          recentActivity: [],
          periodStats: {
            period: selectedPeriod,
            plays: 0,
            likes: 0,
            saves: 0,
            startDate: new Date(),
            endDate: new Date()
          }
        };
      }

      setStats({
        totalUsers: users.length,
        totalCarouselSlides: allSlides.length,
        activeCarouselSlides: activeSlides.length,
        publishedAlbums: publishedAlbums.length,
        totalTracks: engagementStats.totalTracks,
        totalPlays: engagementStats.totalPlays,
        totalLikes: engagementStats.totalLikes,
        totalSaves: engagementStats.totalSaves
      });

      setPlatformStats(engagementStats);
    } catch (err: any) {
      console.error('Failed to load dashboard stats:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const testEngagement = async () => {
    if (!user) return;

    try {
      // Get published tracks using getTrendingTracks (which gets published tracks)
      const tracks = await MusicService.getTrendingTracks(10);
      if (tracks.length === 0) {
        // Try getting published singles instead
        const singles = await MusicService.getPublishedSingles(10);
        if (singles.length === 0) {
          alert('No published tracks found. Please upload and publish some music first.');
          return;
        }

        const firstTrack = singles[0];
        console.log('🧪 Testing engagement with single:', firstTrack.title);

        // Create a test play event
        await EngagementService.trackPlay(firstTrack.id, firstTrack.albumId, user.id);

        alert(`✅ Test play event created for "${firstTrack.title}"! Check the console and refresh the dashboard.`);
        return;
      }

      const firstTrack = tracks[0];
      console.log('🧪 Testing engagement with track:', firstTrack.title);

      // Create a test play event
      await EngagementService.trackPlay(firstTrack.id, firstTrack.albumId, user.id);

      alert(`✅ Test play event created for "${firstTrack.title}"! Check the console and refresh the dashboard.`);
    } catch (error) {
      console.error('❌ Test engagement failed:', error);
      alert('❌ Test failed. Check the console for details.');
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-foreground mb-2">Admin Dashboard</h1>
          <p className="text-muted-foreground">Loading dashboard statistics...</p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="p-6">
              <div className="animate-pulse">
                <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-foreground mb-2">Admin Dashboard</h1>
          <p className="text-muted-foreground">Overview of platform statistics and quick actions</p>
        </div>
        <Card className="p-6 border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/20">
          <p className="text-red-600 dark:text-red-400">Failed to load dashboard: {error}</p>
          <Button onClick={loadDashboardStats} className="mt-4">
            Retry
          </Button>
        </Card>
      </div>
    );
  }

  const statCards = [
    {
      title: 'Total Users',
      value: stats?.totalUsers || 0,
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100 dark:bg-blue-900/20',
      action: () => navigate('/admin/users')
    },
    {
      title: 'Total Tracks',
      value: stats?.totalTracks || 0,
      icon: Music,
      color: 'text-green-600',
      bgColor: 'bg-green-100 dark:bg-green-900/20'
    },
    {
      title: 'Published Albums',
      value: stats?.publishedAlbums || 0,
      icon: Music,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100 dark:bg-purple-900/20'
    },
    {
      title: `Plays (${selectedPeriod === 'hour' ? 'Last Hour' : selectedPeriod === 'day' ? 'Last 24h' : selectedPeriod === 'week' ? 'Last Week' : 'Last Month'})`,
      value: formatNumber(platformStats?.periodStats?.plays || 0),
      subtitle: `Total: ${formatNumber(stats?.totalPlays || 0)}`,
      icon: TrendingUp,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100 dark:bg-orange-900/20'
    },
    {
      title: `Likes (${selectedPeriod === 'hour' ? 'Last Hour' : selectedPeriod === 'day' ? 'Last 24h' : selectedPeriod === 'week' ? 'Last Week' : 'Last Month'})`,
      value: formatNumber(platformStats?.periodStats?.likes || 0),
      subtitle: `Total: ${formatNumber(stats?.totalLikes || 0)}`,
      icon: Eye,
      color: 'text-red-600',
      bgColor: 'bg-red-100 dark:bg-red-900/20'
    },
    {
      title: 'Carousel Slides',
      value: `${stats?.activeCarouselSlides || 0}/${stats?.totalCarouselSlides || 0}`,
      subtitle: 'Active/Total',
      icon: Presentation,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-100 dark:bg-indigo-900/20',
      action: () => navigate('/admin/carousel')
    }
  ];

  const quickActions = [
    {
      title: 'Manage Carousel',
      description: 'Update home page carousel slides and album links',
      icon: Presentation,
      action: () => navigate('/admin/carousel'),
      color: 'from-purple-600 to-indigo-600'
    },
    {
      title: 'User Management',
      description: 'Manage user roles and permissions',
      icon: Users,
      action: () => navigate('/admin/users'),
      color: 'from-blue-600 to-cyan-600'
    },
    {
      title: 'Debug Tools',
      description: 'Admin debugging and troubleshooting tools',
      icon: Settings,
      action: () => navigate('/admin/debug'),
      color: 'from-gray-600 to-gray-700'
    }
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground mb-2">Admin Dashboard</h1>
          <p className="text-muted-foreground">
            Overview of platform statistics and quick actions
          </p>
        </div>

        <div className="flex items-center space-x-4">
          {/* Test Engagement Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={testEngagement}
            className="flex items-center space-x-2 bg-green-50 border-green-200 text-green-700 hover:bg-green-100"
          >
            <Music className="w-4 h-4" />
            <span>Test Play</span>
          </Button>

          {/* Refresh Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={loadDashboardStats}
            disabled={loading}
            className="flex items-center space-x-2"
          >
            <RefreshCw className={cn("w-4 h-4", loading && "animate-spin")} />
            <span>Refresh</span>
          </Button>

          {/* Time Period Selector */}
          <div className="flex items-center space-x-2">
            <span className="text-sm text-muted-foreground">Analytics Period:</span>
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value as 'hour' | 'day' | 'week' | 'month')}
              className="px-3 py-2 text-sm border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
            >
              <option value="hour">Last Hour</option>
              <option value="day">Last 24 Hours</option>
              <option value="week">Last Week</option>
              <option value="month">Last Month</option>
            </select>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {statCards.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card
              key={index}
              className={cn(
                "p-6 transition-all duration-200",
                stat.action ? "cursor-pointer hover:shadow-lg hover:scale-105" : ""
              )}
              onClick={stat.action}
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground mb-1">
                    {stat.title}
                  </p>
                  <p className="text-2xl font-bold text-foreground">
                    {stat.value}
                  </p>
                  {stat.subtitle && (
                    <p className="text-xs text-muted-foreground mt-1">
                      {stat.subtitle}
                    </p>
                  )}
                </div>
                <div className={cn("w-12 h-12 rounded-lg flex items-center justify-center", stat.bgColor)}>
                  <Icon className={cn("w-6 h-6", stat.color)} />
                </div>
              </div>
            </Card>
          );
        })}
      </div>

      {/* Top Performing Content */}
      <div>
        <h2 className="text-xl font-semibold text-foreground mb-4">Top Performing Tracks</h2>
        {platformStats && platformStats.topTracks.length > 0 ? (
          <Card className="p-6">
            <div className="space-y-4">
              {platformStats.topTracks.slice(0, 5).map((item, index) => (
                <div key={item.track.id} className="flex items-center space-x-4 p-3 rounded-lg hover:bg-secondary/10 transition-colors">
                  <div className="w-8 h-8 bg-gradient-to-br from-primary to-primary/70 rounded-full flex items-center justify-center text-white font-bold text-sm">
                    {index + 1}
                  </div>

                  {item.track.coverUrl ? (
                    <img
                      src={item.track.coverUrl}
                      alt={item.track.title}
                      className="w-12 h-12 rounded-lg object-cover"
                    />
                  ) : (
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                      <Music className="w-6 h-6 text-white" />
                    </div>
                  )}

                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-foreground truncate">{item.track.title}</h4>
                    <p className="text-sm text-muted-foreground truncate">{item.track.artist}</p>
                  </div>

                  <div className="text-right">
                    <p className="text-sm font-medium text-foreground">
                      {formatNumber(item.playCount)} plays
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {formatNumber(item.likeCount)} likes
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        ) : (
          <Card className="p-6 text-center">
            <TrendingUp className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium text-foreground mb-2">No Engagement Data Yet</h3>
            <p className="text-muted-foreground mb-4">
              Track performance will appear here once users start playing, liking, and saving music.
            </p>
            <p className="text-sm text-muted-foreground">
              Upload some music and share your platform to start seeing engagement metrics!
            </p>
          </Card>
        )}
      </div>

      {/* Recent Activity */}
      <div>
        <h2 className="text-xl font-semibold text-foreground mb-4">Recent Activity</h2>
        {platformStats && platformStats.recentActivity.length > 0 ? (
          <Card className="p-6">
            <div className="space-y-3">
              {platformStats.recentActivity.slice(0, 10).map((activity, index) => (
                <div key={index} className="flex items-center space-x-3 text-sm">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-muted-foreground">
                    Someone played <span className="font-medium text-foreground">"{activity.trackTitle}"</span> by {activity.artist}
                  </span>
                  <span className="text-xs text-muted-foreground ml-auto">
                    {formatRelativeTime(activity.timestamp)}
                  </span>
                </div>
              ))}
            </div>
          </Card>
        ) : (
          <Card className="p-6 text-center">
            <Eye className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium text-foreground mb-2">No Recent Activity</h3>
            <p className="text-muted-foreground mb-4">
              User activity will appear here in real-time as people interact with your music.
            </p>
            <p className="text-sm text-muted-foreground">
              Activity includes plays, likes, saves, and other user interactions.
            </p>
          </Card>
        )}
      </div>

      {/* Quick Actions */}
      <div>
        <h2 className="text-xl font-semibold text-foreground mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {quickActions.map((action, index) => {
            const Icon = action.icon;
            return (
              <Card 
                key={index}
                className="p-6 cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105"
                onClick={action.action}
              >
                <div className="flex items-start space-x-4">
                  <div className={cn(
                    "w-12 h-12 rounded-lg flex items-center justify-center bg-gradient-to-br",
                    action.color
                  )}>
                    <Icon className="w-6 h-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-foreground mb-1">
                      {action.title}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      {action.description}
                    </p>
                  </div>
                </div>
              </Card>
            );
          })}
        </div>
      </div>
    </div>
  );
};
