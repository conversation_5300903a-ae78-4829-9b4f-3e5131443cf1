@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 250 250 252;
    --foreground: 9 9 11;
    --card: 255 255 255;
    --card-foreground: 9 9 11;
    --popover: 255 255 255;
    --popover-foreground: 9 9 11;
    --primary: 108 92 231;
    --primary-foreground: 255 255 255;
    --secondary: 244 244 245;
    --secondary-foreground: 9 9 11;
    --muted: 244 244 245;
    --muted-foreground: 113 113 122;
    --accent: 244 244 245;
    --accent-foreground: 9 9 11;
    --destructive: 239 68 68;
    --destructive-foreground: 255 255 255;
    --border: 228 228 231;
    --input: 228 228 231;
    --ring: 108 92 231;
    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0 0; /* Pure black background */
    --foreground: 250 250 250;
    --card: 20 20 20; /* RGB(20, 20, 20) for all cards */
    --card-foreground: 250 250 250;
    --popover: 20 20 20; /* RGB(20, 20, 20) */
    --popover-foreground: 250 250 250;
    --primary: 159 127 255;
    --primary-foreground: 9 9 11;
    --secondary: 35 35 35; /* RGB(35, 35, 35) - Lighter for main content area */
    --secondary-foreground: 250 250 250;
    --muted: 20 20 20; /* RGB(20, 20, 20) */
    --muted-foreground: 161 161 170;
    --accent: 20 20 20; /* RGB(20, 20, 20) */
    --accent-foreground: 250 250 250;
    --destructive: 239 68 68;
    --destructive-foreground: 255 255 255;
    --border: 39 39 42;
    --input: 39 39 42;
    --ring: 159 127 255;
  }

  /* Ensure body and html use the background color */
  html,
  body {
    background-color: rgb(var(--background));
    color: rgb(var(--foreground));
  }

  /* Theme transition for smooth switching */
  * {
    transition-property: background-color, border-color, color, fill, stroke,
      opacity, box-shadow, transform, filter, backdrop-filter;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
  }

  /* Prevent transition on page load */
  .no-transition * {
    transition: none !important;
  }

  /* Ensure smooth theme transitions */
  html {
    color-scheme: light;
  }

  html.dark {
    color-scheme: dark;
  }

  /* Optimize for theme switching performance */
  html.theme-transitioning,
  html.theme-transitioning *,
  html.theme-transitioning *:before,
  html.theme-transitioning *:after {
    transition: none !important;
    transition-delay: 0 !important;
  }
}

@layer utilities {
  /* Z-Index Hierarchy - Standardized layering system */
  .z-base {
    z-index: 10;
  } /* Base content */
  .z-sticky {
    z-index: 20;
  } /* Sticky headers, sidebars */
  .z-fixed {
    z-index: 30;
  } /* Fixed navigation, toolbars */
  .z-overlay {
    z-index: 40;
  } /* Global player, persistent UI, header */
  .z-dropdown {
    z-index: 50;
  } /* Dropdowns, tooltips, popovers */
  .z-modal {
    z-index: 9999;
  } /* Modals, dialogs - maximum priority */
  .z-notification {
    z-index: 70;
  } /* Critical notifications, alerts */
  .z-maximum {
    z-index: 9999;
  } /* Emergency override (use sparingly) */

  /* Mobile-First Responsive Utilities */

  /* Safe Area Support for iOS devices */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }
  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
  .safe-area-left {
    padding-left: env(safe-area-inset-left);
  }
  .safe-area-right {
    padding-right: env(safe-area-inset-right);
  }
  .safe-area-inset {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* Touch-Friendly Interaction Utilities */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }
  .touch-target-sm {
    min-height: 36px;
    min-width: 36px;
  }
  .touch-target-lg {
    min-height: 56px;
    min-width: 56px;
  }

  /* Mobile-Optimized Scrolling */
  .scroll-smooth-mobile {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Prevent zoom on input focus (iOS) */
  .no-zoom {
    font-size: 16px;
  }

  /* Mobile-friendly tap highlights */
  .tap-highlight-none {
    -webkit-tap-highlight-color: transparent;
  }

  /* Mobile gesture utilities */
  .touch-pan-x {
    touch-action: pan-x;
  }
  .touch-pan-y {
    touch-action: pan-y;
  }
  .touch-manipulation {
    touch-action: manipulation;
  }

  /* Mobile-first layout utilities */
  .mobile-stack {
    @apply flex flex-col space-y-4;
  }
  .mobile-stack > * {
    @apply w-full;
  }

  @media (min-width: 768px) {
    .mobile-stack {
      @apply flex-row space-y-0 space-x-4;
    }
    .mobile-stack > * {
      @apply w-auto;
    }
  }

  .drop-shadow-centered-md {
    filter: drop-shadow(0 1px 2px rgb(0 0 0 / 0.021))
      drop-shadow(0 1px 1px rgb(0 0 0 / 0.018));
  }

  /* Slide animations for mobile player */
  .animate-slide-up {
    animation: slideUp 0.3s ease-out forwards;
  }

  .animate-slide-down {
    animation: slideDown 0.3s ease-out forwards;
  }

  @keyframes slideUp {
    from {
      transform: translateY(100%);
    }
    to {
      transform: translateY(0);
    }
  }

  @keyframes slideDown {
    from {
      transform: translateY(0);
    }
    to {
      transform: translateY(100%);
    }
  }

  .light-interaction {
    position: relative;
    overflow: hidden;
  }

  .light-interaction::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
      circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
      rgba(59, 130, 246, 0.02) 0%,
      transparent 40%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }

  .light-interaction:hover::before {
    opacity: 1;
  }

  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Custom animations for visualizations */
  @keyframes spin-slow {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .animate-spin-slow {
    animation: spin-slow 8s linear infinite;
  }

  /* Theme-aware scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.3);
    border-radius: 3px;
    transition: background-color 0.3s ease;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(156, 163, 175, 0.5);
  }

  .dark ::-webkit-scrollbar-thumb {
    background: rgba(75, 85, 99, 0.3);
  }

  .dark ::-webkit-scrollbar-thumb:hover {
    background: rgba(75, 85, 99, 0.5);
  }

  /* Smooth theme transition utilities */
  .theme-transition {
    transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
      border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
      color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
      fill 0.3s cubic-bezier(0.4, 0, 0.2, 1),
      stroke 0.3s cubic-bezier(0.4, 0, 0.2, 1),
      opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1),
      box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1),
      transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .theme-transition {
      transition: none;
    }

    * {
      transition-duration: 0.01ms !important;
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      scroll-behavior: auto !important;
    }
  }
}

/* Custom scrollbar for webkit browsers with theme support */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.3);
  border-radius: 3px;
  transition: background-color 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.5);
}

.dark ::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.3);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(75, 85, 99, 0.5);
}
