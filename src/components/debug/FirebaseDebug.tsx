import React, { useState } from 'react';
import { Card } from '../atoms/Card';
import { Button } from '../atoms/Button';
import { Database, RefreshCw, Trash2, <PERSON><PERSON><PERSON>riangle, CheckCircle } from 'lucide-react';
import { cn } from '../../utils/cn';

interface FirebaseDebugProps {
  className?: string;
}

export const FirebaseDebug: React.FC<FirebaseDebugProps> = ({ className }) => {
  const [isResetting, setIsResetting] = useState(false);
  const [lastAction, setLastAction] = useState<string>('');

  const resetFirebaseConnection = async () => {
    setIsResetting(true);
    setLastAction('Resetting Firebase connection...');
    try {
      // Call the global function
      await (window as any).resetFirebaseConnection();
      setLastAction('✅ Firebase connection reset successfully');
    } catch (error) {
      setLastAction('❌ Failed to reset Firebase connection');
      console.error('Reset failed:', error);
    } finally {
      setIsResetting(false);
    }
  };

  const clearActiveQueries = () => {
    setLastAction('Clearing active queries...');
    try {
      (window as any).clearActiveQueries();
      setLastAction('✅ Active queries cleared successfully');
    } catch (error) {
      setLastAction('❌ Failed to clear active queries');
      console.error('Clear failed:', error);
    }
  };

  const testQueryDeduplication = async () => {
    setLastAction('Testing query deduplication...');
    try {
      await (window as any).testQueryDeduplication();
      setLastAction('✅ Query deduplication test completed');
    } catch (error) {
      setLastAction('❌ Query deduplication test failed');
      console.error('Test failed:', error);
    }
  };

  const clearFirestorePersistence = async () => {
    setIsResetting(true);
    setLastAction('Clearing Firestore persistence...');
    try {
      await (window as any).clearFirestorePersistence();
      setLastAction('✅ Firestore persistence cleared successfully');
    } catch (error) {
      setLastAction('❌ Failed to clear Firestore persistence');
      console.error('Clear persistence failed:', error);
    } finally {
      setIsResetting(false);
    }
  };

  return (
    <Card className={cn("p-6", className)}>
      <div className="space-y-6">
        <div className="flex items-center space-x-3">
          <Database className="w-6 h-6 text-blue-500" />
          <div>
            <h3 className="text-lg font-semibold text-foreground">Firebase Debug Tools</h3>
            <p className="text-sm text-muted-foreground">
              Tools to diagnose and fix Firebase Target ID conflicts and connection issues
            </p>
          </div>
        </div>

        {/* Status */}
        {lastAction && (
          <div className={cn(
            "p-3 rounded-lg border",
            lastAction.includes('✅') ? "bg-green-500/10 border-green-500/20" :
            lastAction.includes('❌') ? "bg-red-500/10 border-red-500/20" :
            "bg-blue-500/10 border-blue-500/20"
          )}>
            <div className="text-sm text-foreground">{lastAction}</div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="space-y-4">
          <h4 className="font-medium text-foreground">Quick Actions:</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <Button
              onClick={clearActiveQueries}
              variant="outline"
              size="sm"
              className="flex items-center space-x-2"
              disabled={isResetting}
            >
              <Trash2 className="w-4 h-4" />
              <span>Clear Active Queries</span>
            </Button>

            <Button
              onClick={resetFirebaseConnection}
              variant="secondary"
              size="sm"
              className="flex items-center space-x-2"
              disabled={isResetting}
            >
              <RefreshCw className={cn("w-4 h-4", isResetting && "animate-spin")} />
              <span>Reset Connection</span>
            </Button>

            <Button
              onClick={testQueryDeduplication}
              variant="outline"
              size="sm"
              className="flex items-center space-x-2"
              disabled={isResetting}
            >
              <CheckCircle className="w-4 h-4" />
              <span>Test Deduplication</span>
            </Button>

            <Button
              onClick={clearFirestorePersistence}
              variant="destructive"
              size="sm"
              className="flex items-center space-x-2"
              disabled={isResetting}
            >
              <AlertTriangle className="w-4 h-4" />
              <span>Clear Persistence</span>
            </Button>
          </div>
        </div>

        {/* Common Issues */}
        <div className="space-y-4">
          <h4 className="font-medium text-foreground">Common Issues & Solutions:</h4>
          <div className="space-y-3">
            <div className="p-3 bg-muted/30 rounded-lg">
              <div className="text-sm font-medium text-foreground">Target ID already exists</div>
              <div className="text-xs text-muted-foreground mt-1">
                Solution: Click "Clear Active Queries" then "Reset Connection"
              </div>
            </div>

            <div className="p-3 bg-muted/30 rounded-lg">
              <div className="text-sm font-medium text-foreground">Multiple auth listeners</div>
              <div className="text-xs text-muted-foreground mt-1">
                Solution: Refresh the page or click "Reset Connection"
              </div>
            </div>

            <div className="p-3 bg-muted/30 rounded-lg">
              <div className="text-sm font-medium text-foreground">Persistence conflicts</div>
              <div className="text-xs text-muted-foreground mt-1">
                Solution: Click "Clear Persistence" (warning: will clear offline data)
              </div>
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="space-y-4">
          <h4 className="font-medium text-foreground">Debug Instructions:</h4>
          <div className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
            <ol className="text-sm text-muted-foreground space-y-2 list-decimal list-inside">
              <li>Check browser console for "Target ID already exists" errors</li>
              <li>Try "Clear Active Queries" first (least disruptive)</li>
              <li>If issues persist, use "Reset Connection"</li>
              <li>For persistent issues, try "Clear Persistence" (last resort)</li>
              <li>Use "Test Deduplication" to verify the fix is working</li>
            </ol>
          </div>
        </div>
      </div>
    </Card>
  );
};
