import React, { useState } from 'react';
import { Card } from '../atoms/Card';
import { Button } from '../atoms/Button';
import { Music, RefreshCw, Trash2, Play, Pause, RotateCcw } from 'lucide-react';
import { useMusicStore } from '../../store/musicStore';
import { useMusicStateRestoration } from '../../hooks/useMusicStateRestoration';
import { cn } from '../../utils/cn';
import { testMusicPersistence } from '../../utils/testMusicPersistence';
import { testAudioRestoration, testPlayback, forceLoadTrack } from '../../utils/testAudioRestoration';

interface MusicPersistenceDebugProps {
  className?: string;
}

export const MusicPersistenceDebug: React.FC<MusicPersistenceDebugProps> = ({ className }) => {
  const musicStore = useMusicStore();
  const { hasPersistedState, isRestored } = useMusicStateRestoration();
  const [refreshKey, setRefreshKey] = useState(0);

  const clearPersistedState = () => {
    localStorage.removeItem('vibes-music-state');
    setRefreshKey(prev => prev + 1);
    console.log('🗑️ Cleared persisted music state');
  };

  const simulatePageRefresh = () => {
    console.log('🔄 Simulating page refresh...');
    window.location.reload();
  };

  const testRestoration = () => {
    console.log('🧪 Testing music state restoration...');
    console.log('📊 Current state:', {
      currentTrack: musicStore.currentTrack?.title,
      queueLength: musicStore.queue.length,
      volume: musicStore.volume,
      hasPersistedState,
      isRestored
    });

    // Run comprehensive tests
    const persistenceResult = testMusicPersistence();
    const audioResult = testAudioRestoration();

    console.log('🎯 Persistence test:', persistenceResult ? 'PASSED' : 'FAILED');
    console.log('🎯 Audio restoration test:', audioResult ? 'PASSED' : 'FAILED');
  };

  const testPlaybackFunction = async () => {
    console.log('🎵 Testing playback functionality...');
    const result = await testPlayback();
    console.log('🎯 Playback test:', result ? 'PASSED' : 'FAILED');
  };

  const forceLoadTrackFunction = async () => {
    console.log('🔧 Force loading track...');
    const result = await forceLoadTrack();
    console.log('🎯 Force load test:', result ? 'PASSED' : 'FAILED');
  };

  const getPersistedData = () => {
    const stored = localStorage.getItem('vibes-music-state');
    if (!stored) return null;
    try {
      return JSON.parse(stored);
    } catch {
      return null;
    }
  };

  const persistedData = getPersistedData();

  return (
    <Card className={cn("p-6", className)} key={refreshKey}>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-foreground flex items-center space-x-2">
              <Music className="w-5 h-5 text-blue-500" />
              <span>Music Persistence Debug</span>
            </h3>
            <p className="text-sm text-muted-foreground">
              Test music state persistence across page refreshes
            </p>
          </div>
          <div className="flex space-x-2">
            <Button
              onClick={testRestoration}
              variant="secondary"
              size="sm"
              className="flex items-center space-x-2"
            >
              <Music className="w-4 h-4" />
              <span>Test Restoration</span>
            </Button>
            <Button
              onClick={testPlaybackFunction}
              variant="outline"
              size="sm"
              className="flex items-center space-x-2"
            >
              <Play className="w-4 h-4" />
              <span>Test Playback</span>
            </Button>
            <Button
              onClick={forceLoadTrackFunction}
              variant="destructive"
              size="sm"
              className="flex items-center space-x-2"
            >
              <RotateCcw className="w-4 h-4" />
              <span>Force Load Track</span>
            </Button>
            <Button
              onClick={clearPersistedState}
              variant="outline"
              size="sm"
              className="flex items-center space-x-2"
            >
              <Trash2 className="w-4 h-4" />
              <span>Clear State</span>
            </Button>
            <Button
              onClick={simulatePageRefresh}
              variant="outline"
              size="sm"
              className="flex items-center space-x-2"
            >
              <RefreshCw className="w-4 h-4" />
              <span>Refresh Page</span>
            </Button>
          </div>
        </div>

        {/* Current Music State */}
        <div className="space-y-4">
          <h4 className="font-medium text-foreground">Current Music State:</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="p-3 bg-muted/50 rounded-lg">
                <div className="text-sm font-medium text-foreground">Current Track</div>
                <div className="text-sm text-muted-foreground">
                  {musicStore.currentTrack ? (
                    <>
                      <div>"{musicStore.currentTrack.title}"</div>
                      <div>by {musicStore.currentTrack.artist}</div>
                    </>
                  ) : (
                    'None'
                  )}
                </div>
              </div>
              <div className="p-3 bg-muted/50 rounded-lg">
                <div className="text-sm font-medium text-foreground">Queue</div>
                <div className="text-sm text-muted-foreground">
                  {musicStore.queue.length} tracks
                </div>
              </div>
              <div className="p-3 bg-muted/50 rounded-lg">
                <div className="text-sm font-medium text-foreground">Current Album</div>
                <div className="text-sm text-muted-foreground">
                  {musicStore.currentAlbum?.title || 'None'}
                </div>
              </div>
            </div>
            <div className="space-y-2">
              <div className="p-3 bg-muted/50 rounded-lg">
                <div className="text-sm font-medium text-foreground">Playback State</div>
                <div className="text-sm text-muted-foreground">
                  {musicStore.isPlaying ? 'Playing' : 'Paused'}
                </div>
              </div>
              <div className="p-3 bg-muted/50 rounded-lg">
                <div className="text-sm font-medium text-foreground">Volume</div>
                <div className="text-sm text-muted-foreground">
                  {Math.round(musicStore.volume * 100)}%
                </div>
              </div>
              <div className="p-3 bg-muted/50 rounded-lg">
                <div className="text-sm font-medium text-foreground">Settings</div>
                <div className="text-sm text-muted-foreground">
                  Shuffle: {musicStore.shuffle ? 'On' : 'Off'} | 
                  Repeat: {musicStore.repeat}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Persistence Status */}
        <div className="space-y-4">
          <h4 className="font-medium text-foreground">Persistence Status:</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className={cn(
              "p-3 rounded-lg border",
              hasPersistedState ? "bg-green-500/10 border-green-500/20" : "bg-gray-500/10 border-gray-500/20"
            )}>
              <div className="text-sm font-medium text-foreground">Has Persisted State</div>
              <div className="text-sm text-muted-foreground">
                {hasPersistedState ? 'Yes' : 'No'}
              </div>
            </div>
            <div className={cn(
              "p-3 rounded-lg border",
              isRestored ? "bg-green-500/10 border-green-500/20" : "bg-yellow-500/10 border-yellow-500/20"
            )}>
              <div className="text-sm font-medium text-foreground">Is Restored</div>
              <div className="text-sm text-muted-foreground">
                {isRestored ? 'Yes' : 'No'}
              </div>
            </div>
            <div className={cn(
              "p-3 rounded-lg border",
              persistedData ? "bg-blue-500/10 border-blue-500/20" : "bg-gray-500/10 border-gray-500/20"
            )}>
              <div className="text-sm font-medium text-foreground">LocalStorage</div>
              <div className="text-sm text-muted-foreground">
                {persistedData ? 'Data Found' : 'No Data'}
              </div>
            </div>
          </div>
        </div>

        {/* Persisted Data Preview */}
        {persistedData && (
          <div className="space-y-4">
            <h4 className="font-medium text-foreground">Persisted Data Preview:</h4>
            <div className="p-4 bg-muted/30 rounded-lg">
              <pre className="text-xs text-muted-foreground overflow-x-auto">
                {JSON.stringify(persistedData, null, 2)}
              </pre>
            </div>
          </div>
        )}

        {/* Test Instructions */}
        <div className="space-y-4">
          <h4 className="font-medium text-foreground">Test Instructions:</h4>
          <div className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
            <ol className="text-sm text-muted-foreground space-y-2 list-decimal list-inside">
              <li>Play a song from any page (Explore, Library, etc.)</li>
              <li>Navigate to different pages to verify the player persists</li>
              <li>Click "Refresh Page" button above to simulate page refresh</li>
              <li>Verify that the song context is restored (track, queue, album)</li>
              <li>Check that playback doesn't auto-start (good UX)</li>
              <li>Verify that volume and settings are restored</li>
            </ol>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="space-y-4">
          <h4 className="font-medium text-foreground">Quick Actions:</h4>
          <div className="flex flex-wrap gap-2">
            <Button
              onClick={() => musicStore.togglePlay()}
              variant="outline"
              size="sm"
              className="flex items-center space-x-2"
              disabled={!musicStore.currentTrack}
            >
              {musicStore.isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
              <span>{musicStore.isPlaying ? 'Pause' : 'Play'}</span>
            </Button>
            <Button
              onClick={() => musicStore.nextTrack()}
              variant="outline"
              size="sm"
              disabled={!musicStore.currentTrack || musicStore.queue.length <= 1}
            >
              Next Track
            </Button>
            <Button
              onClick={() => musicStore.previousTrack()}
              variant="outline"
              size="sm"
              disabled={!musicStore.currentTrack}
            >
              Previous Track
            </Button>
            <Button
              onClick={() => musicStore.toggleShuffle()}
              variant="outline"
              size="sm"
              disabled={!musicStore.currentTrack}
            >
              Shuffle: {musicStore.shuffle ? 'On' : 'Off'}
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
};
