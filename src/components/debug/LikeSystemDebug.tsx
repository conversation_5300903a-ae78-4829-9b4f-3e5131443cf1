import React, { useState, useEffect } from 'react';
import { Card } from '../atoms/Card';
import { Button } from '../atoms/Button';
import { Heart, Play, RefreshCw, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { EngagementService } from '../../services/engagementService';
import { MusicService } from '../../services/musicService';
import { Track } from '../../types';
import { cn } from '../../utils/cn';

interface LikeSystemDebugProps {
  className?: string;
}

interface TestResult {
  test: string;
  status: 'pending' | 'success' | 'error' | 'warning';
  message: string;
  details?: string;
}

export const LikeSystemDebug: React.FC<LikeSystemDebugProps> = ({ className }) => {
  const { user } = useAuth();
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [testTrack, setTestTrack] = useState<Track | null>(null);

  const addResult = (result: TestResult) => {
    setTestResults(prev => [...prev, result]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const runLikeSystemTests = async () => {
    if (!user) {
      addResult({
        test: 'Authentication Check',
        status: 'error',
        message: 'No authenticated user found',
        details: 'Please sign in to run like system tests'
      });
      return;
    }

    setIsRunning(true);
    clearResults();

    try {
      // Test 1: Get test track
      addResult({
        test: 'Getting Test Track',
        status: 'pending',
        message: 'Fetching tracks for testing...'
      });

      const tracks = await MusicService.getTrendingTracks(1);
      if (tracks.length === 0) {
        addResult({
          test: 'Getting Test Track',
          status: 'error',
          message: 'No tracks available for testing',
          details: 'Upload some tracks first'
        });
        return;
      }

      const track = tracks[0];
      setTestTrack(track);
      addResult({
        test: 'Getting Test Track',
        status: 'success',
        message: `Using "${track.title}" by ${track.artist}`
      });

      // Test 2: Check current like status
      addResult({
        test: 'Check Like Status',
        status: 'pending',
        message: 'Checking current like status...'
      });

      const currentLike = await EngagementService.getUserLike(track.id, 'track', user.id);
      const isLiked = !!currentLike;
      addResult({
        test: 'Check Like Status',
        status: 'success',
        message: `Track is ${isLiked ? 'LIKED' : 'NOT LIKED'}`
      });

      // Test 3: Toggle like status
      addResult({
        test: 'Toggle Like',
        status: 'pending',
        message: isLiked ? 'Attempting to unlike...' : 'Attempting to like...'
      });

      if (isLiked) {
        await EngagementService.unlikeItem(track.id, 'track', user.id);
        addResult({
          test: 'Toggle Like',
          status: 'success',
          message: 'Successfully unliked track'
        });
      } else {
        await EngagementService.likeItem(track.id, 'track', user.id);
        addResult({
          test: 'Toggle Like',
          status: 'success',
          message: 'Successfully liked track'
        });
      }

      // Test 4: Verify the change
      addResult({
        test: 'Verify Change',
        status: 'pending',
        message: 'Verifying like status change...'
      });

      const newLike = await EngagementService.getUserLike(track.id, 'track', user.id);
      const isNowLiked = !!newLike;
      
      if (isLiked !== isNowLiked) {
        addResult({
          test: 'Verify Change',
          status: 'success',
          message: `Like status changed correctly: ${isNowLiked ? 'LIKED' : 'NOT LIKED'}`
        });
      } else {
        addResult({
          test: 'Verify Change',
          status: 'error',
          message: 'Like status did not change',
          details: 'The toggle operation may have failed silently'
        });
      }

      // Test 5: Check liked tracks list
      addResult({
        test: 'Check Liked Tracks List',
        status: 'pending',
        message: 'Checking user liked tracks...'
      });

      const likedTracks = await EngagementService.getUserLikedTracks(user.id);
      const isInList = likedTracks.includes(track.id);
      
      if ((isNowLiked && isInList) || (!isNowLiked && !isInList)) {
        addResult({
          test: 'Check Liked Tracks List',
          status: 'success',
          message: `Liked tracks list is consistent (${likedTracks.length} total)`
        });
      } else {
        addResult({
          test: 'Check Liked Tracks List',
          status: 'warning',
          message: 'Liked tracks list inconsistency detected',
          details: `Track ${isNowLiked ? 'should be' : 'should not be'} in list but ${isInList ? 'is' : 'is not'}`
        });
      }

      // Test 6: Test Firestore permissions
      addResult({
        test: 'Firestore Permissions',
        status: 'pending',
        message: 'Testing Firestore permissions...'
      });

      await EngagementService.testFirestorePermissions(user.id);
      addResult({
        test: 'Firestore Permissions',
        status: 'success',
        message: 'All Firestore permissions working correctly'
      });

    } catch (error: any) {
      addResult({
        test: 'System Error',
        status: 'error',
        message: error.message || 'Unknown error occurred',
        details: error.stack
      });
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'warning':
        return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      case 'pending':
        return <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />;
      default:
        return null;
    }
  };

  return (
    <Card className={cn("p-6", className)}>
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-foreground flex items-center space-x-2">
              <Heart className="w-5 h-5 text-red-500" />
              <span>Like System Debug</span>
            </h3>
            <p className="text-sm text-muted-foreground">
              Test the like functionality across the application
            </p>
          </div>
          <Button
            onClick={runLikeSystemTests}
            disabled={isRunning || !user}
            className="flex items-center space-x-2"
          >
            <Play className="w-4 h-4" />
            <span>{isRunning ? 'Running...' : 'Run Tests'}</span>
          </Button>
        </div>

        {!user && (
          <div className="p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
            <p className="text-sm text-yellow-600">
              Please sign in to run like system tests
            </p>
          </div>
        )}

        {testResults.length > 0 && (
          <div className="space-y-2">
            <h4 className="font-medium text-foreground">Test Results:</h4>
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {testResults.map((result, index) => (
                <div
                  key={index}
                  className={cn(
                    "p-3 rounded-lg border",
                    result.status === 'success' && "bg-green-500/10 border-green-500/20",
                    result.status === 'error' && "bg-red-500/10 border-red-500/20",
                    result.status === 'warning' && "bg-yellow-500/10 border-yellow-500/20",
                    result.status === 'pending' && "bg-blue-500/10 border-blue-500/20"
                  )}
                >
                  <div className="flex items-start space-x-3">
                    {getStatusIcon(result.status)}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium text-sm">{result.test}</span>
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">
                        {result.message}
                      </p>
                      {result.details && (
                        <p className="text-xs text-muted-foreground mt-1 font-mono">
                          {result.details}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};
