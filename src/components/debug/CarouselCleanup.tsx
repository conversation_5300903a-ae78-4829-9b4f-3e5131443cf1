import React, { useState } from 'react';
import { Trash2, List, AlertTriangle, CheckCircle } from 'lucide-react';
import { Card } from '../atoms/Card';
import { Button } from '../atoms/Button';
import { CarouselCleanupService } from '../../scripts/cleanupCarouselSlides';

export const CarouselCleanup: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleCleanupHardcoded = async () => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      await CarouselCleanupService.cleanupHardcodedSlides();
      setSuccess('Successfully removed hardcoded carousel slides (Plead Album, VibeCode Album)');
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDebugList = async () => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      await CarouselCleanupService.debugListAllSlides();
      setSuccess('Check console for carousel slides list');
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleCleanupOrphaned = async () => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      await CarouselCleanupService.cleanupOrphanedSlides();
      setSuccess('Successfully removed orphaned carousel slides (slides with no album linked)');
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="p-6">
      <div className="space-y-4">
        <div>
          <h3 className="text-lg font-semibold text-foreground mb-2">Carousel Cleanup Tools</h3>
          <p className="text-sm text-muted-foreground">
            Tools to clean up unwanted carousel slides from your database
          </p>
        </div>

        {success && (
          <div className="flex items-center space-x-2 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
            <CheckCircle className="w-4 h-4 text-green-600" />
            <span className="text-sm text-green-700 dark:text-green-300">{success}</span>
          </div>
        )}

        {error && (
          <div className="flex items-center space-x-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <AlertTriangle className="w-4 h-4 text-red-600" />
            <span className="text-sm text-red-700 dark:text-red-300">{error}</span>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Button
            onClick={handleCleanupHardcoded}
            disabled={loading}
            variant="destructive"
            className="flex items-center space-x-2"
          >
            <Trash2 className="w-4 h-4" />
            <span>{loading ? 'Cleaning...' : 'Remove Hardcoded Slides'}</span>
          </Button>

          <Button
            onClick={handleDebugList}
            disabled={loading}
            variant="outline"
            className="flex items-center space-x-2"
          >
            <List className="w-4 h-4" />
            <span>{loading ? 'Loading...' : 'List All Slides'}</span>
          </Button>

          <Button
            onClick={handleCleanupOrphaned}
            disabled={loading}
            variant="secondary"
            className="flex items-center space-x-2"
          >
            <Trash2 className="w-4 h-4" />
            <span>{loading ? 'Cleaning...' : 'Remove Orphaned Slides'}</span>
          </Button>
        </div>

        <div className="text-xs text-muted-foreground space-y-1">
          <p><strong>Remove Hardcoded Slides:</strong> Deletes "Plead Album" and "VibeCode Album" slides created by seeding script</p>
          <p><strong>List All Slides:</strong> Shows all carousel slides in console for debugging</p>
          <p><strong>Remove Orphaned Slides:</strong> Deletes slides that have no album linked</p>
        </div>
      </div>
    </Card>
  );
};
