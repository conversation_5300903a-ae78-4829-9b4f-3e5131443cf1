import React, { useState, useEffect } from 'react';
import { Shield, User, Crown, AlertCircle, Trash2 } from 'lucide-react';
import { Card } from '../atoms/Card';
import { Button } from '../atoms/Button';
import { useAuth } from '../../hooks/useAuth';
import { UserManagementService } from '../../services/userManagementService';
import { RoleService } from '../../services/roleService';
import { AuthService } from '../../services/authService';
import { DatabaseDemoDataCleaner } from '../../scripts/cleanDemoDataFromDatabase';
import { DemoDataCleaner } from '../../utils/clearDemoData';

export const AdminDebug: React.FC = () => {
  const { user } = useAuth();
  const [hasAdmins, setHasAdmins] = useState<boolean | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [debugInfo, setDebugInfo] = useState<any>(null);

  // Security check: This component should only be rendered for admin users
  // If somehow a non-admin user accesses this component, show access denied
  if (user && !RoleService.isAdmin(user)) {
    return (
      <Card className="p-6 border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/20">
        <div className="flex items-center space-x-3 text-red-600 dark:text-red-400">
          <AlertCircle className="w-6 h-6" />
          <div>
            <h3 className="font-semibold">Access Denied</h3>
            <p className="text-sm">This component is restricted to administrators only.</p>
            <p className="text-xs mt-1">Current role: {user.role}</p>
          </div>
        </div>
      </Card>
    );
  }

  useEffect(() => {
    if (user) {
      checkAdminStatus();
    }
  }, [user]);

  const checkAdminStatus = async () => {
    if (!user) return;

    try {
      const adminExists = await UserManagementService.hasAdminUsers();
      const userRole = user.role;
      const canUpload = RoleService.canUploadMusic(user);
      const isAdmin = RoleService.isAdmin(user);

      setHasAdmins(adminExists);
      setDebugInfo({
        userRole,
        canUpload,
        isAdmin,
        adminExists,
        userId: user.id,
        userEmail: user.email
      });
    } catch (err: any) {
      setError(err.message);
    }
  };

  const forceAdminSetup = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      await UserManagementService.initializeFirstAdmin(user.id);
      console.log('🔧 Admin setup complete, refreshing user data...');

      // Refresh user data instead of reloading the page
      await AuthService.refreshUserData();
      await checkAdminStatus(); // Refresh the debug info

      alert('Admin setup complete! User data refreshed.');
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const refreshUserData = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      console.log('🔄 Manually refreshing user data...');
      await AuthService.refreshUserData();
      await checkAdminStatus();
      alert('User data refreshed successfully!');
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const clearLocalStorageAndRefresh = async () => {
    setLoading(true);
    setError(null);

    try {
      console.log('🧹 Clearing local storage and refreshing...');
      await AuthService.clearAllLocalDataAndRefresh();
      await checkAdminStatus();
      alert('Local storage cleared and data refreshed successfully!');
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const promoteCurrentUserToAdmin = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      console.log('🔧 Promoting current user to admin using REST API...');
      await UserManagementService.initializeFirstAdmin(user.id);
      await checkAdminStatus();
      alert('Successfully promoted to admin! Please refresh the page.');
    } catch (err: any) {
      setError(err.message);
      console.error('Failed to promote user to admin:', err);
    } finally {
      setLoading(false);
    }
  };

  const cleanDemoData = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      console.log('🧹 Cleaning demo data from database and local storage...');

      // Clean database demo data
      await DatabaseDemoDataCleaner.runCompleteCleanup();

      // Clean local storage demo data
      DemoDataCleaner.clearAllDemoData();

      alert('Demo data cleanup completed! Check console for details.');
    } catch (err: any) {
      setError(err.message);
      console.error('Failed to clean demo data:', err);
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <Card className="m-4 p-4">
        <p>Please log in to see admin debug info</p>
      </Card>
    );
  }

  return (
    <Card className="w-full p-6">
      <div className="flex items-center space-x-3 mb-4">
        <Shield className="w-6 h-6 text-blue-500" />
        <h2 className="text-lg font-semibold">Admin Debug Panel</h2>
      </div>

      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <strong>User ID:</strong> {user.id}
          </div>
          <div>
            <strong>Email:</strong> {user.email}
          </div>
          <div>
            <strong>Display Name:</strong> {user.displayName}
          </div>
          <div>
            <strong>Current Role:</strong> {user.role || 'undefined'}
          </div>
        </div>

        {debugInfo && (
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-medium mb-2">Debug Information:</h3>
            <pre className="text-xs overflow-auto max-h-40 bg-white p-2 rounded border">
              {JSON.stringify(debugInfo, null, 2)}
            </pre>
          </div>
        )}

        <div className="flex items-center space-x-2">
          <User className="w-4 h-4" />
          <span>Can Upload Music: {RoleService.canUploadMusic(user) ? '✅ Yes' : '❌ No'}</span>
        </div>

        <div className="flex items-center space-x-2">
          <Crown className="w-4 h-4" />
          <span>Is Admin: {RoleService.isAdmin(user) ? '✅ Yes' : '❌ No'}</span>
        </div>

        <div className="flex items-center space-x-2">
          <Shield className="w-4 h-4" />
          <span>Admins Exist in System: {hasAdmins === null ? '⏳ Checking...' : hasAdmins ? '✅ Yes' : '❌ No'}</span>
        </div>

        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm">
            <div className="flex items-center space-x-2">
              <AlertCircle className="w-4 h-4" />
              <span>Error: {error}</span>
            </div>
          </div>
        )}

        <div className="space-y-2">
          <Button onClick={checkAdminStatus} variant="outline" className="w-full">
            Refresh Admin Status
          </Button>

          <Button onClick={refreshUserData} variant="outline" className="w-full">
            Force Refresh User Data
          </Button>

          <Button
            onClick={clearLocalStorageAndRefresh}
            variant="outline"
            className="w-full bg-gradient-to-r from-red-500/10 to-orange-500/10 border-red-500/20 hover:from-red-500/20 hover:to-orange-500/20"
          >
            Clear Local Storage & Refresh
          </Button>

          {user?.role === 'listener' && hasAdmins === false && (
            <>
              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg text-yellow-800 text-sm">
                <div className="flex items-center space-x-2">
                  <AlertCircle className="w-4 h-4" />
                  <span><strong>Initial Setup Only:</strong> This button is only available when no admins exist in the system.</span>
                </div>
              </div>
              <Button
                onClick={promoteCurrentUserToAdmin}
                disabled={loading}
                className="w-full bg-gradient-to-r from-green-500 to-emerald-500 text-white"
              >
                {loading ? 'Promoting...' : 'Promote to Admin (INITIAL SETUP ONLY)'}
              </Button>
            </>
          )}

          {hasAdmins === false && (
            <Button
              onClick={forceAdminSetup}
              disabled={loading}
              className="w-full bg-gradient-to-r from-yellow-500 to-orange-500 text-white"
            >
              {loading ? 'Setting up...' : 'Force Admin Setup'}
            </Button>
          )}

          <Button
            onClick={cleanDemoData}
            disabled={loading}
            variant="outline"
            className="w-full bg-gradient-to-r from-red-500/10 to-pink-500/10 border-red-500/20 hover:from-red-500/20 hover:to-pink-500/20"
          >
            <Trash2 className="w-4 h-4 mr-2" />
            {loading ? 'Cleaning...' : 'Clean Demo Data'}
          </Button>
        </div>

        <div className="text-xs text-gray-500">
          <p><strong>Expected Behavior:</strong></p>
          <ul className="list-disc list-inside space-y-1">
            <li>If your role is "listener", you should not see upload buttons</li>
            <li>If your role is "admin", you should see upload buttons and admin panel access</li>
            <li>Admin users can upload music and manage platform content</li>
          </ul>
        </div>
      </div>
    </Card>
  );
};
