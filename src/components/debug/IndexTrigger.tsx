import React, { useState } from 'react';
import { Button } from '../atoms/Button';
import { Card } from '../atoms/Card';
import { MusicService } from '../../services/musicService';
import { AlbumService } from '../../services/albumService';
import { EngagementService } from '../../services/engagementService';

/**
 * Debug component to trigger Firestore queries that will generate
 * index creation links in the browser console
 */
export const IndexTrigger: React.FC = () => {
  const [results, setResults] = useState<string[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const addResult = (message: string) => {
    setResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const triggerIndexCreation = async () => {
    setIsRunning(true);
    setResults([]);
    
    addResult('🔥 Starting index creation triggers...');
    
    try {
      // 1. Test trending tracks query
      addResult('📊 Testing trending tracks query...');
      try {
        await MusicService.getTrendingTracks(5);
        addResult('✅ Trending tracks query successful');
      } catch (error: any) {
        addResult(`🔍 Trending tracks error (check console for index link): ${error.message}`);
        console.error('Trending tracks index needed:', error);
      }

      // 2. Test published albums query
      addResult('📚 Testing published albums query...');
      try {
        await AlbumService.getPublishedAlbums(5);
        addResult('✅ Published albums query successful');
      } catch (error: any) {
        addResult(`🔍 Published albums error (check console for index link): ${error.message}`);
        console.error('Published albums index needed:', error);
      }

      // 3. Test published singles query
      addResult('🎵 Testing published singles query...');
      try {
        await MusicService.getPublishedSingles(5);
        addResult('✅ Published singles query successful');
      } catch (error: any) {
        addResult(`🔍 Published singles error (check console for index link): ${error.message}`);
        console.error('Published singles index needed:', error);
      }

      // 4. Test new releases query
      addResult('🆕 Testing new releases query...');
      try {
        await MusicService.getNewReleases(5);
        addResult('✅ New releases query successful');
      } catch (error: any) {
        addResult(`🔍 New releases error (check console for index link): ${error.message}`);
        console.error('New releases index needed:', error);
      }

      // 5. Test tracks by genre query
      addResult('🎸 Testing tracks by genre query...');
      try {
        await MusicService.getTracksByGenre('Electronic');
        addResult('✅ Tracks by genre query successful');
      } catch (error: any) {
        addResult(`🔍 Tracks by genre error (check console for index link): ${error.message}`);
        console.error('Tracks by genre index needed:', error);
      }

      // 6. Test tracks by album query (if we have any albums)
      addResult('💿 Testing tracks by album query...');
      try {
        await MusicService.getTracksByAlbum('dummy-album-id');
        addResult('✅ Tracks by album query successful');
      } catch (error: any) {
        addResult(`🔍 Tracks by album error (check console for index link): ${error.message}`);
        console.error('Tracks by album index needed:', error);
      }

      // 7. Test engagement queries (these will definitely need indexes)
      addResult('❤️ Testing engagement queries...');
      
      try {
        await EngagementService.getUserLikedTracks('dummy-user-id');
        addResult('✅ User liked tracks query successful');
      } catch (error: any) {
        addResult(`🔍 User liked tracks error (check console for index link): ${error.message}`);
        console.error('User liked tracks index needed:', error);
      }

      try {
        await EngagementService.getUserSavedTracks('dummy-user-id');
        addResult('✅ User saved tracks query successful');
      } catch (error: any) {
        addResult(`🔍 User saved tracks error (check console for index link): ${error.message}`);
        console.error('User saved tracks index needed:', error);
      }

      try {
        await EngagementService.getUserLike('dummy-track-id', 'track', 'dummy-user-id');
        addResult('✅ User like check query successful');
      } catch (error: any) {
        addResult(`🔍 User like check error (check console for index link): ${error.message}`);
        console.error('User like check index needed:', error);
      }

      try {
        await EngagementService.getUserSave('dummy-track-id', 'track', 'dummy-user-id');
        addResult('✅ User save check query successful');
      } catch (error: any) {
        addResult(`🔍 User save check error (check console for index link): ${error.message}`);
        console.error('User save check index needed:', error);
      }

      addResult('🎉 All queries completed! Check browser console for index creation links.');
      addResult('📋 Look for Firebase error messages with clickable links to create indexes.');
      
    } catch (error: any) {
      addResult(`❌ Unexpected error: ${error.message}`);
    } finally {
      setIsRunning(false);
    }
  };

  return (
    <Card className="p-6 max-w-4xl mx-auto">
      <div className="space-y-4">
        <div className="text-center">
          <h2 className="text-xl font-bold text-foreground mb-2">
            🔧 Firestore Index Trigger Tool
          </h2>
          <p className="text-sm text-muted-foreground mb-4">
            This tool runs all the queries needed for the Discover page to generate 
            Firestore index creation links in the browser console.
          </p>
          
          <Button 
            onClick={triggerIndexCreation}
            disabled={isRunning}
            variant="primary"
            className="mb-4"
          >
            {isRunning ? '⏳ Running Queries...' : '🚀 Trigger Index Creation'}
          </Button>
        </div>

        {results.length > 0 && (
          <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-xs max-h-96 overflow-y-auto">
            <div className="mb-2 text-yellow-400 font-bold">
              📋 Query Results (Check browser console for index links):
            </div>
            {results.map((result, index) => (
              <div key={index} className="mb-1">
                {result}
              </div>
            ))}
          </div>
        )}

        <div className="text-xs text-muted-foreground bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
          <strong>📖 Instructions:</strong>
          <ol className="list-decimal list-inside mt-2 space-y-1">
            <li>Click "Trigger Index Creation" button above</li>
            <li>Open your browser's Developer Console (F12)</li>
            <li>Look for Firebase error messages with clickable links</li>
            <li>Click the links to automatically create the required indexes</li>
            <li>Wait for indexes to build (usually takes a few minutes)</li>
            <li>Refresh the Discover page to see it working with real data</li>
          </ol>
        </div>
      </div>
    </Card>
  );
};
