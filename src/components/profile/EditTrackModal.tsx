import React, { useState, useEffect } from 'react';
import { X, Save, Music, Tag, Clock, User as UserIcon } from 'lucide-react';
import { Card } from '../atoms/Card';
import { Button } from '../atoms/Button';
import { cn } from '../../utils/cn';
import { Track } from '../../types';
import { MusicService } from '../../services/musicService';
import { formatDuration } from '../../utils/audioUtils';
import { filterUndefinedValues } from '../../utils/firestoreHelpers';

interface EditTrackModalProps {
  isOpen: boolean;
  track: Track | null;
  onClose: () => void;
  onTrackUpdated: () => void;
}

export const EditTrackModal: React.FC<EditTrackModalProps> = ({
  isOpen,
  track,
  onClose,
  onTrackUpdated
}) => {
  const [formData, setFormData] = useState({
    title: '',
    artist: '',
    genre: '',
    mood: '',
    year: '',
    composer: '',
    bpm: '',
    key: ''
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Initialize form data when track changes
  useEffect(() => {
    if (track) {
      setFormData({
        title: track.title || '',
        artist: track.artist || '',
        genre: track.genre || '',
        mood: track.mood || '',
        year: track.year || '',
        composer: track.composer || '',
        bpm: track.bpm?.toString() || '',
        key: track.key || ''
      });
      setErrors({});
    }
  }, [track]);

  if (!isOpen || !track) return null;

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!formData.artist.trim()) {
      newErrors.artist = 'Artist is required';
    }

    if (formData.bpm && (isNaN(Number(formData.bpm)) || Number(formData.bpm) <= 0)) {
      newErrors.bpm = 'BPM must be a positive number';
    }

    if (formData.year && (isNaN(Number(formData.year)) || Number(formData.year) < 1900 || Number(formData.year) > new Date().getFullYear() + 1)) {
      newErrors.year = 'Please enter a valid year';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      // Create updates object with all fields
      const allUpdates: Partial<Track> = {
        title: formData.title.trim(),
        artist: formData.artist.trim(),
        genre: formData.genre.trim() || undefined,
        mood: formData.mood.trim() || undefined,
        year: formData.year.trim() || undefined,
        composer: formData.composer.trim() || undefined,
        bpm: formData.bpm ? Number(formData.bpm) : undefined,
        key: formData.key.trim() || undefined
      };

      // Filter out undefined values to avoid Firestore errors
      const updates = filterUndefinedValues(allUpdates);

      await MusicService.updateTrack(track.id, updates);
      onTrackUpdated();
    } catch (error: any) {
      console.error('Failed to update track:', error);
      setErrors({ general: error.message || 'Failed to update track' });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4" style={{ zIndex: 10000 }}>
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto" variant="glass">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                <Music className="w-5 h-5 text-primary" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-foreground">Edit Track</h2>
                <p className="text-sm text-muted-foreground">Update track information</p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="h-8 w-8 p-0"
              disabled={loading}
            >
              <X className="w-4 h-4" />
            </Button>
          </div>

          {/* Track Info */}
          <div className="mb-6 p-4 bg-secondary/10 rounded-lg">
            <div className="flex items-center space-x-3">
              {track.coverUrl ? (
                <img
                  src={track.coverUrl}
                  alt={track.title}
                  className="w-12 h-12 rounded-lg object-cover"
                />
              ) : (
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                  <Music className="w-6 h-6 text-white" />
                </div>
              )}
              <div>
                <p className="font-medium text-foreground">{track.title}</p>
                <p className="text-sm text-muted-foreground">{track.artist}</p>
                <div className="flex items-center space-x-4 text-xs text-muted-foreground mt-1">
                  <span className="flex items-center">
                    <Clock className="w-3 h-3 mr-1" />
                    {formatDuration(track.duration)}
                  </span>
                  <span className="capitalize">{track.status}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Form */}
          <div className="space-y-4">
            {/* Title and Artist - Required fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Title <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  className={cn(
                    'w-full px-3 py-2 border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2',
                    errors.title 
                      ? 'border-red-500 focus:ring-red-500/20' 
                      : 'border-border focus:ring-primary/20'
                  )}
                  placeholder="Enter track title"
                  disabled={loading}
                />
                {errors.title && (
                  <p className="text-sm text-red-500 mt-1">{errors.title}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Artist <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.artist}
                  onChange={(e) => handleInputChange('artist', e.target.value)}
                  className={cn(
                    'w-full px-3 py-2 border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2',
                    errors.artist 
                      ? 'border-red-500 focus:ring-red-500/20' 
                      : 'border-border focus:ring-primary/20'
                  )}
                  placeholder="Enter artist name"
                  disabled={loading}
                />
                {errors.artist && (
                  <p className="text-sm text-red-500 mt-1">{errors.artist}</p>
                )}
              </div>
            </div>

            {/* Genre and Mood */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Genre
                </label>
                <input
                  type="text"
                  value={formData.genre}
                  onChange={(e) => handleInputChange('genre', e.target.value)}
                  className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary/20"
                  placeholder="e.g., Electronic, Rock, Jazz"
                  disabled={loading}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Mood
                </label>
                <input
                  type="text"
                  value={formData.mood}
                  onChange={(e) => handleInputChange('mood', e.target.value)}
                  className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary/20"
                  placeholder="e.g., Energetic, Chill, Upbeat"
                  disabled={loading}
                />
              </div>
            </div>

            {/* Year and Composer */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Year
                </label>
                <input
                  type="text"
                  value={formData.year}
                  onChange={(e) => handleInputChange('year', e.target.value)}
                  className={cn(
                    'w-full px-3 py-2 border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2',
                    errors.year 
                      ? 'border-red-500 focus:ring-red-500/20' 
                      : 'border-border focus:ring-primary/20'
                  )}
                  placeholder="e.g., 2024"
                  disabled={loading}
                />
                {errors.year && (
                  <p className="text-sm text-red-500 mt-1">{errors.year}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Composer
                </label>
                <input
                  type="text"
                  value={formData.composer}
                  onChange={(e) => handleInputChange('composer', e.target.value)}
                  className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary/20"
                  placeholder="Enter composer name"
                  disabled={loading}
                />
              </div>
            </div>

            {/* BPM and Key */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  BPM
                </label>
                <input
                  type="text"
                  value={formData.bpm}
                  onChange={(e) => handleInputChange('bpm', e.target.value)}
                  className={cn(
                    'w-full px-3 py-2 border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2',
                    errors.bpm 
                      ? 'border-red-500 focus:ring-red-500/20' 
                      : 'border-border focus:ring-primary/20'
                  )}
                  placeholder="e.g., 120"
                  disabled={loading}
                />
                {errors.bpm && (
                  <p className="text-sm text-red-500 mt-1">{errors.bpm}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Key
                </label>
                <input
                  type="text"
                  value={formData.key}
                  onChange={(e) => handleInputChange('key', e.target.value)}
                  className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary/20"
                  placeholder="e.g., C Major, A Minor"
                  disabled={loading}
                />
              </div>
            </div>

            {/* Error Message */}
            {errors.general && (
              <div className="p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
                <p className="text-sm text-red-500">{errors.general}</p>
              </div>
            )}

            {/* Actions */}
            <div className="flex items-center justify-end space-x-3 pt-4">
              <Button
                variant="outline"
                onClick={handleClose}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={handleSave}
                disabled={loading}
                className="flex items-center space-x-2"
              >
                <Save className="w-4 h-4" />
                <span>{loading ? 'Saving...' : 'Save Changes'}</span>
              </Button>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};
