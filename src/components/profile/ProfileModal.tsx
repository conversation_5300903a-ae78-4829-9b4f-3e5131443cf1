import React, { useState } from 'react';
import { X, User, Mail, Music, Calendar, Edit3, Save, Camera } from 'lucide-react';
import { Card } from '../atoms/Card';
import { Button } from '../atoms/Button';
import { cn } from '../../utils/cn';

interface ProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const ProfileModal: React.FC<ProfileModalProps> = ({
  isOpen,
  onClose
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [profileData, setProfileData] = useState({
    displayName: 'Music Lover',
    email: '<EMAIL>',
    bio: 'Passionate about electronic music and discovering new artists. Always vibing to the latest beats!',
    location: 'San Francisco, CA',
    joinedDate: 'January 2024',
    tracksUploaded: 12,
    playlistsCreated: 8,
    followers: 156,
    following: 89
  });

  if (!isOpen) return null;

  const handleSave = () => {
    // TODO: Implement profile update
    console.log('Saving profile:', profileData);
    setIsEditing(false);
  };

  const handleInputChange = (field: string, value: string) => {
    setProfileData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4" style={{ zIndex: 10000 }}>
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto" variant="glass">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-foreground">Profile</h2>
            <div className="flex items-center space-x-2">
              {isEditing ? (
                <>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsEditing(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={handleSave}
                  >
                    <Save className="w-4 h-4 mr-2" />
                    Save
                  </Button>
                </>
              ) : (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsEditing(true)}
                >
                  <Edit3 className="w-4 h-4 mr-2" />
                  Edit
                </Button>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="h-8 w-8 p-0"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Profile Content */}
          <div className="space-y-6">
            {/* Avatar and Basic Info */}
            <div className="flex items-start space-x-6">
              <div className="relative">
                <div className="w-24 h-24 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full flex items-center justify-center">
                  <User className="w-12 h-12 text-white" />
                </div>
                {isEditing && (
                  <button className="absolute -bottom-2 -right-2 w-8 h-8 bg-primary rounded-full flex items-center justify-center shadow-lg hover:bg-primary/90 transition-colors">
                    <Camera className="w-4 h-4 text-white" />
                  </button>
                )}
              </div>

              <div className="flex-1 space-y-3">
                {isEditing ? (
                  <input
                    type="text"
                    value={profileData.displayName}
                    onChange={(e) => handleInputChange('displayName', e.target.value)}
                    className="text-2xl font-bold bg-transparent border-b border-border/20 focus:border-primary/40 focus:outline-none text-foreground w-full"
                  />
                ) : (
                  <h3 className="text-2xl font-bold text-foreground">{profileData.displayName}</h3>
                )}

                <div className="flex items-center space-x-2 text-muted-foreground">
                  <Mail className="w-4 h-4" />
                  <span className="text-sm">{profileData.email}</span>
                </div>

                <div className="flex items-center space-x-2 text-muted-foreground">
                  <Calendar className="w-4 h-4" />
                  <span className="text-sm">Joined {profileData.joinedDate}</span>
                </div>
              </div>
            </div>

            {/* Bio */}
            <div>
              <h4 className="text-sm font-semibold text-foreground mb-2">Bio</h4>
              {isEditing ? (
                <textarea
                  value={profileData.bio}
                  onChange={(e) => handleInputChange('bio', e.target.value)}
                  className="w-full p-3 bg-secondary/20 border border-border/20 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary/40 text-foreground resize-none"
                  rows={3}
                  placeholder="Tell us about yourself..."
                />
              ) : (
                <p className="text-sm text-muted-foreground">{profileData.bio}</p>
              )}
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-3 bg-secondary/20 rounded-xl">
                <div className="text-xl font-bold text-foreground">{profileData.tracksUploaded}</div>
                <div className="text-xs text-muted-foreground">Tracks</div>
              </div>
              <div className="text-center p-3 bg-secondary/20 rounded-xl">
                <div className="text-xl font-bold text-foreground">{profileData.playlistsCreated}</div>
                <div className="text-xs text-muted-foreground">Playlists</div>
              </div>
              <div className="text-center p-3 bg-secondary/20 rounded-xl">
                <div className="text-xl font-bold text-foreground">{profileData.followers}</div>
                <div className="text-xs text-muted-foreground">Followers</div>
              </div>
              <div className="text-center p-3 bg-secondary/20 rounded-xl">
                <div className="text-xl font-bold text-foreground">{profileData.following}</div>
                <div className="text-xs text-muted-foreground">Following</div>
              </div>
            </div>

            {/* Recent Activity */}
            <div>
              <h4 className="text-sm font-semibold text-foreground mb-3">Recent Activity</h4>
              <div className="space-y-2">
                {[
                  { action: 'Uploaded', item: '"Midnight Dreams"', time: '2 hours ago' },
                  { action: 'Created playlist', item: '"Chill Vibes"', time: '1 day ago' },
                  { action: 'Liked', item: '"Electric Nights"', time: '3 days ago' }
                ].map((activity, i) => (
                  <div key={i} className="flex items-center justify-between p-2 rounded-lg hover:bg-secondary/20 transition-colors">
                    <div className="flex items-center space-x-3">
                      <Music className="w-4 h-4 text-primary" />
                      <span className="text-sm text-foreground">
                        {activity.action} <span className="font-medium">{activity.item}</span>
                      </span>
                    </div>
                    <span className="text-xs text-muted-foreground">{activity.time}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};