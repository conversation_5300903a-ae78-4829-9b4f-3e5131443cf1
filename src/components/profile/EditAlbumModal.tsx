import React, { useState, useEffect } from 'react';
import { X, Save, Disc, Tag, FileText, User as UserIcon } from 'lucide-react';
import { Card } from '../atoms/Card';
import { Button } from '../atoms/Button';
import { cn } from '../../utils/cn';
import { Album } from '../../types';
import { AlbumService } from '../../services/albumService';
import { filterUndefinedValues } from '../../utils/firestoreHelpers';

interface EditAlbumModalProps {
  isOpen: boolean;
  album: Album | null;
  onClose: () => void;
  onAlbumUpdated: () => void;
}

export const EditAlbumModal: React.FC<EditAlbumModalProps> = ({
  isOpen,
  album,
  onClose,
  onAlbumUpdated
}) => {
  const [formData, setFormData] = useState({
    title: '',
    artist: '',
    description: '',
    genre: ''
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Initialize form data when album changes
  useEffect(() => {
    if (album) {
      setFormData({
        title: album.title || '',
        artist: album.artist || '',
        description: album.description || '',
        genre: album.genre || ''
      });
      setErrors({});
    }
  }, [album]);

  if (!isOpen || !album) return null;

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!formData.artist.trim()) {
      newErrors.artist = 'Artist is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      // Create updates object with all fields
      const allUpdates: Partial<Album> = {
        title: formData.title.trim(),
        artist: formData.artist.trim(),
        description: formData.description.trim() || undefined,
        genre: formData.genre.trim() || undefined
      };

      // Filter out undefined values to avoid Firestore errors
      const updates = filterUndefinedValues(allUpdates);

      await AlbumService.updateAlbum(album.id, updates);
      onAlbumUpdated();
    } catch (error: any) {
      console.error('Failed to update album:', error);
      setErrors({ general: error.message || 'Failed to update album' });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4" style={{ zIndex: 10000 }}>
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto" variant="glass">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-purple-500/10 rounded-lg flex items-center justify-center">
                <Disc className="w-5 h-5 text-purple-500" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-foreground">Edit Album</h2>
                <p className="text-sm text-muted-foreground">Update album information</p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="h-8 w-8 p-0"
              disabled={loading}
            >
              <X className="w-4 h-4" />
            </Button>
          </div>

          {/* Album Info */}
          <div className="mb-6 p-4 bg-secondary/10 rounded-lg">
            <div className="flex items-center space-x-3">
              {album.coverUrl ? (
                <img
                  src={album.coverUrl}
                  alt={album.title}
                  className="w-16 h-16 rounded-lg object-cover"
                />
              ) : (
                <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                  <Disc className="w-8 h-8 text-white" />
                </div>
              )}
              <div>
                <p className="font-medium text-foreground">{album.title}</p>
                <p className="text-sm text-muted-foreground">{album.artist}</p>
                <div className="flex items-center space-x-4 text-xs text-muted-foreground mt-1">
                  <span>{album.trackCount || 0} tracks</span>
                  <span className="capitalize">{album.status}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Form */}
          <div className="space-y-4">
            {/* Title and Artist - Required fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Album Title <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  className={cn(
                    'w-full px-3 py-2 border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2',
                    errors.title 
                      ? 'border-red-500 focus:ring-red-500/20' 
                      : 'border-border focus:ring-primary/20'
                  )}
                  placeholder="Enter album title"
                  disabled={loading}
                />
                {errors.title && (
                  <p className="text-sm text-red-500 mt-1">{errors.title}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Artist <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.artist}
                  onChange={(e) => handleInputChange('artist', e.target.value)}
                  className={cn(
                    'w-full px-3 py-2 border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2',
                    errors.artist 
                      ? 'border-red-500 focus:ring-red-500/20' 
                      : 'border-border focus:ring-primary/20'
                  )}
                  placeholder="Enter artist name"
                  disabled={loading}
                />
                {errors.artist && (
                  <p className="text-sm text-red-500 mt-1">{errors.artist}</p>
                )}
              </div>
            </div>

            {/* Genre */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Genre
              </label>
              <input
                type="text"
                value={formData.genre}
                onChange={(e) => handleInputChange('genre', e.target.value)}
                className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary/20"
                placeholder="e.g., Electronic, Rock, Jazz"
                disabled={loading}
              />
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Description
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary/20 resize-none"
                rows={4}
                placeholder="Describe your album..."
                disabled={loading}
              />
            </div>

            {/* Error Message */}
            {errors.general && (
              <div className="p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
                <p className="text-sm text-red-500">{errors.general}</p>
              </div>
            )}

            {/* Actions */}
            <div className="flex items-center justify-end space-x-3 pt-4">
              <Button
                variant="outline"
                onClick={handleClose}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={handleSave}
                disabled={loading}
                className="flex items-center space-x-2"
              >
                <Save className="w-4 h-4" />
                <span>{loading ? 'Saving...' : 'Save Changes'}</span>
              </Button>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};
