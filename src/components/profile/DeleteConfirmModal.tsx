import React, { useState, useEffect } from 'react';
import { X, Trash2, AlertTriangle, Music, Disc, Wifi, WifiOff } from 'lucide-react';
import { Card } from '../atoms/Card';
import { Button } from '../atoms/Button';
import { cn } from '../../utils/cn';
import { Track, Album } from '../../types';
import { MusicService } from '../../services/musicService';
import { AlbumService } from '../../services/albumService';
import { formatDuration } from '../../utils/audioUtils';

interface DeleteConfirmModalProps {
  isOpen: boolean;
  type: 'track' | 'album' | null;
  item: Track | Album | null;
  onClose: () => void;
  onDeleted: () => void;
}

export const DeleteConfirmModal: React.FC<DeleteConfirmModalProps> = ({
  isOpen,
  type,
  item,
  onClose,
  onDeleted
}) => {
  const [loading, setLoading] = useState(false);
  const [deleteAlbumTracks, setDeleteAlbumTracks] = useState(false);
  const [error, setError] = useState('');
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  // Monitor online status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  if (!isOpen || !type || !item) return null;

  const isTrack = type === 'track';
  const track = isTrack ? item as Track : null;
  const album = !isTrack ? item as Album : null;

  const handleDelete = async () => {
    // Check online status before attempting delete
    if (!isOnline) {
      setError('Cannot delete while offline. Please check your internet connection and try again.');
      return;
    }

    setLoading(true);
    setError('');

    try {
      if (isTrack && track) {
        await MusicService.deleteTrack(track.id, track.uploadedBy);
      } else if (album) {
        await AlbumService.deleteAlbum(album.id, deleteAlbumTracks);
      }
      onDeleted();
    } catch (error: any) {
      console.error(`Failed to delete ${type}:`, error);
      setError(error.message || `Failed to delete ${type}`);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setError('');
      setDeleteAlbumTracks(false);
      onClose();
    }
  };

  const handleRetry = () => {
    setError('');
    handleDelete();
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4" style={{ zIndex: 10000 }}>
      <Card className="w-full max-w-md" variant="glass">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-red-500/10 rounded-lg flex items-center justify-center">
                <Trash2 className="w-5 h-5 text-red-500" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-foreground">Delete {isTrack ? 'Track' : 'Album'}</h2>
                <p className="text-sm text-muted-foreground">This action cannot be undone</p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="h-8 w-8 p-0"
              disabled={loading}
            >
              <X className="w-4 h-4" />
            </Button>
          </div>

          {/* Warning */}
          <div className="mb-6 p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-red-500 mb-1">
                  Warning: This action is permanent
                </p>
                <p className="text-sm text-red-500/80">
                  {isTrack 
                    ? 'This track will be permanently deleted from your library and cannot be recovered.'
                    : 'This album will be permanently deleted from your library and cannot be recovered.'
                  }
                </p>
              </div>
            </div>
          </div>

          {/* Item Info */}
          <div className="mb-6 p-4 bg-secondary/10 rounded-lg">
            <div className="flex items-center space-x-3">
              {isTrack ? (
                <>
                  {track?.coverUrl ? (
                    <img
                      src={track.coverUrl}
                      alt={track.title}
                      className="w-12 h-12 rounded-lg object-cover"
                    />
                  ) : (
                    <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                      <Music className="w-6 h-6 text-white" />
                    </div>
                  )}
                  <div>
                    <p className="font-medium text-foreground">{track?.title}</p>
                    <p className="text-sm text-muted-foreground">{track?.artist}</p>
                    {track?.duration && (
                      <p className="text-xs text-muted-foreground">
                        {formatDuration(track.duration)}
                      </p>
                    )}
                  </div>
                </>
              ) : (
                <>
                  {album?.coverUrl ? (
                    <img
                      src={album.coverUrl}
                      alt={album.title}
                      className="w-12 h-12 rounded-lg object-cover"
                    />
                  ) : (
                    <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                      <Disc className="w-6 h-6 text-white" />
                    </div>
                  )}
                  <div>
                    <p className="font-medium text-foreground">{album?.title}</p>
                    <p className="text-sm text-muted-foreground">{album?.artist}</p>
                    <p className="text-xs text-muted-foreground">
                      {album?.trackCount || 0} tracks
                    </p>
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Album-specific options */}
          {!isTrack && album && (
            <div className="mb-6">
              <label className="flex items-start space-x-3 cursor-pointer">
                <input
                  type="checkbox"
                  checked={deleteAlbumTracks}
                  onChange={(e) => setDeleteAlbumTracks(e.target.checked)}
                  className="mt-1 w-4 h-4 text-red-500 border-border rounded focus:ring-red-500/20"
                  disabled={loading}
                />
                <div>
                  <p className="text-sm font-medium text-foreground">
                    Also delete all tracks in this album
                  </p>
                  <p className="text-xs text-muted-foreground">
                    If unchecked, tracks will remain as singles in your library
                  </p>
                </div>
              </label>
            </div>
          )}

          {/* Offline Warning */}
          {!isOnline && (
            <div className="mb-4 p-3 bg-orange-500/10 border border-orange-500/20 rounded-lg">
              <div className="flex items-center space-x-2">
                <WifiOff className="w-4 h-4 text-orange-500" />
                <p className="text-sm text-orange-500">
                  You're currently offline. Connect to the internet to delete items.
                </p>
              </div>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
              <div className="flex items-start space-x-2">
                <AlertTriangle className="w-4 h-4 text-red-500 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="text-sm text-red-500 font-medium">Delete Failed</p>
                  <p className="text-sm text-red-500/80">{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center justify-end space-x-3">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={loading}
            >
              Cancel
            </Button>

            {/* Show retry button if there's an error and we're online */}
            {error && isOnline && !loading && (
              <Button
                variant="outline"
                onClick={handleRetry}
                className="flex items-center space-x-2"
              >
                <Wifi className="w-4 h-4" />
                <span>Retry</span>
              </Button>
            )}

            <Button
              variant="primary"
              onClick={handleDelete}
              disabled={loading || !isOnline}
              className={cn(
                "flex items-center space-x-2 text-white",
                !isOnline
                  ? "bg-gray-500 cursor-not-allowed"
                  : "bg-red-500 hover:bg-red-600"
              )}
            >
              {!isOnline ? (
                <WifiOff className="w-4 h-4" />
              ) : (
                <Trash2 className="w-4 h-4" />
              )}
              <span>
                {loading
                  ? 'Deleting...'
                  : !isOnline
                    ? 'Offline'
                    : `Delete ${isTrack ? 'Track' : 'Album'}`
                }
              </span>
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};
