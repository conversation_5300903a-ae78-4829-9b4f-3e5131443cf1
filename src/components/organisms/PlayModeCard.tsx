import React, { useState } from 'react';
import { Play, Pause, List, Eye, MoreHorizontal, Maximize, Minimize, Share, Plus, Disc, Heart, User, Album, CreditCard, Link, ChevronDown, SkipBack, SkipForward } from 'lucide-react';
import { Card } from '../atoms/Card';
import { Button } from '../atoms/Button';
import { OverflowMenu } from '../atoms/OverflowMenu';
import { TrackActions } from '../molecules/TrackActions';
import { AddToPlaylistModal } from '../playlist/AddToPlaylistModal';
import { cn } from '../../utils/cn';
import { useMusicStore } from '../../store/musicStore';
import { AudioService } from '../../services/audioService';
import { useAudioAnalyzer } from '../../hooks/useAudioAnalyzer';
import { useAuth } from '../../hooks/useAuth';
import { EngagementService } from '../../services/engagementService';
import { ShareService } from '../../services/shareService';
import { usePlayModeContext } from '../../hooks/usePlayModeContext';
import { AlbumService } from '../../services/albumService';
import { MusicService } from '../../services/musicService';

type PlayModeTab = 'play' | 'queue' | 'playlist';
type VisualizationType = 'album' | 'waveform' | 'spectrum';

// Helper function to format time
const formatTime = (seconds: number): string => {
  if (!seconds || isNaN(seconds)) return '0:00';
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins}:${secs.toString().padStart(2, '0')}`;
};

interface PlayModeCardProps {
  // Optional props for external control of contextual tabs
  onContextualTabChange?: (type: 'album' | 'playlist' | 'liked' | null, label: string, data?: any) => void;
  // Optional collapse handler for mobile expanded view
  onCollapse?: () => void;
  // Flag to indicate mobile context for styling adjustments
  isMobile?: boolean;
}

export const PlayModeCard: React.FC<PlayModeCardProps> = ({ onContextualTabChange, onCollapse, isMobile = false }) => {
  const { user } = useAuth();

  // Use shared state for contextual tabs
  const { contextualTab, activeTab, setActiveTab, activateContextualTab, clearContextualTab } = usePlayModeContext();

  const [activeVisualization, setActiveVisualization] = useState<VisualizationType>('album');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showAddToPlaylist, setShowAddToPlaylist] = useState(false);
  const [selectedTrack, setSelectedTrack] = useState<any>(null);
  const [likedTracks, setLikedTracks] = useState<Set<string>>(new Set());
  const [likingTrack, setLikingTrack] = useState<string | null>(null);

  // State for contextual tab tracks
  const [contextualTracks, setContextualTracks] = useState<any[]>([]);
  const [contextualTracksLoading, setContextualTracksLoading] = useState(false);

  // Audio analyzer for real-time visualizations
  const { waveformData, isAnalyzing } = useAudioAnalyzer();

  // Fullscreen API functions
  const enterFullscreen = async () => {
    try {
      await document.documentElement.requestFullscreen();
      setIsFullscreen(true);
    } catch (error) {
      console.error('Error entering fullscreen:', error);
    }
  };

  const exitFullscreen = async () => {
    try {
      await document.exitFullscreen();
      setIsFullscreen(false);
    } catch (error) {
      console.error('Error exiting fullscreen:', error);
    }
  };

  // Listen for fullscreen changes
  React.useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  // Get real music data from the store only (don't use useAudioPlayer to avoid conflicts)
  const {
    currentTrack,
    isPlaying,
    currentTime,
    duration,
    queue,
    currentAlbum,
    setCurrentTrack,
    togglePlay,
    setCurrentTime,
    updateTrackLikeCount
  } = useMusicStore();

  // Format time helper with better validation
  const formatTime = (seconds: number) => {
    // Handle edge cases and invalid inputs
    if (!seconds || seconds <= 0 || !isFinite(seconds)) {
      return '0:00';
    }

    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Seek functionality using AudioService directly
  const handleSeek = (time: number) => {
    AudioService.seek(time);
    setCurrentTime(time);
  };

  // Handle add to playlist modal
  const handleAddToPlaylist = (track: any) => {
    setSelectedTrack(track);
    setShowAddToPlaylist(true);
  };

  // Load liked tracks on mount and when user changes
  React.useEffect(() => {
    if (user) {
      loadLikedTracks();
    } else {
      setLikedTracks(new Set());
    }
  }, [user]);

  // Reload liked tracks when queue changes (when new album/playlist is loaded)
  React.useEffect(() => {
    if (user && queue.length > 0) {
      loadLikedTracks();
    }
  }, [user, queue.map(t => t.id).join(',')]); // Trigger when track IDs in queue change

  // Load contextual tracks when contextual tab changes
  React.useEffect(() => {
    loadContextualTracks();
  }, [contextualTab.type, contextualTab.data, user]);

  const loadContextualTracks = async () => {
    if (!contextualTab.type || !user) {
      setContextualTracks([]);
      return;
    }

    setContextualTracksLoading(true);
    try {
      let tracks: any[] = [];

      switch (contextualTab.type) {
        case 'album':
          if (contextualTab.data?.id) {
            const result = await AlbumService.getAlbumWithTracks(contextualTab.data.id);
            if (result) {
              tracks = result.tracks;
            }
          }
          break;

        case 'playlist':
          if (contextualTab.data?.tracks) {
            // If playlist data includes track objects, use them directly
            tracks = contextualTab.data.tracks;
          } else if (contextualTab.data?.id) {
            // If we only have playlist ID, fetch the tracks
            const playlist = await MusicService.getPlaylistById(contextualTab.data.id);
            if (playlist?.tracks) {
              tracks = playlist.tracks;
            }
          }
          break;

        case 'liked':
          const likedTrackIds = await EngagementService.getUserLikedTracks(user.id);
          if (likedTrackIds.length > 0) {
            // Load all liked tracks
            const trackPromises = likedTrackIds.map(async (trackId) => {
              try {
                return await MusicService.getTrackById(trackId);
              } catch (error) {
                console.error(`Failed to load liked track ${trackId}:`, error);
                return null;
              }
            });
            const trackResults = await Promise.all(trackPromises);
            tracks = trackResults.filter(track => track !== null);
          }
          break;
      }

      setContextualTracks(tracks);
    } catch (error) {
      console.error('Failed to load contextual tracks:', error);
      setContextualTracks([]);
    } finally {
      setContextualTracksLoading(false);
    }
  };

  const loadLikedTracks = async () => {
    if (!user) return;

    try {
      const likedTrackIds = await EngagementService.getUserLikedTracks(user.id);
      setLikedTracks(new Set(likedTrackIds));
    } catch (error) {
      console.error('Failed to load liked tracks:', error);
    }
  };

  // Handle like/unlike track
  const handleLikeTrack = async (track: any, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent track play

    if (!user || likingTrack === track.id) return;

    setLikingTrack(track.id);
    try {
      const isLiked = likedTracks.has(track.id);

      if (isLiked) {
        await EngagementService.unlikeItem(track.id, 'track', user.id);
        setLikedTracks(prev => {
          const newSet = new Set(prev);
          newSet.delete(track.id);
          return newSet;
        });
        // Update local like count immediately
        updateTrackLikeCount(track.id, false);
      } else {
        await EngagementService.likeItem(track.id, 'track', user.id);
        setLikedTracks(prev => new Set(prev).add(track.id));
        // Update local like count immediately
        updateTrackLikeCount(track.id, true);
      }
    } catch (error) {
      console.error('Failed to toggle like:', error);
    } finally {
      setLikingTrack(null);
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'play':
        return (
          <div className="flex-1 flex flex-col">
            {/* Visualization Switcher */}
            <div className="flex-shrink-0 px-6 pt-4 pb-2">
              <div className="flex items-center justify-center space-x-2">
                <Button
                  variant={activeVisualization === 'album' ? 'primary' : 'ghost'}
                  size="sm"
                  onClick={() => setActiveVisualization('album')}
                  className="h-8 px-3 text-xs"
                >
                  Album
                </Button>
                <Button
                  variant={activeVisualization === 'waveform' ? 'primary' : 'ghost'}
                  size="sm"
                  onClick={() => setActiveVisualization('waveform')}
                  className="h-8 px-3 text-xs"
                >
                  Waveform
                </Button>
                <Button
                  variant={activeVisualization === 'spectrum' ? 'primary' : 'ghost'}
                  size="sm"
                  onClick={() => setActiveVisualization('spectrum')}
                  className="h-8 px-3 text-xs"
                >
                  Spectrum
                </Button>
              </div>
            </div>

            {/* Visualization Content */}
            <div className="flex-1 flex items-center justify-center px-6">
              {activeVisualization === 'album' && (
                <div className="text-center space-y-6 h-full flex flex-col items-center justify-center">
                  {/* Album Art - Larger in fullscreen */}
                  <div className={cn(
                    "mx-auto rounded-2xl overflow-hidden shadow-2xl",
                    isFullscreen ? "w-96 h-96" :
                    isMobile ? "w-full max-w-xs sm:max-w-sm aspect-square" : "w-64 h-64"
                  )}>
                    {currentTrack?.coverUrl || currentAlbum?.coverUrl ? (
                      <img
                        src={currentTrack?.coverUrl || currentAlbum?.coverUrl}
                        alt={currentTrack?.title || currentAlbum?.title}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-purple-400 to-pink-400 flex items-center justify-center">
                        <span className={cn(
                          "text-white",
                          isFullscreen ? "text-8xl" : isMobile ? "text-4xl sm:text-5xl" : "text-6xl"
                        )}>🎵</span>
                      </div>
                    )}
                  </div>

                  {/* Track Info - Compact spacing for mobile */}
                  <div className={cn("space-y-3", isMobile && "space-y-2")}>
                    <h2 className={cn(
                      "font-bold text-center",
                      isFullscreen ? "text-4xl text-white" :
                      isMobile ? "text-xl text-foreground" : "text-2xl text-foreground"
                    )}>
                      {currentTrack?.title || 'No track selected'}
                    </h2>
                    {isMobile ? (
                      // Mobile: Artist and Album aligned with song title edges
                      <div className="flex items-center justify-between py-2">
                        <p className="text-base text-muted-foreground flex-1 min-w-0 text-left">
                          {currentTrack?.artist || 'Choose a song to play'}
                          {currentTrack?.albumTitle && (
                            <span className="text-muted-foreground/80"> • {currentTrack.albumTitle}</span>
                          )}
                        </p>
                        <div className="flex items-center space-x-3 flex-shrink-0">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => currentTrack && handleLikeTrack(currentTrack, e)}
                            className="h-10 w-10 p-0 hover:bg-red-500/10"
                            disabled={!currentTrack || likingTrack === currentTrack?.id}
                          >
                            <Heart className={cn(
                              "w-5 h-5 transition-colors",
                              currentTrack && likedTracks.has(currentTrack.id)
                                ? "text-red-500 fill-red-500"
                                : "text-muted-foreground hover:text-red-500"
                            )} />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-10 w-10 p-0 hover:bg-foreground/10"
                          >
                            <MoreHorizontal className="w-5 h-5" />
                          </Button>
                        </div>
                      </div>
                    ) : (
                      // Desktop: Separate lines
                      <>
                        <p className={cn(
                          isFullscreen ? "text-2xl text-white/80" : "text-lg text-muted-foreground"
                        )}>
                          {currentTrack?.artist || 'Choose a song to play'}
                        </p>
                        <p className={cn(
                          isFullscreen ? "text-lg text-white/60" : "text-sm text-muted-foreground/80"
                        )}>
                          {currentTrack?.albumTitle || ''}
                        </p>
                      </>
                    )}
                  </div>

                  {/* Mobile Playback Controls - Ultra compact for small screens */}
                  {isMobile && currentTrack && (
                    <div className="space-y-3 mt-3">
                      {/* Progress Bar aligned with artist text and button edges */}
                      <div className="space-y-1.5">
                        {/* Progress bar container with negative margins to align with text/button edges */}
                        <div className="relative h-1 bg-foreground/20 rounded-full -mx-2.5">
                          <div
                            className="absolute top-0 left-0 h-full bg-primary rounded-full transition-all duration-300"
                            style={{ width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%` }}
                          />
                        </div>
                        {/* Time stamps aligned with progress bar edges */}
                        <div className="flex justify-between text-xs text-muted-foreground -mx-2.5">
                          <span>{formatTime(currentTime)}</span>
                          <span>{formatTime(duration)}</span>
                        </div>
                      </div>

                      {/* Playback Controls - Ultra compact for small screens */}
                      <div className="flex items-center justify-center space-x-8 py-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            const currentIndex = queue.findIndex(track => track.id === currentTrack?.id);
                            if (currentIndex > 0) {
                              setCurrentTrack(queue[currentIndex - 1]);
                            }
                          }}
                          className="h-10 w-10 p-0 rounded-full hover:bg-foreground/10"
                          disabled={!queue.length || queue.findIndex(track => track.id === currentTrack?.id) <= 0}
                        >
                          <SkipBack className="w-5 h-5" />
                        </Button>

                        <Button
                          variant="ghost"
                          size="lg"
                          onClick={togglePlay}
                          className="h-14 w-14 p-0 rounded-full bg-white text-black hover:bg-white/90 shadow-lg"
                        >
                          {isPlaying ? (
                            <Pause className="w-7 h-7" />
                          ) : (
                            <Play className="w-7 h-7 ml-0.5" />
                          )}
                        </Button>

                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            const currentIndex = queue.findIndex(track => track.id === currentTrack?.id);
                            if (currentIndex < queue.length - 1) {
                              setCurrentTrack(queue[currentIndex + 1]);
                            }
                          }}
                          className="h-10 w-10 p-0 rounded-full hover:bg-foreground/10"
                          disabled={!queue.length || queue.findIndex(track => track.id === currentTrack?.id) >= queue.length - 1}
                        >
                          <SkipForward className="w-5 h-5" />
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {activeVisualization === 'waveform' && (
                <div className="w-full h-full flex items-center justify-center">
                  <div className="w-full max-w-6xl">
                    {/* Real Waveform Visualization - Responsive to fullscreen */}
                    <div className={cn(
                      "bg-gradient-to-r from-purple-900/20 to-pink-900/20 rounded-2xl flex items-center justify-center overflow-hidden",
                      isFullscreen ? "h-96" : "h-64"
                    )}>
                      <div className={cn(
                        "flex items-center justify-center space-x-1",
                        isFullscreen ? "h-80" : "h-40"
                      )}>
                        {isAnalyzing && waveformData.length > 0 ? (
                          // Real audio waveform data
                          Array.from({ length: isFullscreen ? 120 : 80 }).map((_, i) => {
                            // Sample the waveform data evenly across available data points
                            const dataIndex = Math.floor((i / (isFullscreen ? 120 : 80)) * waveformData.length);
                            const amplitude = waveformData[dataIndex] || 128; // Default to center if no data

                            // Convert amplitude (0-255) to height percentage
                            // 128 is center (silence), so we calculate deviation from center
                            const deviation = Math.abs(amplitude - 128) / 128;
                            const maxHeight = isFullscreen ? 240 : 120;
                            const minHeight = isFullscreen ? 20 : 10;
                            const height = minHeight + (deviation * (maxHeight - minHeight));

                            return (
                              <div
                                key={`waveform-${i}`}
                                className="bg-gradient-to-t from-purple-500 to-pink-500 rounded-full transition-all duration-75"
                                style={{
                                  width: isFullscreen ? '6px' : '4px',
                                  height: `${height}px`,
                                }}
                              />
                            );
                          })
                        ) : (
                          // Fallback bars when no audio data is available
                          Array.from({ length: isFullscreen ? 120 : 80 }).map((_, i) => (
                            <div
                              key={i}
                              className="bg-gradient-to-t from-purple-500/30 to-pink-500/30 rounded-full"
                              style={{
                                width: isFullscreen ? '6px' : '4px',
                                height: `${isFullscreen ? 40 : 20}px`,
                              }}
                            />
                          ))
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeVisualization === 'spectrum' && (
                <div className="w-full h-full flex items-center justify-center">
                  <div className="w-full max-w-2xl">
                    {/* Pure Spectrum Analyzer - Responsive to fullscreen */}
                    <div className="relative">
                      {/* Circular Spectrum */}
                      <div className={cn(
                        "mx-auto relative",
                        isFullscreen ? "w-[600px] h-[600px]" : "w-96 h-96"
                      )}>
                        <div className="absolute inset-0 rounded-full bg-gradient-to-br from-purple-500/10 to-pink-500/10 animate-spin-slow">
                          {Array.from({ length: isFullscreen ? 64 : 48 }).map((_, i) => {
                            const radius = isFullscreen ? 300 : 192;
                            const barWidth = isFullscreen ? 8 : 6;
                            const barHeight = Math.random() * (isFullscreen ? 100 : 60) + (isFullscreen ? 50 : 30);

                            return (
                              <div
                                key={`spectrum-${i}`}
                                className="absolute bg-gradient-to-t from-purple-500 to-pink-500 rounded-full animate-pulse"
                                style={{
                                  width: `${barWidth}px`,
                                  height: `${barHeight}px`,
                                  left: '50%',
                                  top: '50%',
                                  transformOrigin: `${barWidth/2}px ${radius}px`,
                                  transform: `rotate(${i * (360 / (isFullscreen ? 64 : 48))}deg) translateY(-${radius}px)`,
                                  animationDelay: `${i * (isFullscreen ? 60 : 80)}ms`,
                                  animationDuration: `${600 + Math.random() * 600}ms`
                                }}
                              />
                            );
                          })}
                        </div>

                        {/* Center Glow Effect */}
                        <div className={cn(
                          "absolute inset-1/2 -translate-x-1/2 -translate-y-1/2 rounded-full bg-gradient-to-br from-purple-500/30 to-pink-500/30 animate-pulse",
                          isFullscreen ? "w-32 h-32" : "w-24 h-24"
                        )}></div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        );
      
      case 'queue':
        return (
          <div className="flex-1 overflow-y-auto">
            {/* Spotify-Style Queue Interface */}
            <div className="px-6 py-4 space-y-6">

              {/* Now Playing Section */}
              {currentTrack && (
                <div className="space-y-3">
                  <h2 className="text-lg font-semibold text-foreground">Now playing</h2>
                  <div className="flex items-center space-x-4 p-4 rounded-xl bg-purple-600/10 border border-purple-600/20">
                    {/* Track Cover */}
                    <div className="w-16 h-16 rounded-lg overflow-hidden flex-shrink-0">
                      {currentTrack.coverUrl || currentAlbum?.coverUrl ? (
                        <img
                          src={currentTrack.coverUrl || currentAlbum?.coverUrl}
                          alt={currentTrack.title}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-purple-600 to-purple-800 flex items-center justify-center">
                          <span className="text-white text-lg">🎵</span>
                        </div>
                      )}
                    </div>

                    {/* Track Info */}
                    <div className="flex-1 min-w-0">
                      <p className="text-base font-medium text-foreground truncate">
                        {currentTrack.title}
                      </p>
                      <p className="text-sm text-muted-foreground truncate">
                        {currentTrack.artist}
                      </p>
                      {currentTrack.albumTitle && (
                        <p className="text-xs text-muted-foreground/80 truncate">
                          {currentTrack.albumTitle}
                        </p>
                      )}
                    </div>

                    {/* Playing Indicator */}
                    {isPlaying && (
                      <div className="flex items-center space-x-1">
                        <div className="w-1 h-4 bg-purple-500 rounded-full animate-pulse"></div>
                        <div className="w-1 h-3 bg-purple-500 rounded-full animate-pulse delay-75"></div>
                        <div className="w-1 h-4 bg-purple-500 rounded-full animate-pulse delay-150"></div>
                      </div>
                    )}

                    {/* Like Button */}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-10 w-10 p-0"
                      onClick={(e) => handleLikeTrack(currentTrack, e)}
                      disabled={likingTrack === currentTrack.id}
                      title={likedTracks.has(currentTrack.id) ? "Unlike this track" : "Like this track"}
                    >
                      <Heart className={cn(
                        "w-5 h-5 transition-colors",
                        likedTracks.has(currentTrack.id)
                          ? "text-red-500 fill-red-500"
                          : "text-muted-foreground hover:text-red-500"
                      )} />
                    </Button>
                  </div>
                </div>
              )}

              {/* Next from Section */}
              {queue.length > 0 && (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h2 className="text-lg font-semibold text-foreground">
                      Next from: {currentAlbum?.title || 'Queue'}
                    </h2>
                    {queue.length > 1 && (
                      <div className="flex items-center space-x-3">
                        <span className="text-xs text-muted-foreground">
                          {queue.filter(track => track.id !== currentTrack?.id).length} songs
                        </span>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-xs text-muted-foreground hover:text-foreground"
                          onClick={() => {
                            // TODO: Implement clear queue functionality
                            // This would clear all tracks except the currently playing one
                            console.log('Clear queue clicked - would clear', queue.length - 1, 'tracks');
                          }}
                        >
                          Clear queue
                        </Button>
                      </div>
                    )}
                  </div>

                  {/* Queue List */}
                  <div className="space-y-1">
                    {queue
                      .filter(track => track.id !== currentTrack?.id) // Exclude currently playing track
                      .map((track, index) => {
                        const actualIndex = queue.findIndex(t => t.id === track.id);

                        return (
                          <div
                            key={track.id}
                            className="flex items-center space-x-4 px-4 py-3 rounded-xl hover:bg-purple-600/5 transition-all cursor-pointer group"
                            onClick={() => setCurrentTrack(track)}
                          >
                            {/* Track Number */}
                            <div className="w-6 flex items-center justify-center">
                              <span className="text-sm text-muted-foreground group-hover:hidden">
                                {actualIndex + 1}
                              </span>
                              <Play className="w-4 h-4 text-purple-500 hidden group-hover:block" />
                            </div>

                            {/* Track Cover */}
                            <div className="w-12 h-12 rounded-lg overflow-hidden flex-shrink-0">
                              {track.coverUrl || currentAlbum?.coverUrl ? (
                                <img
                                  src={track.coverUrl || currentAlbum?.coverUrl}
                                  alt={track.title}
                                  className="w-full h-full object-cover"
                                />
                              ) : (
                                <div className="w-full h-full bg-gradient-to-br from-purple-600 to-purple-800 flex items-center justify-center">
                                  <span className="text-white text-sm">🎵</span>
                                </div>
                              )}
                            </div>

                            {/* Track Info */}
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium text-foreground truncate group-hover:text-purple-400 transition-colors">
                                {track.title}
                              </p>
                              <p className="text-xs text-muted-foreground/80 truncate">
                                {track.artist}
                              </p>
                            </div>

                            {/* Duration */}
                            <div className="text-xs text-muted-foreground/80">
                              {track.duration ? formatTime(track.duration) : '--:--'}
                            </div>

                            {/* Like Button */}
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                              onClick={(e) => {
                                e.stopPropagation(); // Prevent track row click
                                handleLikeTrack(track, e);
                              }}
                              disabled={likingTrack === track.id}
                              title={likedTracks.has(track.id) ? "Unlike this track" : "Like this track"}
                            >
                              <Heart className={cn(
                                "w-4 h-4 transition-colors",
                                likedTracks.has(track.id)
                                  ? "text-red-500 fill-red-500"
                                  : "text-muted-foreground hover:text-red-500"
                              )} />
                            </Button>

                            {/* Overflow Menu */}
                            <div
                              className="opacity-0 group-hover:opacity-100 transition-opacity"
                              onClick={(e) => e.stopPropagation()} // Prevent track row click
                            >
                              <OverflowMenu
                                items={[
                                  {
                                    id: 'add-to-playlist',
                                    label: 'Add to playlist',
                                    icon: Plus,
                                    onClick: () => handleAddToPlaylist(track),
                                    separator: true
                                  },
                                  {
                                    id: 'view-artist',
                                    label: 'View artist',
                                    icon: User,
                                    onClick: () => {
                                      console.log(`Navigate to artist: ${track.artist}`);
                                    }
                                  },
                                  {
                                    id: 'view-album',
                                    label: 'View album',
                                    icon: Album,
                                    onClick: () => {
                                      console.log(`Navigate to album: ${track.albumTitle || 'Unknown Album'}`);
                                    }
                                  },
                                  {
                                    id: 'view-credits',
                                    label: 'View credits',
                                    icon: CreditCard,
                                    onClick: () => {
                                      console.log(`View credits for: ${track.title}`);
                                    },
                                    separator: true
                                  },
                                  {
                                    id: 'share',
                                    label: 'Share',
                                    icon: Share,
                                    submenu: [
                                      {
                                        id: 'share-native',
                                        label: 'Share via...',
                                        icon: Share,
                                        onClick: async () => {
                                          try {
                                            const success = await ShareService.shareTrack(track);
                                            if (success) {
                                              console.log(`✅ Track "${track.title}" shared successfully`);
                                            } else {
                                              console.error(`❌ Failed to share track "${track.title}"`);
                                            }
                                          } catch (error) {
                                            console.error('Error sharing track:', error);
                                          }
                                        }
                                      },
                                      {
                                        id: 'copy-link',
                                        label: 'Copy link to song',
                                        icon: Link,
                                        onClick: async () => {
                                          try {
                                            const success = await ShareService.copyTrackLink(track);
                                            if (success) {
                                              console.log(`✅ Track link "${track.title}" copied successfully`);
                                            } else {
                                              console.error(`❌ Failed to copy track link "${track.title}"`);
                                            }
                                          } catch (error) {
                                            console.error('Error copying track link:', error);
                                          }
                                        }
                                      }
                                    ]
                                  }
                                ]}
                                placement="top-left"
                                buttonClassName="h-8 w-8 p-0"
                              />
                            </div>
                          </div>
                        );
                      })}
                  </div>
                </div>
              )}

              {/* Empty State */}
              {queue.length === 0 && (
                <div className="text-center py-12">
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-purple-600/10 flex items-center justify-center">
                    <List className="w-8 h-8 text-purple-500" />
                  </div>
                  <h3 className="text-lg font-medium text-foreground mb-2">Your queue is empty</h3>
                  <p className="text-sm text-muted-foreground">
                    Play a song or album to see your queue here
                  </p>
                </div>
              )}

            </div>


          </div>
        );

      case 'playlist':
        // Only show playlist content if there's a contextual tab active
        if (!contextualTab.type) {
          return (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <p className="text-muted-foreground">Select an album, playlist, or liked songs to view here</p>
              </div>
            </div>
          );
        }

        // Get contextual data based on tab type
        const contextualData = contextualTab.data || {};
        const displayTitle = contextualData.title || contextualTab.label || 'Unknown';
        const displayArtist = contextualData.artist || 'Unknown Artist';
        const displayCoverUrl = contextualData.coverUrl || currentAlbum?.coverUrl;
        const displayType = contextualTab.type === 'album' ? 'Album' :
                           contextualTab.type === 'playlist' ? 'Playlist' :
                           contextualTab.type === 'liked' ? 'Liked Songs' : 'Collection';

        // Calculate total duration from tracks if not provided
        const totalDuration = contextualData?.totalDuration ||
                             (contextualTracks.length > 0 ?
                              contextualTracks.reduce((total, track) => total + (track.duration || 0), 0) : 0);

        return (
          <div className="flex-1 overflow-y-auto">
            {/* Dynamic Header based on contextual tab type */}
            <div className="relative">
              {/* Clean Hero Section */}
              <div className="px-6 pt-4 pb-8">
                <div className="flex items-end space-x-6">
                  {/* Clean Cover - No Card/Shadow */}
                  <div className="w-48 h-48 rounded-xl overflow-hidden flex-shrink-0">
                    {displayCoverUrl ? (
                      <img
                        src={displayCoverUrl}
                        alt={displayTitle}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-purple-600 to-purple-800 flex items-center justify-center">
                        {contextualTab.type === 'album' && <Disc className="w-24 h-24 text-white/90" />}
                        {contextualTab.type === 'playlist' && <List className="w-24 h-24 text-white/90" />}
                        {contextualTab.type === 'liked' && <Heart className="w-24 h-24 text-white/90" />}
                      </div>
                    )}
                  </div>

                  {/* Info with Integrated Controls */}
                  <div className="flex-1 min-w-0 pb-4">
                    <p className="text-sm font-medium text-muted-foreground mb-2">{displayType}</p>
                    <h1 className="text-5xl font-black text-foreground dark:text-transparent dark:bg-gradient-to-r dark:from-white dark:via-purple-100 dark:to-purple-200 dark:bg-clip-text mb-4 leading-tight">
                      {displayTitle}
                    </h1>
                    <div className="flex items-center space-x-2 text-sm mb-6">
                      {/* Artist/Creator Avatar */}
                      {contextualTab.type !== 'liked' && (
                        <>
                          <div className="w-6 h-6 rounded-full bg-gradient-to-br from-purple-500 to-purple-700 flex items-center justify-center">
                            <span className="text-xs font-bold text-white">
                              {displayArtist.charAt(0).toUpperCase()}
                            </span>
                          </div>
                          <span className="font-medium text-foreground">{displayArtist}</span>
                          <span className="text-muted-foreground">•</span>
                        </>
                      )}
                      <span className="text-muted-foreground">2024</span>
                      <span className="text-muted-foreground">•</span>
                      <span className="text-muted-foreground">{contextualTracks.length} songs</span>
                      {/* Show duration if calculated total is greater than 0 */}
                      {totalDuration > 0 && (
                        <>
                          <span className="text-muted-foreground">•</span>
                          <span className="text-muted-foreground">{formatTime(totalDuration)}</span>
                        </>
                      )}
                    </div>

                    {/* Integrated Controls */}
                    <div className="flex items-center space-x-3">
                      {/* Clean Purple Play Button */}
                      <button
                        className="w-12 h-12 rounded-full bg-purple-600 hover:bg-purple-500 text-white shadow-lg hover:shadow-purple-600/20 hover:scale-105 transition-all duration-200 flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-purple-600/20"
                        onClick={() => {
                          if (queue.length > 0) {
                            // If the first track is already playing, just toggle play/pause
                            if (currentTrack?.id === queue[0]?.id) {
                              togglePlay();
                            } else {
                              // If different track, set it and start playing
                              setCurrentTrack(queue[0]);
                              if (!isPlaying) togglePlay();
                            }
                          }
                        }}
                      >
                        {isPlaying && currentTrack?.id === queue[0]?.id ? (
                          <Pause className="w-5 h-5 text-white" />
                        ) : (
                          <Play className="w-5 h-5 ml-0.5 text-white" />
                        )}
                      </button>

                      {/* Enhanced Action Buttons */}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="w-12 h-12 rounded-full text-muted-foreground hover:text-foreground hover:bg-purple-600/10 transition-all"
                        onClick={(e) => {
                          if (currentTrack) {
                            handleLikeTrack(currentTrack, e);
                          }
                        }}
                        disabled={!currentTrack || likingTrack === currentTrack?.id}
                        title={currentTrack && likedTracks.has(currentTrack.id) ? "Unlike this track" : "Like this track"}
                      >
                        <Heart className={cn(
                          "w-5 h-5 transition-colors",
                          currentTrack && likedTracks.has(currentTrack.id)
                            ? "text-red-500 fill-red-500"
                            : "text-muted-foreground hover:text-red-500"
                        )} />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="w-12 h-12 rounded-full text-muted-foreground hover:text-foreground hover:bg-purple-600/10 transition-all"
                        title="More options"
                      >
                        <MoreHorizontal className="w-5 h-5" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Modern Track List Section */}
            <div className="px-6 pb-6">
              {/* Clean Track List Header */}
              <div className="flex items-center space-x-4 px-4 py-3 text-sm font-medium text-muted-foreground/80 border-b border-purple-600/10 mb-3">
                <div className="w-8 text-center">#</div>
                <div className="flex-1">Track</div>
                <div className="w-16 text-center">
                  <span className="inline-block">⏱</span>
                </div>
                <div className="w-8 text-center">
                  <Heart className="w-4 h-4 mx-auto" />
                </div>
                <div className="w-8 text-center">
                  <MoreHorizontal className="w-4 h-4 mx-auto" />
                </div>
              </div>

              {contextualTracksLoading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mx-auto"></div>
                  <p className="text-sm text-muted-foreground mt-2">Loading tracks...</p>
                </div>
              ) : contextualTracks.length > 0 ? (
                <div className="space-y-1">
                  {contextualTracks.map((track, index) => {
                    const isCurrentlyPlaying = currentTrack?.id === track.id;
                    const playCount = track.playCount || 0; // Real play count from track data
                    const likeCount = track.likeCount || 0; // Real like count from track data

                    return (
                      <div
                        key={track.id}
                        className={cn(
                          "flex items-center space-x-4 px-4 py-3 rounded-xl transition-all cursor-pointer group",
                          isCurrentlyPlaying
                            ? "bg-purple-600/10"
                            : "hover:bg-purple-600/5"
                        )}
                        onClick={() => {
                          // When playing from contextual tab, set the contextual tracks as the new queue
                          setCurrentTrack(track);
                          setQueue(contextualTracks);
                          if (!isPlaying) {
                            togglePlay();
                          }
                        }}
                      >
                        {/* Track Number / Play Button */}
                        <div className="w-8 flex items-center justify-center">
                          {isCurrentlyPlaying && isPlaying ? (
                            <div className="flex items-center space-x-0.5">
                              <div className="w-1 h-4 bg-purple-500 rounded-full animate-pulse"></div>
                              <div className="w-1 h-3 bg-purple-500 rounded-full animate-pulse delay-75"></div>
                              <div className="w-1 h-4 bg-purple-500 rounded-full animate-pulse delay-150"></div>
                            </div>
                          ) : (
                            <>
                              <span className={cn(
                                "text-sm font-medium group-hover:hidden transition-all",
                                isCurrentlyPlaying ? "text-purple-500" : "text-muted-foreground"
                              )}>
                                {index + 1}
                              </span>
                              <Play className="w-4 h-4 text-purple-500 hidden group-hover:block" />
                            </>
                          )}
                        </div>

                        {/* Track Info */}
                        <div className="flex-1 min-w-0">
                          <p className={cn(
                            "text-sm font-medium truncate transition-all",
                            isCurrentlyPlaying ? "text-purple-500" : "text-foreground group-hover:text-purple-400"
                          )}>
                            {track.title}
                          </p>
                          <p className="text-xs text-muted-foreground/80 truncate flex items-center space-x-1">
                            {track.explicit && (
                              <span className="bg-purple-600/20 text-purple-400 text-xs px-1.5 py-0.5 rounded-full border border-purple-500/30">E</span>
                            )}
                            <span>{track.artist}</span>
                          </p>
                        </div>

                        {/* Duration */}
                        <div className="w-16 text-center">
                          <span className={cn(
                            "text-sm transition-all",
                            isCurrentlyPlaying ? "text-purple-500" : "text-muted-foreground/80 group-hover:text-muted-foreground"
                          )}>
                            {track.duration ? formatTime(track.duration) : '--:--'}
                          </span>
                        </div>

                        {/* Like Button */}
                        <div className="w-8 flex items-center justify-center">
                          <Button
                            variant="ghost"
                            size="sm"
                            className={cn(
                              "h-8 w-8 p-0 transition-opacity",
                              isCurrentlyPlaying ? "opacity-100" : "opacity-0 group-hover:opacity-100"
                            )}
                            onClick={(e) => handleLikeTrack(track, e)}
                            disabled={likingTrack === track.id}
                            title={likedTracks.has(track.id) ? "Unlike this track" : "Like this track"}
                          >
                            <Heart className={cn(
                              "w-4 h-4 transition-colors",
                              likedTracks.has(track.id)
                                ? "text-red-500 fill-red-500"
                                : "text-foreground hover:text-red-500"
                            )} />
                          </Button>
                        </div>

                        {/* Overflow Menu */}
                        <div className="w-8 flex items-center justify-center">
                          <OverflowMenu
                            items={[
                              {
                                id: 'add-to-playlist',
                                label: 'Add to playlist',
                                icon: Plus,
                                onClick: () => handleAddToPlaylist(track),
                                separator: true
                              },
                              {
                                id: 'view-artist',
                                label: 'View artist',
                                icon: User,
                                onClick: () => {
                                  console.log(`Navigate to artist: ${track.artist}`);
                                  // TODO: Navigate to artist page
                                }
                              },
                              {
                                id: 'view-album',
                                label: 'View album',
                                icon: Album,
                                onClick: () => {
                                  console.log(`Navigate to album: ${track.albumTitle || 'Unknown Album'}`);
                                  // TODO: Navigate to album page
                                }
                              },
                              {
                                id: 'view-credits',
                                label: 'View credits',
                                icon: CreditCard,
                                onClick: () => {
                                  console.log(`View credits for: ${track.title}`);
                                  // TODO: Show credits modal
                                },
                                separator: true
                              },
                              {
                                id: 'share',
                                label: 'Share',
                                icon: Share,
                                submenu: [
                                  {
                                    id: 'share-native',
                                    label: 'Share via...',
                                    icon: Share,
                                    onClick: async () => {
                                      try {
                                        const success = await ShareService.shareTrack(track);
                                        if (success) {
                                          console.log(`✅ Track "${track.title}" shared successfully`);
                                        } else {
                                          console.error(`❌ Failed to share track "${track.title}"`);
                                        }
                                      } catch (error) {
                                        console.error('Error sharing track:', error);
                                      }
                                    }
                                  },
                                  {
                                    id: 'copy-link',
                                    label: 'Copy link to song',
                                    icon: Link,
                                    onClick: async () => {
                                      try {
                                        const success = await ShareService.copyTrackLink(track);
                                        if (success) {
                                          console.log(`✅ Track link "${track.title}" copied successfully`);
                                        } else {
                                          console.error(`❌ Failed to copy track link "${track.title}"`);
                                        }
                                      } catch (error) {
                                        console.error('Error copying track link:', error);
                                      }
                                    }
                                  }
                                ]
                              }
                            ]}
                            placement="top-left"
                            buttonClassName={cn(
                              "h-8 w-8 p-0 transition-opacity",
                              isCurrentlyPlaying ? "opacity-100" : "opacity-0 group-hover:opacity-100"
                            )}
                          />
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">
                    {contextualTab.type === 'liked' ? 'No liked songs yet' :
                     contextualTab.type === 'playlist' ? 'This playlist is empty' :
                     'No tracks in this album'}
                  </p>
                  <p className="text-sm text-muted-foreground/80 mt-1">
                    {contextualTab.type === 'liked' ? 'Like some songs to see them here' :
                     'Add some tracks to get started'}
                  </p>
                </div>
              )}
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  // Fullscreen overlay component
  if (isFullscreen) {
    return (
      <div className="fixed inset-0 z-modal bg-black flex flex-col">
        {/* Fullscreen Header */}
        <div className="flex-shrink-0 p-6 pb-4">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-white">Now Playing</h1>
            <div className="flex items-center space-x-2">
              {/* Fullscreen Share Options Menu */}
              <OverflowMenu
                items={[
                  {
                    id: 'share',
                    label: 'Share',
                    icon: Share,
                    submenu: [
                      {
                        id: 'share-native',
                        label: 'Share via...',
                        icon: Share,
                        onClick: async () => {
                          if (currentTrack) {
                            try {
                              const success = await ShareService.shareTrack(currentTrack);
                              if (success) {
                                console.log(`✅ Track "${currentTrack.title}" shared successfully`);
                              } else {
                                console.error(`❌ Failed to share track "${currentTrack.title}"`);
                              }
                            } catch (error) {
                              console.error('Error sharing track:', error);
                            }
                          }
                        }
                      },
                      {
                        id: 'copy-link',
                        label: 'Copy link to song',
                        icon: Link,
                        onClick: async () => {
                          if (currentTrack) {
                            try {
                              const success = await ShareService.copyTrackLink(currentTrack);
                              if (success) {
                                console.log(`✅ Track link "${currentTrack.title}" copied successfully`);
                              } else {
                                console.error(`❌ Failed to copy track link "${currentTrack.title}"`);
                              }
                            } catch (error) {
                              console.error('Error copying track link:', error);
                            }
                          }
                        }
                      }
                    ]
                  }
                ]}
                placement="bottom-left"
                buttonClassName="h-10 w-10 p-0 text-white hover:bg-white/10"
                buttonContent={<Share className="w-5 h-5" />}
                title="Share options"
              />
              <Button
                variant="ghost"
                size="sm"
                className="h-10 w-10 p-0 text-white hover:bg-white/10"
                onClick={(e) => {
                  if (currentTrack) {
                    handleLikeTrack(currentTrack, e);
                  }
                }}
                disabled={!currentTrack || likingTrack === currentTrack?.id}
                title={currentTrack && likedTracks.has(currentTrack.id) ? "Unlike this track" : "Like this track"}
              >
                <Heart className={cn(
                  "w-5 h-5 transition-colors",
                  currentTrack && likedTracks.has(currentTrack.id)
                    ? "text-red-500 fill-red-500"
                    : "text-white hover:text-red-400"
                )} />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-10 w-10 p-0 text-white hover:bg-white/10"
                title="Exit Fullscreen"
                onClick={exitFullscreen}
              >
                <Minimize className="w-5 h-5" />
              </Button>
              <Button variant="ghost" size="sm" className="h-10 w-10 p-0 text-white hover:bg-white/10" title="More options">
                <MoreHorizontal className="w-5 h-5" />
              </Button>
            </div>
          </div>
        </div>

        {/* Fullscreen Content */}
        <div className="flex-1 px-6">
          {renderTabContent()}
        </div>

        {/* Fullscreen Footer with Modern Pill Toggle */}
        <div className="flex-shrink-0 p-6 pt-4">
          <div className="flex items-center justify-center">
            <div className="relative bg-white/10 rounded-full p-1 backdrop-blur-sm">
              {/* Background pill that slides */}
              <div
                className={cn(
                  "absolute top-1 bottom-1 bg-white/20 rounded-full transition-all duration-300 ease-out shadow-lg",
                  !contextualTab.type ? (
                    // 2-tab mode
                    activeTab === 'play' ? "left-1 right-1/2" : "left-1/2 right-1"
                  ) : (
                    // 3-tab mode
                    activeTab === 'play' ? "left-1 right-2/3" :
                    activeTab === 'queue' ? "left-1/3 right-1/3" :
                    "left-2/3 right-1"
                  )
                )}
              />

              {/* Tab buttons */}
              <div className="relative flex">
                <button
                  onClick={() => setActiveTab('play')}
                  className={cn(
                    'flex items-center space-x-2 text-sm font-medium rounded-full transition-all duration-200 relative z-10',
                    !contextualTab.type ? 'px-6 py-3' : 'px-4 py-3',
                    activeTab === 'play'
                      ? 'text-white'
                      : 'text-white/60 hover:text-white/80'
                  )}
                >
                  <Play className="w-4 h-4" />
                  <span>Play</span>
                </button>

                <button
                  onClick={() => setActiveTab('queue')}
                  className={cn(
                    'flex items-center space-x-2 text-sm font-medium rounded-full transition-all duration-200 relative z-10',
                    !contextualTab.type ? 'px-6 py-3' : 'px-4 py-3',
                    activeTab === 'queue'
                      ? 'text-white'
                      : 'text-white/60 hover:text-white/80'
                  )}
                >
                  <List className="w-4 h-4" />
                  <span>Queue</span>
                </button>

                {/* Contextual third tab - only shows when activated */}
                {contextualTab.type && (
                  <button
                    onClick={() => setActiveTab('playlist')}
                    className={cn(
                      'flex items-center space-x-2 px-4 py-3 text-sm font-medium rounded-full transition-all duration-200 relative z-10',
                      activeTab === 'playlist'
                        ? 'text-white'
                        : 'text-white/60 hover:text-white/80'
                    )}
                  >
                    {contextualTab.type === 'album' && <Disc className="w-4 h-4" />}
                    {contextualTab.type === 'playlist' && <List className="w-4 h-4" />}
                    {contextualTab.type === 'liked' && <Heart className="w-4 h-4" />}
                    <span>
                      {contextualTab.type === 'album' ? 'Album' :
                       contextualTab.type === 'playlist' ? 'Playlist' :
                       contextualTab.type === 'liked' ? 'Liked Songs' : 'Collection'}
                    </span>
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <Card className="h-full flex flex-col bg-gradient-to-b from-purple-900/20 via-background to-background overflow-hidden" variant="glass">
        {/* Header with Soft Edge */}
        <div className="flex-shrink-0 relative">
          <div className="p-4 pb-2">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                {/* Collapse Button - Only show if onCollapse handler is provided */}
                {onCollapse && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onCollapse}
                    className="h-8 w-8 p-0"
                    title="Collapse player"
                  >
                    <ChevronDown className="w-5 h-5" />
                  </Button>
                )}
                <h1 className={cn(
                  "font-bold text-foreground",
                  isMobile ? "text-base" : "text-xl"
                )}>Now Playing</h1>
              </div>
              <div className="flex items-center space-x-1">
                {/* Contextual Share Options Menu */}
                <OverflowMenu
                  items={[
                    {
                      id: 'share',
                      label: 'Share',
                      icon: Share,
                      submenu: [
                        {
                          id: 'share-native',
                          label: 'Share via...',
                          icon: Share,
                          onClick: async () => {
                            try {
                              // Share based on current context
                              if (contextualTab.type === 'album' && contextualTab.data) {
                                const success = await ShareService.shareAlbum(contextualTab.data);
                                if (success) {
                                  console.log(`✅ Album "${contextualTab.data.title}" shared successfully`);
                                } else {
                                  console.error(`❌ Failed to share album "${contextualTab.data.title}"`);
                                }
                              } else if (contextualTab.type === 'playlist' && contextualTab.data) {
                                const success = await ShareService.sharePlaylist(contextualTab.data);
                                if (success) {
                                  console.log(`✅ Playlist "${contextualTab.data.name}" shared successfully`);
                                } else {
                                  console.error(`❌ Failed to share playlist "${contextualTab.data.name}"`);
                                }
                              } else if (contextualTab.type === 'liked') {
                                const success = await ShareService.shareLikedSongs();
                                if (success) {
                                  console.log('✅ Liked songs shared successfully');
                                } else {
                                  console.error('❌ Failed to share liked songs');
                                }
                              } else if (currentTrack) {
                                // Fallback to sharing current track
                                const success = await ShareService.shareTrack(currentTrack);
                                if (success) {
                                  console.log(`✅ Track "${currentTrack.title}" shared successfully`);
                                } else {
                                  console.error(`❌ Failed to share track "${currentTrack.title}"`);
                                }
                              }
                            } catch (error) {
                              console.error('Error sharing content:', error);
                            }
                          }
                        },
                        {
                          id: 'copy-link',
                          label: `Copy link to ${
                            contextualTab.type === 'album' ? 'album' :
                            contextualTab.type === 'playlist' ? 'playlist' :
                            contextualTab.type === 'liked' ? 'liked songs' :
                            'song'
                          }`,
                          icon: Link,
                          onClick: async () => {
                            try {
                              // Copy link based on current context
                              if (contextualTab.type === 'album' && contextualTab.data) {
                                const success = await ShareService.copyAlbumLink(contextualTab.data);
                                if (success) {
                                  console.log(`✅ Album link "${contextualTab.data.title}" copied successfully`);
                                } else {
                                  console.error(`❌ Failed to copy album link "${contextualTab.data.title}"`);
                                }
                              } else if (contextualTab.type === 'playlist' && contextualTab.data) {
                                const success = await ShareService.copyPlaylistLink(contextualTab.data);
                                if (success) {
                                  console.log(`✅ Playlist link "${contextualTab.data.name}" copied successfully`);
                                } else {
                                  console.error(`❌ Failed to copy playlist link "${contextualTab.data.name}"`);
                                }
                              } else if (contextualTab.type === 'liked') {
                                const success = await ShareService.copyLikedSongsLink();
                                if (success) {
                                  console.log('✅ Liked songs link copied successfully');
                                } else {
                                  console.error('❌ Failed to copy liked songs link');
                                }
                              } else if (currentTrack) {
                                // Fallback to copying current track link
                                const success = await ShareService.copyTrackLink(currentTrack);
                                if (success) {
                                  console.log(`✅ Track link "${currentTrack.title}" copied successfully`);
                                } else {
                                  console.error(`❌ Failed to copy track link "${currentTrack.title}"`);
                                }
                              }
                            } catch (error) {
                              console.error('Error copying link:', error);
                            }
                          }
                        }
                      ]
                    }
                  ]}
                  placement="bottom-left"
                  buttonClassName="h-8 w-8 p-0"
                  buttonContent={<Share className="w-4 h-4" />}
                  title="Share options"
                />
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={(e) => {
                    if (currentTrack) {
                      handleLikeTrack(currentTrack, e);
                    }
                  }}
                  disabled={!currentTrack || likingTrack === currentTrack?.id}
                  title={currentTrack && likedTracks.has(currentTrack.id) ? "Unlike this track" : "Like this track"}
                >
                  <Heart className={cn(
                    "w-4 h-4 transition-colors",
                    currentTrack && likedTracks.has(currentTrack.id)
                      ? "text-red-500 fill-red-500"
                      : "text-muted-foreground hover:text-red-500"
                  )} />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  title="Fullscreen"
                  onClick={enterFullscreen}
                >
                  <Maximize className="w-4 h-4" />
                </Button>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0" title="More options">
                  <MoreHorizontal className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
          {/* Subtle gradient edge - positioned to not interfere with content */}
          <div className="absolute bottom-0 left-0 right-0 h-3 bg-gradient-to-b from-transparent via-transparent to-background/10 pointer-events-none"></div>
        </div>

        {/* Content Area */}
        {renderTabContent()}

        {/* Footer with Soft Edge and Tab Navigation */}
        <div className="flex-shrink-0 relative">
          {/* Subtle gradient edge - positioned above content, not over it */}
          <div className="absolute top-0 left-0 right-0 h-3 bg-gradient-to-t from-transparent via-transparent to-background/10 pointer-events-none"></div>

          <div className="bg-background pt-2 pb-4 px-4">
            {/* Modern Pill Toggle Navigation */}
            <div className="flex items-center justify-center">
              <div className="relative bg-secondary/20 dark:bg-secondary/30 rounded-full p-1 backdrop-blur-sm">
                {/* Background pill that slides */}
                <div
                  className={cn(
                    "absolute top-1 bottom-1 bg-background dark:bg-secondary/60 rounded-full transition-all duration-300 ease-out shadow-sm",
                    !contextualTab.type ? (
                      // 2-tab mode
                      activeTab === 'play' ? "left-1 right-1/2" : "left-1/2 right-1"
                    ) : (
                      // 3-tab mode
                      activeTab === 'play' ? "left-1 right-2/3" :
                      activeTab === 'queue' ? "left-1/3 right-1/3" :
                      "left-2/3 right-1"
                    )
                  )}
                />

                {/* Tab buttons */}
                <div className="relative flex">
                  <button
                    onClick={() => setActiveTab('play')}
                    className={cn(
                      'flex items-center space-x-2 text-sm font-medium rounded-full transition-all duration-200 relative z-10',
                      !contextualTab.type ? 'px-6 py-3' : 'px-4 py-3',
                      activeTab === 'play'
                        ? 'text-primary'
                        : 'text-foreground/60 hover:text-foreground/80'
                    )}
                  >
                    <Play className="w-4 h-4" />
                    <span>Play</span>
                  </button>

                  <button
                    onClick={() => setActiveTab('queue')}
                    className={cn(
                      'flex items-center space-x-2 text-sm font-medium rounded-full transition-all duration-200 relative z-10',
                      !contextualTab.type ? 'px-6 py-3' : 'px-4 py-3',
                      activeTab === 'queue'
                        ? 'text-primary'
                        : 'text-foreground/60 hover:text-foreground/80'
                    )}
                  >
                    <List className="w-4 h-4" />
                    <span>Queue</span>
                  </button>

                  {/* Contextual third tab - only shows when activated */}
                  {contextualTab.type && (
                    <button
                      onClick={() => setActiveTab('playlist')}
                      className={cn(
                        'flex items-center space-x-2 px-4 py-3 text-sm font-medium rounded-full transition-all duration-200 relative z-10',
                        activeTab === 'playlist'
                          ? 'text-primary'
                          : 'text-foreground/60 hover:text-foreground/80'
                      )}
                    >
                      {contextualTab.type === 'album' && <Disc className="w-4 h-4" />}
                      {contextualTab.type === 'playlist' && <List className="w-4 h-4" />}
                      {contextualTab.type === 'liked' && <Heart className="w-4 h-4" />}
                      <span>
                        {contextualTab.type === 'album' ? 'Album' :
                         contextualTab.type === 'playlist' ? 'Playlist' :
                         contextualTab.type === 'liked' ? 'Liked Songs' : 'Collection'}
                      </span>
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Add to Playlist Modal */}
      {selectedTrack && (
        <AddToPlaylistModal
          isOpen={showAddToPlaylist}
          onClose={() => {
            setShowAddToPlaylist(false);
            setSelectedTrack(null);
          }}
          trackId={selectedTrack.id}
          trackTitle={selectedTrack.title}
          onSuccess={() => {
            console.log(`✅ Track "${selectedTrack.title}" added to playlist`);
          }}
        />
      )}
    </>
  );


};