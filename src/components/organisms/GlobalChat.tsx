import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { Send, Loader2, AlertCircle, Users, Globe } from 'lucide-react';
import { Button } from '../atoms/Button';
import { useAuth } from '../../hooks/useAuth';
import { useChatStore } from '../../store/chatStore';
import { chatService } from '../../services/chatService';
import { cn } from '../../utils/cn';

interface GlobalChatProps {
  className?: string;
}

export const GlobalChat: React.FC<GlobalChatProps> = ({ className }) => {
  const { user } = useAuth();
  const [messageInput, setMessageInput] = useState('');
  const [isSending, setIsSending] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  const {
    messages,
    isLoading,
    error,
    isConnected,
    onlineCount,
    addMessage,
    addMessages,
    setLoading,
    setError,
    setConnected,
    setOnlineCount,
    clearMessages
  } = useChatStore();

  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  // Initialize chat connection with instant UI and online users tracking
  useEffect(() => {
    console.log('🎯 GlobalChat: useEffect triggered, initializing chat...');
    const componentStartTime = performance.now();

    // Don't show loading state - Firebase provides immediate callback
    setError(null);
    setConnected(true); // Optimistically set as connected

    // No emergency timeout needed - cache-first strategy handles this

    console.log('📞 GlobalChat: Calling chatService.subscribeToMessages...');
    const unsubscribe = chatService.subscribeToMessages(
      (newMessages) => {
        const updateTime = performance.now();
        const elapsed = updateTime - componentStartTime;
        console.log(`🎉 GlobalChat: Received ${newMessages.length} messages after ${elapsed.toFixed(2)}ms`);

        // Cache-first strategy provides instant response

        addMessages(newMessages);
        // Firebase onSnapshot provides immediate callback - no loading needed
        setConnected(true);

        // Auto-scroll to bottom for new messages
        setTimeout(scrollToBottom, 50);
      },
      (errorMessage) => {
        const errorTime = performance.now();
        const elapsed = errorTime - componentStartTime;
        console.error(`🔴 GlobalChat: Error after ${elapsed.toFixed(2)}ms:`, errorMessage);

        // Error handling with cache-first strategy

        setError(errorMessage);
        setConnected(false);
      }
    );

    // Subscribe to online users count
    console.log('👥 GlobalChat: Setting up online users tracking...');
    const onlineUnsubscribe = chatService.subscribeToOnlineUsers(
      (count) => {
        console.log(`👥 GlobalChat: Online users count updated: ${count}`);
        setOnlineCount(count);
      },
      (errorMessage) => {
        console.error('🔴 GlobalChat: Online users error:', errorMessage);
        // Don't show error for online count - it's not critical
      }
    );

    // Track current user presence if authenticated
    if (user) {
      console.log('👤 GlobalChat: Tracking user presence...');
      chatService.trackUserPresence(user.id, user.displayName);
    }

    console.log('✅ GlobalChat: Chat subscription setup complete');

    return () => {
      console.log('🧹 GlobalChat: Cleaning up chat subscription');

      // Mark user as offline when leaving
      if (user) {
        chatService.markUserOffline(user.id);
      }

      unsubscribe();
      onlineUnsubscribe();
      chatService.disconnect();
      setConnected(false);
    };
  }, [addMessages, setError, setConnected, setOnlineCount, scrollToBottom, user]);

  // Handle sending messages
  const handleSendMessage = useCallback(async () => {
    if (!user || !messageInput.trim() || isSending) return;

    const content = messageInput.trim();
    setMessageInput('');
    setIsSending(true);

    try {
      await chatService.sendMessage(
        content,
        user.id,
        user.displayName,
        user.photoURL
      );
    } catch (error) {
      console.error('Failed to send message:', error);
      setError('Failed to send message. Please try again.');
      // Restore message input on error
      setMessageInput(content);
    } finally {
      setIsSending(false);
    }
  }, [user, messageInput, isSending, setError]);

  // Handle Enter key press
  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  }, [handleSendMessage]);

  // Memoized utility functions
  const formatTimestamp = useCallback((timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'now';
    if (minutes < 60) return `${minutes}m`;
    if (hours < 24) return `${hours}h`;
    return `${days}d`;
  }, []);

  const getUserInitials = useCallback((userName: string) => {
    return userName
      .split(' ')
      .map(name => name[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  }, []);

  const getUserColor = useCallback((userId: string) => {
    const colors = [
      'from-blue-400 to-purple-500',
      'from-green-400 to-blue-500',
      'from-pink-400 to-purple-500',
      'from-orange-400 to-red-500',
      'from-purple-400 to-pink-500',
      'from-indigo-400 to-blue-500',
      'from-teal-400 to-green-500',
      'from-red-400 to-pink-500'
    ];

    const hash = userId.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);

    return colors[Math.abs(hash) % colors.length];
  }, []);

  // No loading state needed - Firebase provides instant callback
  // This eliminates the "Connecting to chat..." delay entirely

  if (error) {
    return (
      <div className={cn('flex flex-col h-full', className)}>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center space-y-3">
            <AlertCircle className="w-6 h-6 mx-auto text-red-500" />
            <p className="text-sm text-red-500">{error}</p>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => window.location.reload()}
            >
              Retry
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('flex flex-col h-full', className)}>
      {/* Chat Header */}
      <div className="flex-shrink-0 px-4 py-2 border-b border-border/50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-1">
            <span className="text-sm font-medium text-foreground">
              Worldwide
            </span>
            <Globe className="w-3 h-3 text-muted-foreground" />
          </div>
          {onlineCount > 0 && (
            <div className="flex items-center space-x-1 text-xs text-muted-foreground">
              <Users className="w-3 h-3" />
              <span>{onlineCount} online</span>
            </div>
          )}
        </div>
      </div>

      {/* Messages List */}
      <div 
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto px-4 py-2 space-y-3"
      >
        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center space-y-2">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full mx-auto flex items-center justify-center">
                <Globe className="w-6 h-6 text-white" />
              </div>
              <p className="text-sm text-muted-foreground">
                No messages yet
              </p>
              <p className="text-xs text-muted-foreground">
                Connect with music lovers worldwide!
              </p>
            </div>
          </div>
        ) : (
          messages.map((message) => (
            <div key={message.id} className="flex items-start space-x-2">
              <div className={cn(
                'w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0',
                `bg-gradient-to-br ${getUserColor(message.userId)}`
              )}>
                {message.userAvatar ? (
                  <img 
                    src={message.userAvatar} 
                    alt={message.userName}
                    className="w-full h-full rounded-full object-cover"
                  />
                ) : (
                  <span className="text-xs font-bold text-white">
                    {getUserInitials(message.userName)}
                  </span>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  <p className="text-xs font-medium text-foreground truncate">
                    {message.userName}
                  </p>
                  <span className="text-xs text-muted-foreground">
                    {formatTimestamp(message.timestamp)}
                  </span>
                </div>
                <p className="text-xs text-muted-foreground break-words">
                  {message.content}
                </p>
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      {user ? (
        <div className="flex-shrink-0 p-4 border-t border-border/50">
          <div className="flex items-center space-x-2">
            <input
              type="text"
              value={messageInput}
              onChange={(e) => setMessageInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type a message..."
              className="flex-1 px-3 py-2 text-sm bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
              disabled={isSending || !isConnected}
              maxLength={500}
            />
            <Button
              onClick={handleSendMessage}
              disabled={!messageInput.trim() || isSending || !isConnected}
              size="sm"
              className="px-3"
            >
              {isSending ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Send className="w-4 h-4" />
              )}
            </Button>
          </div>
        </div>
      ) : (
        <div className="flex-shrink-0 p-4 border-t border-border/50">
          <p className="text-xs text-center text-muted-foreground">
            Sign in to join the conversation
          </p>
        </div>
      )}
    </div>
  );
};
