import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Button } from '../atoms/Button';
import { useAuth } from '../../hooks/useAuth';
import { useMusicStore } from '../../store/musicStore';
import { useChatStore } from '../../store/chatStore';
import { chatService } from '../../services/chatService';
import { cn } from '../../utils/cn';
import { 
  Send, 
  Loader2, 
  Music, 
  Play,
  MessageCircle,
  Trash2,
  Users
} from 'lucide-react';

interface TrackChatProps {
  className?: string;
}

export const TrackChat: React.FC<TrackChatProps> = ({ className }) => {
  const { user } = useAuth();
  const { currentTrack, isPlaying } = useMusicStore();
  const [messageInput, setMessageInput] = useState('');
  const [isSending, setIsSending] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  const {
    messages,
    isLoading,
    error,
    isConnected,
    onlineCount,
    addMessage,
    addMessages,
    setLoading,
    setError,
    setConnected,
    setOnlineCount,
    clearMessages,
    removeMessage,
    setCurrentTrackId
  } = useChatStore();

  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  // Initialize track chat connection
  useEffect(() => {
    if (!currentTrack) {
      // Clear messages when no track is playing
      setCurrentTrackId(null);
      setConnected(false);
      return;
    }

    console.log('🎵 TrackChat: Initializing chat for track:', currentTrack?.title || 'undefined');

    // Set current track ID - this will automatically clear messages if track changed
    setCurrentTrackId(currentTrack.id);

    setError(null);
    setConnected(true);

    // Subscribe to track-specific messages
    const unsubscribe = chatService.subscribeToTrackMessages(
      currentTrack.id,
      (newMessages) => {
        console.log(`💬 TrackChat: Received ${newMessages.length} messages for track ${currentTrack?.id || 'undefined'}`);
        addMessages(newMessages);
        setConnected(true);
        setTimeout(scrollToBottom, 50);
      },
      (error) => {
        console.error('TrackChat: Error loading messages:', error);
        setError(error);
        setConnected(false);
      }
    );

    // Track user presence for this track's chat
    if (user) {
      chatService.trackUserPresence(user.id, user.displayName);
    }

    return () => {
      console.log('🧹 TrackChat: Cleaning up chat subscription for track:', currentTrack?.id || 'undefined');
      unsubscribe();
      if (user) {
        chatService.markUserOffline(user.id);
      }
    };
  }, [currentTrack?.id, addMessages, setError, setConnected, scrollToBottom, user, setCurrentTrackId]);

  // Handle sending messages
  const handleSendMessage = useCallback(async () => {
    if (!user || !messageInput.trim() || isSending || !currentTrack) return;

    const content = messageInput.trim();
    setMessageInput('');
    setIsSending(true);

    try {
      await chatService.sendTrackMessage(
        currentTrack.id,
        content,
        user.id,
        user.displayName,
        user.photoURL
      );
    } catch (error) {
      console.error('Failed to send message:', error);
      setError('Failed to send message. Please try again.');
      setMessageInput(content);
    } finally {
      setIsSending(false);
    }
  }, [user, messageInput, isSending, currentTrack, setError]);

  // Handle message deletion
  const handleDeleteMessage = useCallback(async (messageId: string, messageUserId: string) => {
    if (!user || user.id !== messageUserId || !currentTrack) return;

    try {
      await chatService.deleteTrackMessage(currentTrack.id, messageId);
      removeMessage(messageId);
    } catch (error) {
      console.error('Failed to delete message:', error);
      setError('Failed to delete message. Please try again.');
    }
  }, [user, currentTrack, removeMessage, setError]);

  // Handle key press for sending messages
  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  }, [handleSendMessage]);

  // Format timestamp
  const formatTime = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'now';
    if (minutes < 60) return `${minutes}m`;
    if (hours < 24) return `${hours}h`;
    return `${days}d`;
  };

  // No track playing state
  if (!currentTrack) {
    return (
      <div className={cn('flex flex-col h-full items-center justify-center p-6 text-center', className)}>
        <div className="w-16 h-16 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-2xl flex items-center justify-center mb-4">
          <Music className="w-8 h-8 text-muted-foreground" />
        </div>
        <h3 className="text-lg font-semibold text-foreground mb-2">No Track Playing</h3>
        <p className="text-sm text-muted-foreground mb-4 max-w-xs">
          Play a track to join the conversation and see what others are saying about it!
        </p>
        <div className="flex items-center space-x-2 text-xs text-muted-foreground">
          <Play className="w-4 h-4" />
          <span>Start listening to chat</span>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('flex flex-col h-full', className)}>
      {/* Chat Header */}
      <div className="flex-shrink-0 px-4 py-3 border-b border-border/50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2 min-w-0">
            <MessageCircle className="w-4 h-4 text-primary flex-shrink-0" />
            <div className="min-w-0">
              <h3 className="text-sm font-medium text-foreground truncate">
                {currentTrack.title}
              </h3>
              <p className="text-xs text-muted-foreground truncate">
                by {currentTrack.artist}
              </p>
            </div>
          </div>
          {onlineCount > 0 && (
            <div className="flex items-center space-x-1 text-xs text-muted-foreground flex-shrink-0">
              <Users className="w-3 h-3" />
              <span>{onlineCount}</span>
            </div>
          )}
        </div>
      </div>

      {/* Error State */}
      {error && (
        <div className="flex-shrink-0 px-4 py-2 bg-red-500/10 border-b border-red-500/20">
          <p className="text-xs text-red-500">{error}</p>
        </div>
      )}

      {/* Messages List */}
      <div 
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto px-4 py-2 space-y-3"
      >
        {messages.length === 0 && !isLoading ? (
          <div className="flex flex-col items-center justify-center h-full text-center py-8">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-xl flex items-center justify-center mb-3">
              <MessageCircle className="w-6 h-6 text-muted-foreground" />
            </div>
            <p className="text-sm text-muted-foreground mb-2">No comments yet</p>
            <p className="text-xs text-muted-foreground/70">
              Be the first to share your thoughts about this track!
            </p>
          </div>
        ) : (
          messages.map((message) => (
            <div key={message.id} className="flex items-start space-x-3 group">
              {/* User Avatar */}
              <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-500 flex items-center justify-center flex-shrink-0">
                {message.userAvatar ? (
                  <img 
                    src={message.userAvatar} 
                    alt={message.userName}
                    className="w-full h-full rounded-full object-cover"
                  />
                ) : (
                  <span className="text-xs font-medium text-white">
                    {message.userName.charAt(0).toUpperCase()}
                  </span>
                )}
              </div>

              {/* Message Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2 mb-1">
                  <span className="text-sm font-medium text-foreground">
                    {message.userName}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    {formatTime(message.timestamp)}
                  </span>
                  {user && user.id === message.userId && (
                    <button
                      onClick={() => handleDeleteMessage(message.id, message.userId)}
                      className="opacity-0 group-hover:opacity-100 transition-opacity p-1 hover:bg-red-500/10 rounded"
                      title="Delete message"
                    >
                      <Trash2 className="w-3 h-3 text-red-500" />
                    </button>
                  )}
                </div>
                <p className="text-sm text-foreground break-words">
                  {message.content}
                </p>
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      {user ? (
        <div className="flex-shrink-0 p-4 border-t border-border/50">
          <div className="flex items-center space-x-2">
            <input
              type="text"
              value={messageInput}
              onChange={(e) => setMessageInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={`Comment on "${currentTrack.title}"...`}
              className="flex-1 px-3 py-2 text-sm bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
              disabled={isSending || !isConnected}
              maxLength={500}
            />
            <Button
              onClick={handleSendMessage}
              disabled={!messageInput.trim() || isSending || !isConnected}
              size="sm"
              className="px-3"
            >
              {isSending ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Send className="w-4 h-4" />
              )}
            </Button>
          </div>
        </div>
      ) : (
        <div className="flex-shrink-0 p-4 border-t border-border/50 text-center">
          <p className="text-sm text-muted-foreground">
            Sign in to join the conversation
          </p>
        </div>
      )}
    </div>
  );
};
