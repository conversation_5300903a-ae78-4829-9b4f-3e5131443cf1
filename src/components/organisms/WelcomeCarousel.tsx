import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Headphones, MessageCircle, Eye, ArrowRight, Music, Sparkles } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Card } from '../atoms/Card';
import { Button } from '../atoms/Button';
import { CarouselService } from '../../services/carouselService';
import { AlbumService } from '../../services/albumService';
import { AudioService } from '../../services/audioService';
import { useMusicStore } from '../../store/musicStore';
import { useResponsive } from '../../hooks/useResponsive';


import { CarouselSlide } from '../../types';
import { cn } from '../../utils/cn';

// Icon mapping for dynamic slides
const ICON_MAP = {
  'Headphones': Headphones,
  'Music': Music,
  'MessageCircle': MessageCircle,
  'Sparkles': Sparkles,
  'Eye': Eye
};

// Default slide for instant UI display (no demo content, just structure)
const DEFAULT_SLIDE: CarouselSlide = {
  id: 'loading',
  title: 'Loading...',
  subtitle: 'Vibes',
  description: 'Loading carousel content...',
  gradient: 'from-violet-600 to-purple-600',
  accentColor: 'violet',
  icon: 'Music',
  albumId: null,
  albumTitle: null,
  albumCoverUrl: null,
  isActive: true,
  order: 1,
  createdBy: 'system',
  createdAt: new Date(),
  updatedAt: new Date()
};

export const WelcomeCarousel: React.FC = () => {
  const navigate = useNavigate();
  const { isMobile } = useResponsive();
  const [slides, setSlides] = useState<CarouselSlide[]>([DEFAULT_SLIDE]); // Start with loading slide for instant UI
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [isLoadingMusic, setIsLoadingMusic] = useState(false);
  const [isListenHovered, setIsListenHovered] = useState(false);
  const [albumArt, setAlbumArt] = useState<string | null>(null);
  const [slidesLoaded, setSlidesLoaded] = useState(false);

  // Make test functions available globally for debugging
  useEffect(() => {
    // Import and expose test functions globally
    import('../../utils/carouselTest').then(({ testCarouselLoading, clearCarouselCache }) => {
      (window as any).testCarouselLoading = testCarouselLoading;
      (window as any).clearCarouselCache = clearCarouselCache;
    }).catch((error) => {
      console.warn('Failed to load carousel test utilities:', error.message);
    });

    // Import and expose admin promotion utilities
    import('../../utils/promoteToAdmin').then(({ promoteCurrentUserToAdmin, checkAdminStatus }) => {
      (window as any).promoteCurrentUserToAdmin = promoteCurrentUserToAdmin;
      (window as any).checkAdminStatus = checkAdminStatus;
    }).catch((error) => {
      console.warn('Failed to load admin promotion utilities:', error.message);
    });

    // Import and expose direct role update utilities
    import('../../utils/updateUserRole').then(({ fixCurrentUserAdminRole, updateUserRoleDirectly }) => {
      (window as any).fixCurrentUserAdminRole = fixCurrentUserAdminRole;
      (window as any).updateUserRoleDirectly = updateUserRoleDirectly;
    }).catch((error) => {
      console.warn('Failed to load role update utilities:', error.message);
    });

    // Add debug function to check carousel data
    (window as any).debugCarouselData = async () => {
      try {
        console.log('🔍 Debugging carousel data...');
        const { CarouselService } = await import('../../services/carouselService');

        console.log('📊 Checking all slides (admin method):');
        const allSlides = await CarouselService.getAllSlides();
        console.table(allSlides);

        console.log('🎠 Checking active slides (public method):');
        const activeSlides = await CarouselService.getActiveSlides();
        console.table(activeSlides);

        console.log('🧹 Clearing cache and retrying...');
        CarouselService.clearCache();
        const freshSlides = await CarouselService.getActiveSlides();
        console.table(freshSlides);

      } catch (error) {
        console.error('❌ Debug failed:', error);
      }
    };
  }, []);

  // Progressive loading: Replace default slide with database slides
  useEffect(() => {
    let isMounted = true; // Prevent state updates if component unmounts
    let timeoutId: NodeJS.Timeout;

    const loadDatabaseSlides = async () => {
      try {
        console.log('🎠 Loading carousel slides from database...');
        const databaseSlides = await CarouselService.getActiveSlides();

        // Only update state if component is still mounted
        if (!isMounted) return;

        if (databaseSlides.length > 0) {
          console.log('🎠 Database slides loaded:', databaseSlides.length);
          console.log('🎠 Slide titles:', databaseSlides.map(s => s.title));
          setSlides(databaseSlides); // Replace default slide with real slides
          setSlidesLoaded(true);
        } else {
          console.log('🎠 No database slides found - check if slides exist in Firestore');
          console.log('🎠 You may need to create slides in the admin panel');
          console.log('🎠 Visit /admin/carousel to create carousel slides');
          // Keep default slide but mark as loaded
          setSlidesLoaded(true);
        }
      } catch (err: any) {
        console.warn('Failed to load database slides:', err.message);
        // Keep default slide on error (only if still mounted)
        if (isMounted) {
          setSlidesLoaded(true);
        }
      }
    };

    // Immediate loading with shorter timeout to prevent hanging
    timeoutId = setTimeout(() => {
      // Check network status first
      console.log('🌐 Network status:', navigator.onLine ? 'ONLINE' : 'OFFLINE');

      // Reduced timeout to 3 seconds for faster fallback
      Promise.race([
        loadDatabaseSlides(),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Carousel loading timeout after 3s')), 3000)
        )
      ]).catch((error) => {
        console.warn('Carousel loading failed or timed out:', error.message);
        console.warn('Network status at failure:', navigator.onLine ? 'ONLINE' : 'OFFLINE');
        if (isMounted) {
          setSlidesLoaded(true); // Show default slide on timeout
        }
      });
    }, 10); // Reduced delay for faster loading

    // Cleanup function to prevent memory leaks
    return () => {
      isMounted = false;
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, []); // Empty dependency array ensures this only runs once

  // Load album art instantly from denormalized data
  useEffect(() => {
    const currentSlideData = slides[currentSlide];
    setAlbumArt(currentSlideData?.albumCoverUrl || null);
  }, [currentSlide, slides]);

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying || slides.length === 0) return;

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 6000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, slides.length]);

  const nextSlide = () => {
    if (slides.length === 0) return;
    setCurrentSlide((prev) => (prev + 1) % slides.length);
    setIsAutoPlaying(false);
  };

  const prevSlide = () => {
    if (slides.length === 0) return;
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
    setIsAutoPlaying(false);
  };

  const handleListenClick = async () => {
    const currentSlideData = slides[currentSlide];
    if (!currentSlideData?.albumId) {
      console.warn('No album linked to this slide - admin needs to configure album link');
      return;
    }

    if (isLoadingMusic) {
      console.log('🎵 Already loading music, ignoring click');
      return;
    }

    // CRITICAL: Initialize audio context immediately within user gesture for mobile browsers
    // Mobile browsers (iOS Safari, Android Chrome) require AudioContext to be created/resumed
    // within a direct user interaction. This MUST happen synchronously before any async operations.
    console.log('🎵 Initializing audio context for mobile compatibility...');
    await AudioService.initializeAudioContext();

    try {
      setIsLoadingMusic(true);

      // STEP 1: Get album + first track only (FAST!)
      const albumWithFirstTrack = await AlbumService.getAlbumWithFirstTrack(currentSlideData.albumId);

      if (!albumWithFirstTrack || !albumWithFirstTrack.firstTrack) {
        console.warn('Album has no tracks to play');
        return;
      }

      const { album, firstTrack, remainingTrackIds } = albumWithFirstTrack;

      // Access music store
      const {
        setQueueWithFirstTrack,
        appendToQueue,
        togglePlay,
        isPlaying,
        currentTrack,
        setCurrentAlbum
      } = useMusicStore.getState();

      // STEP 2: Set current album for library display
      setCurrentAlbum(album);

      // STEP 3: Set queue and track immediately (synchronous for mobile)
      console.log('🎵 Setting up playback:', firstTrack.title);
      setQueueWithFirstTrack(firstTrack);

      // STEP 4: Start playback immediately within user gesture (CRITICAL for mobile)
      if (!isPlaying || currentTrack?.id !== firstTrack.id) {
        console.log('🎵 Starting immediate playback for mobile compatibility');
        console.log('🎵 Mobile Fix Applied: No setTimeout delay, synchronous playback within user gesture');
        togglePlay(); // Remove setTimeout - must be synchronous for mobile browsers
      }

      // STEP 5: Navigate to play mode (after audio starts for mobile compatibility)
      navigate('/play');

      // STEP 5: Load remaining tracks in background (NON-BLOCKING!)
      if (remainingTrackIds.length > 0) {
        // Don't await this - let it load in background
        AlbumService.getRemainingTracks(remainingTrackIds)
          .then((remainingTracks) => {
            if (remainingTracks.length > 0) {
              console.log(`🎵 Added ${remainingTracks.length} more tracks to queue`);
              appendToQueue(remainingTracks);
            }
          })
          .catch((err) => {
            console.warn('Failed to load remaining tracks:', err);
          });
      }

      console.log('🎵 Started album:', album.title);

    } catch (err: any) {
      console.error('Failed to start album playback:', err);
    } finally {
      // Reset loading state after a short delay to show feedback
      setTimeout(() => setIsLoadingMusic(false), 500);
    }
  };





  // No need for loading states - we always have at least the default slide
  // The UI shows instantly with the default slide, then gets enhanced with database data

  const currentSlideData = slides[currentSlide];
  const IconComponent = ICON_MAP[currentSlideData.icon as keyof typeof ICON_MAP] || Music;

  return (
    <Card className={cn(
      "relative overflow-hidden bg-secondary/30 dark:bg-secondary/30",
      isMobile ? "h-full mt-3" : "h-[calc(100vh-200px)] min-h-[400px]" // Desktop: fit between header and footer with minimum height
    )} variant="glass">
      {/* Background with animated gradient */}
      <div className="absolute inset-0">
        <div className={cn(
          "absolute inset-0 bg-gradient-to-br opacity-[0.03] transition-all duration-1000",
          `bg-gradient-to-br ${currentSlideData.gradient}`
        )} />
        <div className="absolute inset-0 bg-gradient-to-t from-background/50 via-transparent to-background/20" />
      </div>

      {/* Main Content */}
      <div className="relative z-10 h-full flex items-center">
        <div className={cn(
          "w-full max-w-6xl mx-auto",
          isMobile ? "px-4 pb-0" : "px-12" // Remove margin-top since we added padding to parent
        )}>
          <div className={cn(
            "grid items-center",
            isMobile ? "grid-cols-1 gap-6" : "grid-cols-1 lg:grid-cols-2 gap-12"
          )}>
            
            {/* Left Side - Content */}
            <div className={cn(
              "space-y-6",
              isMobile ? "-translate-y-10" : "" // Move content up just a tiny bit more in mobile view
            )}>
              {/* Icon with animated background */}
              <div className="relative">
                <div className={cn(
                  "w-16 h-16 rounded-xl flex items-center justify-center shadow-xl transition-all duration-700",
                  `bg-gradient-to-br ${currentSlideData.gradient}`
                )}>
                  <IconComponent className="w-8 h-8 text-white" />
                </div>
                
                {/* Animated glow */}
                <div className={cn(
                  "absolute inset-0 w-16 h-16 rounded-xl animate-pulse opacity-30 blur-lg",
                  `bg-gradient-to-br ${currentSlideData.gradient}`
                )} />
              </div>

              {/* Text Content */}
              <div className="space-y-4">
                <div className="space-y-2">
                  <p className={cn(
                    "text-xs font-semibold uppercase tracking-wider transition-all duration-700",
                    currentSlideData.accentColor === 'violet' && "text-violet-600 dark:text-violet-400",
                    currentSlideData.accentColor === 'red' && "text-red-600 dark:text-red-400",
                    currentSlideData.accentColor === 'blue' && "text-blue-600 dark:text-blue-400",
                    currentSlideData.accentColor === 'orange' && "text-orange-600 dark:text-orange-400"
                  )}>
                    {currentSlideData.subtitle}
                  </p>
                  
                  <h1 className="text-3xl lg:text-4xl font-bold text-foreground leading-tight">
                    {currentSlideData.title}
                  </h1>
                </div>
                
                <p className="text-base text-muted-foreground leading-relaxed max-w-lg">
                  {currentSlideData.description}
                </p>
              </div>

              {/* Action Buttons - Stack vertically when both sidebars are expanded */}
              <div className="flex flex-col space-y-3 pt-2 max-w-xs">
                <Button
                  size="md"
                  onClick={handleListenClick}
                  onMouseEnter={() => setIsListenHovered(true)}
                  onMouseLeave={() => setIsListenHovered(false)}
                  disabled={!currentSlideData.albumId || isLoadingMusic}
                  className={cn(
                    "bg-gradient-to-r text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 px-6 h-11 text-sm font-semibold w-full justify-center",
                    currentSlideData.gradient,
                    (!currentSlideData.albumId || isLoadingMusic) && "opacity-50 cursor-not-allowed"
                  )}
                  title={
                    !currentSlideData.albumId
                      ? 'No album linked'
                      : isLoadingMusic
                        ? 'Loading music...'
                        : `Listen to ${currentSlideData.albumTitle}`
                  }
                >
                  {isLoadingMusic ? 'Loading...' : 'Listen'}
                  {!isLoadingMusic && <ArrowRight className="w-4 h-4 ml-2" />}
                  {/* Progressive loading indicator */}
                  {!slidesLoaded && (
                    <div className="absolute top-0 right-0 w-2 h-2 bg-yellow-400 rounded-full animate-pulse"
                         title="Loading enhanced features..." />
                  )}
                </Button>
                
                <Button
                  variant="ghost"
                  size="md"
                  className="h-11 px-6 text-sm font-semibold hover:bg-foreground/5 border border-border/20 hover:border-border/40 transition-all duration-300 w-full justify-center"
                >
                  Learn More
                </Button>
              </div>
            </div>

            {/* Right Side - Progressive Vinyl + Album Art */}
            <div className={cn(
              "relative", // Remove z-index here, will be handled by vinyl container
              isMobile ? "order-first -mx-4 overflow-visible h-32" : "" // Changed overflow-hidden to overflow-visible for mobile
            )}>
              {/* Epic Mobile Vinyl: Optimized size and position for better card proportions */}
              <div className={cn(
                "relative",
                isMobile
                  ? "w-[90vw] h-[90vw] max-w-none -translate-y-[52%] translate-x-[48%] z-50" // Epic mobile: 90% screen width, shifted slightly up and a bit more right, HIGH z-index
                  : "w-full max-w-sm aspect-square mx-auto z-20" // Desktop: normal size with moderate z-index
              )}>

                {/* LAYER 1: Vinyl Record - ALWAYS VISIBLE (INSTANT) */}
                <div className="absolute inset-0 z-10">
                  <div
                    className={cn(
                      "w-full h-full rounded-full bg-gradient-to-br from-gray-900 via-gray-800 to-black shadow-2xl transition-transform duration-300",
                      isListenHovered ? "animate-spin" : ""
                    )}
                    style={{
                      animationDuration: isListenHovered ? '3s' : undefined,
                      animationIterationCount: 'infinite',
                      animationTimingFunction: 'linear'
                    }}
                  >
                    {/* Vinyl Grooves - Responsive sizing */}
                    <div className="absolute inset-[6%] rounded-full border border-gray-700/50"></div>
                    <div className="absolute inset-[12%] rounded-full border border-gray-700/30"></div>
                    <div className="absolute inset-[16%] rounded-full border border-gray-700/20"></div>
                    <div className="absolute inset-[20%] rounded-full border border-gray-700/10"></div>

                    {/* Center Label - Responsive sizing */}
                    <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[20%] h-[20%] rounded-full bg-gradient-to-br from-violet-600 to-purple-800 shadow-lg flex items-center justify-center">
                      <div className="w-[15%] h-[15%] rounded-full bg-black"></div>
                    </div>
                  </div>
                </div>

                {/* LAYER 2: Album Cover - PROGRESSIVE (loads on top) */}
                {albumArt && (
                  <div className="absolute inset-0 z-20">
                    <div className="w-full h-full rounded-full overflow-hidden shadow-2xl">
                      <img
                        src={albumArt}
                        alt={`${currentSlideData.title} Cover`}
                        className="w-full h-full object-cover transition-opacity duration-500"
                        loading="lazy"
                        decoding="async"
                        onError={() => {
                          console.warn('Failed to load album art, falling back to vinyl only');
                          setAlbumArt(null);
                        }}
                        onLoad={() => {
                          console.log('🎨 Album art loaded successfully');
                        }}
                        style={{
                          // Optimize image rendering
                          imageRendering: 'auto',
                          // Prevent layout shift
                          aspectRatio: '1 / 1'
                        }}
                      />
                    </div>
                  </div>
                )}

                {/* LAYER 3: Fallback Abstract Visual - Only when no vinyl/album */}
                {!albumArt && !currentSlideData.albumId && (
                  <>
                    {/* Main Circle */}
                    <div className={cn(
                      "absolute inset-0 rounded-full transition-all duration-1000",
                      `bg-gradient-to-br ${currentSlideData.gradient}`,
                      "opacity-10"
                    )} />

                    {/* Animated Rings */}
                    <div className={cn(
                      "absolute inset-3 rounded-full border-2 transition-all duration-1000 animate-spin",
                      currentSlideData.accentColor === 'violet' && "border-violet-500/20",
                      currentSlideData.accentColor === 'red' && "border-red-500/20",
                      currentSlideData.accentColor === 'blue' && "border-blue-500/20",
                      currentSlideData.accentColor === 'orange' && "border-orange-500/20"
                    )} style={{ animationDuration: '20s' }} />

                    <div className={cn(
                      "absolute inset-6 rounded-full border transition-all duration-1000 animate-spin",
                      currentSlideData.accentColor === 'violet' && "border-violet-500/30",
                      currentSlideData.accentColor === 'red' && "border-red-500/30",
                      currentSlideData.accentColor === 'blue' && "border-blue-500/30",
                      currentSlideData.accentColor === 'orange' && "border-orange-500/30"
                    )} style={{ animationDuration: '15s', animationDirection: 'reverse' }} />

                    {/* Center Icon */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className={cn(
                        "w-20 h-20 rounded-full flex items-center justify-center shadow-xl transition-all duration-700",
                        `bg-gradient-to-br ${currentSlideData.gradient}`
                      )}>
                        <IconComponent className="w-10 h-10 text-white" />
                      </div>
                    </div>

                    {/* Floating Elements */}
                    <div className="absolute -top-2 -right-2 w-6 h-6 rounded-full bg-gradient-to-br from-white/20 to-white/5 backdrop-blur-sm animate-bounce" style={{ animationDelay: '0s', animationDuration: '3s' }} />
                    <div className="absolute -bottom-4 -left-4 w-4 h-4 rounded-full bg-gradient-to-br from-white/20 to-white/5 backdrop-blur-sm animate-bounce" style={{ animationDelay: '1s', animationDuration: '3s' }} />
                    <div className="absolute top-1/4 -left-6 w-3 h-3 rounded-full bg-gradient-to-br from-white/20 to-white/5 backdrop-blur-sm animate-bounce" style={{ animationDelay: '2s', animationDuration: '3s' }} />
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Left Navigation - Only Navigation Arrows (NO dot indicators) */}
      {slides.length > 1 && (
        <div className="absolute bottom-6 left-6 z-20">
          <div className="flex items-center space-x-2">
            <button
              onClick={prevSlide}
              className="group w-10 h-10 rounded-full bg-background/10 backdrop-blur-xl border border-white/10 flex items-center justify-center hover:bg-background/20 transition-all duration-300 hover:scale-110 shadow-xl"
              title="Previous slide"
            >
              <ChevronLeft className="w-5 h-5 text-foreground/80 group-hover:text-foreground transition-colors" />
            </button>

            <button
              onClick={nextSlide}
              className="group w-10 h-10 rounded-full bg-background/10 backdrop-blur-xl border border-white/10 flex items-center justify-center hover:bg-background/20 transition-all duration-300 hover:scale-110 shadow-xl"
              title="Next slide"
            >
              <ChevronRight className="w-5 h-5 text-foreground/80 group-hover:text-foreground transition-colors" />
            </button>
          </div>
        </div>
      )}

      {/* Bottom Center - Progress Counter (CENTERED HORIZONTALLY) */}
      {slides.length > 1 && (
        <div className="absolute bottom-6 left-1/2 -translate-x-1/2 z-20">
          <div className="flex items-center space-x-2 text-xs text-muted-foreground">
            <span className="font-medium">{String(currentSlide + 1).padStart(2, '0')}</span>
            <div className="w-8 h-px bg-foreground/20">
              <div 
                className={cn(
                  "h-full bg-gradient-to-r transition-all duration-300",
                  currentSlideData.gradient
                )}
                style={{ width: `${((currentSlide + 1) / slides.length) * 100}%` }}
              />
            </div>
            <span className="font-medium">{String(slides.length).padStart(2, '0')}</span>
          </div>
        </div>
      )}

      {/* Bottom Right - Auto/Manual Toggle (STAYS ON RIGHT SIDE) */}
      {slides.length > 1 && (
        <div className="absolute bottom-6 right-6 z-20">
          <button
            onClick={() => setIsAutoPlaying(!isAutoPlaying)}
            className={cn(
              "flex items-center space-x-2 px-3 py-2 rounded-full text-xs font-medium transition-all duration-300",
              "bg-background/10 backdrop-blur-xl border border-white/10 hover:bg-background/20",
              isAutoPlaying ? "text-green-400" : "text-muted-foreground"
            )}
            title={isAutoPlaying ? "Disable auto-play" : "Enable auto-play"}
          >
            <div className={cn(
              "w-1.5 h-1.5 rounded-full transition-colors",
              isAutoPlaying ? "bg-green-400 animate-pulse" : "bg-muted-foreground"
            )} />
            <span>{isAutoPlaying ? 'Auto' : 'Manual'}</span>
          </button>
        </div>
      )}
    </Card>
  );
};