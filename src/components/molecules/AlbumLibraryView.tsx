import React, { useState, useEffect } from 'react';
import { Play, Pause, MoreHorizontal, Clock, Disc, Heart } from 'lucide-react';
import { Card } from '../atoms/Card';
import { Button } from '../atoms/Button';
import { cn } from '../../utils/cn';
import { useMusicStore } from '../../store/musicStore';
import { Album, Track } from '../../types';
import { AlbumService } from '../../services/albumService';
import { EngagementService } from '../../services/engagementService';
import { useAuth } from '../../hooks/useAuth';

interface AlbumLibraryViewProps {
  album: Album;
  className?: string;
}

export const AlbumLibraryView: React.FC<AlbumLibraryViewProps> = ({ album, className }) => {
  const { user } = useAuth();
  const [tracks, setTracks] = useState<Track[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [likedTracks, setLikedTracks] = useState<Set<string>>(new Set());
  const [likingTrack, setLikingTrack] = useState<string | null>(null);

  const {
    currentTrack,
    isPlaying,
    queue,
    setCurrentTrack,
    setQueue,
    setCurrentAlbum,
    togglePlay,
    updateTrackLikeCount
  } = useMusicStore();

  // Load album tracks
  useEffect(() => {
    const loadTracks = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const albumWithTracks = await AlbumService.getAlbumWithTracks(album.id);
        if (albumWithTracks) {
          setTracks(albumWithTracks.tracks);
        } else {
          setError('Failed to load album tracks');
        }
      } catch (err: any) {
        console.error('Failed to load album tracks:', err);
        setError(err.message || 'Failed to load tracks');
      } finally {
        setLoading(false);
      }
    };

    loadTracks();
  }, [album.id]);

  // Load liked status for tracks
  useEffect(() => {
    const loadLikedStatus = async () => {
      if (!user || tracks.length === 0) {
        setLikedTracks(new Set());
        return;
      }

      try {
        const likedTrackIds = await EngagementService.getUserLikedTracks(user.id);
        const likedSet = new Set(likedTrackIds.filter(id => tracks.some(track => track.id === id)));
        setLikedTracks(likedSet);
      } catch (error) {
        console.error('Failed to load liked status:', error);
      }
    };

    loadLikedStatus();
  }, [user, tracks]);

  // Format duration helper
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Check if track is currently playing
  const isCurrentTrack = (trackId: string) => currentTrack?.id === trackId;

  // Handle track play
  const handlePlayTrack = (track: Track, index: number) => {
    if (currentTrack?.id === track.id) {
      togglePlay();
    } else {
      setQueue(tracks);
      setCurrentTrack(track);
      // Automatically start playing the new track
      if (!isPlaying) {
        togglePlay();
      }
    }
  };

  // Handle play entire album
  const handlePlayAlbum = async () => {
    if (tracks.length === 0) return;

    // Set the current album for Play mode display
    setCurrentAlbum(album);

    setQueue(tracks);
    setCurrentTrack(tracks[0]);
    if (!isPlaying || currentTrack?.id !== tracks[0].id) {
      togglePlay();
    }

    // Play tracking is now handled in useAudioPlayer hook when playback actually starts
  };

  // Handle like/unlike track
  const handleLikeTrack = async (track: Track, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent track play

    if (!user || likingTrack === track.id) return;

    setLikingTrack(track.id);
    try {
      const isLiked = likedTracks.has(track.id);

      if (isLiked) {
        await EngagementService.unlikeItem(track.id, 'track', user.id);
        setLikedTracks(prev => {
          const newSet = new Set(prev);
          newSet.delete(track.id);
          return newSet;
        });
        // Update local like count immediately
        updateTrackLikeCount(track.id, false);
        console.log(`💔 Unliked track: ${track.title}`);
      } else {
        await EngagementService.likeItem(track.id, 'track', user.id);
        setLikedTracks(prev => new Set(prev).add(track.id));
        // Update local like count immediately
        updateTrackLikeCount(track.id, true);
        console.log(`💚 Liked track: ${track.title}`);
      }
    } catch (error) {
      console.error('Failed to toggle like:', error);
    } finally {
      setLikingTrack(null);
    }
  };

  if (loading) {
    return (
      <div className={cn("space-y-4", className)}>
        <div className="animate-pulse">
          <div className="w-full h-48 bg-secondary/20 rounded-xl mb-4"></div>
          <div className="space-y-2">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-12 bg-secondary/10 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn("text-center py-8", className)}>
        <div className="text-muted-foreground">
          <Disc className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p className="text-sm">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Album Header */}
      <div className="space-y-4">
        {/* Album Cover */}
        <div className="relative group">
          {album.coverUrl ? (
            <img
              src={album.coverUrl}
              alt={album.title}
              className="w-full aspect-square rounded-xl object-cover shadow-lg"
            />
          ) : (
            <div className="w-full aspect-square bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center shadow-lg">
              <Disc className="w-16 h-16 text-white" />
            </div>
          )}
          
          {/* Play Button Overlay */}
          <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity rounded-xl flex items-center justify-center">
            <Button
              variant="primary"
              size="lg"
              className="rounded-full h-16 w-16 p-0 shadow-xl"
              onClick={handlePlayAlbum}
            >
              <Play className="w-8 h-8" />
            </Button>
          </div>
        </div>

        {/* Album Info */}
        <div className="space-y-2">
          <h2 className="text-xl font-bold text-foreground">{album.title}</h2>
          <p className="text-muted-foreground">{album.artist}</p>
          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
            <span>{tracks.length} tracks</span>
            {album.totalDuration && (
              <>
                <span>•</span>
                <span>{formatDuration(album.totalDuration)}</span>
              </>
            )}
          </div>
        </div>

        {/* Album Actions */}
        <div className="flex items-center space-x-2">
          <Button
            variant="primary"
            size="md"
            onClick={handlePlayAlbum}
            className="flex items-center space-x-2"
          >
            <Play className="w-4 h-4" />
            <span>Play</span>
          </Button>
          <Button variant="ghost" size="md" className="h-10 w-10 p-0">
            <MoreHorizontal className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Track List */}
      <div className="space-y-1">
        {/* Track List Header */}
        <div className="flex items-center px-3 py-2 text-xs font-medium text-muted-foreground uppercase tracking-wider border-b border-border/50">
          <div className="w-8">#</div>
          <div className="flex-1">Title</div>
          {user && <div className="w-8"></div>}
          <div className="w-12 text-right">
            <Clock className="w-3 h-3 ml-auto" />
          </div>
        </div>

        {/* Tracks */}
        {tracks.map((track, index) => (
          <div
            key={track.id}
            className="flex items-center px-3 py-2 rounded-lg hover:bg-foreground/5 transition-colors cursor-pointer group"
            onClick={() => handlePlayTrack(track, index)}
          >
            {/* Track Number / Play Button */}
            <div className="w-8 flex items-center justify-center">
              {isCurrentTrack(track.id) && isPlaying ? (
                <Pause className="w-4 h-4 text-primary" />
              ) : (
                <>
                  <span className="text-sm text-muted-foreground group-hover:hidden">
                    {index + 1}
                  </span>
                  <Play className="w-4 h-4 text-primary hidden group-hover:block" />
                </>
              )}
            </div>

            {/* Track Info */}
            <div className="flex-1 min-w-0">
              <p className={cn(
                "text-sm font-medium truncate",
                isCurrentTrack(track.id) ? "text-primary" : "text-foreground"
              )}>
                {track.title}
              </p>
              <p className="text-xs text-muted-foreground truncate">
                {track.artist}
              </p>
            </div>

            {/* Like Button */}
            {user && (
              <div className="w-8 flex items-center justify-center">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={(e) => handleLikeTrack(track, e)}
                  disabled={likingTrack === track.id}
                  title={likedTracks.has(track.id) ? 'Unlike this track' : 'Like this track'}
                >
                  <Heart
                    className={cn(
                      "w-3 h-3 transition-colors",
                      likedTracks.has(track.id)
                        ? "text-red-500 fill-red-500"
                        : "text-muted-foreground hover:text-red-500"
                    )}
                  />
                </Button>
              </div>
            )}

            {/* Duration */}
            <div className="w-12 text-right">
              <span className="text-xs text-muted-foreground">
                {track.duration ? formatDuration(track.duration) : '--:--'}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
