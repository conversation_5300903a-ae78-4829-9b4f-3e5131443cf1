import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ChevronDown, User, Settings, LogOut, Edit, Shield } from 'lucide-react';
import { cn } from '../../utils/cn';
import { User as UserType } from '../../types';
import { useAuth } from '../../hooks/useAuth';
import { RoleService } from '../../services/roleService';

interface ProfileDropdownProps {
  user: UserType;
  isCollapsed?: boolean;
  onProfileClick?: () => void;
  onSettingsClick?: () => void;
  className?: string;
}

export const ProfileDropdown: React.FC<ProfileDropdownProps> = ({
  user,
  isCollapsed = false,
  onProfileClick,
  onSettingsClick,
  className
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { signOut } = useAuth();
  const navigate = useNavigate();

  // Debug user data
  useEffect(() => {
    console.log('🔍 ProfileDropdown user data:', {
      id: user.id,
      email: user.email,
      displayName: user.displayName,
      username: user.username,
      role: user.role,
      photoURL: user.photoURL
    });
  }, [user]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleSignOut = async () => {
    try {
      await signOut();
      setIsOpen(false);
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  const handleProfileClick = () => {
    setIsOpen(false);
    onProfileClick?.();
  };

  const handleSettingsClick = () => {
    setIsOpen(false);
    onSettingsClick?.();
  };

  const handleAdminClick = () => {
    setIsOpen(false);
    navigate('/admin');
  };

  return (
    <div className={cn("relative", className)} ref={dropdownRef}>
      {/* Profile Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          "w-full flex items-center space-x-3 p-3 rounded-2xl hover:bg-foreground/5 transition-colors",
          isCollapsed ? "justify-center" : "",
          isOpen && "bg-foreground/5"
        )}
      >
        {/* Avatar */}
        {user.photoURL ? (
          <img
            src={user.photoURL}
            alt={user.displayName || 'User'}
            className="w-10 h-10 rounded-full object-cover flex-shrink-0"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.style.display = 'none';
              const fallback = target.nextElementSibling as HTMLElement;
              if (fallback) fallback.style.display = 'flex';
            }}
          />
        ) : null}
        <div
          className={cn(
            "w-10 h-10 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full flex items-center justify-center flex-shrink-0",
            user.photoURL ? "hidden" : "flex"
          )}
        >
          <User className="w-5 h-5 text-white" />
        </div>

        {/* User Info */}
        {!isCollapsed && (
          <div className="flex-1 min-w-0 text-left">
            <p className="text-sm font-medium text-foreground truncate">
              {user.displayName || user.email?.split('@')[0] || 'User'}
            </p>
            <p className="text-xs text-muted-foreground truncate">
              @{user.username || user.email?.split('@')[0] || 'user'}
            </p>
          </div>
        )}

        {/* Dropdown Arrow */}
        {!isCollapsed && (
          <ChevronDown
            className={cn(
              "w-4 h-4 text-muted-foreground transition-transform duration-200",
              isOpen && "rotate-180"
            )}
          />
        )}
      </button>

      {/* Dropdown Menu */}
      {isOpen && !isCollapsed && (
        <div className="absolute bottom-full left-0 right-0 mb-2 bg-background border border-border rounded-2xl shadow-lg overflow-hidden z-50">
          <div className="py-2">
            {/* Profile Option */}
            <button
              onClick={handleProfileClick}
              className="w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-foreground/5 transition-colors"
            >
              <User className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm text-foreground">View Profile</span>
            </button>

            {/* Edit Profile Option */}
            <button
              onClick={handleProfileClick}
              className="w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-foreground/5 transition-colors"
            >
              <Edit className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm text-foreground">Edit Profile</span>
            </button>

            {/* Settings Option */}
            <button
              onClick={handleSettingsClick}
              className="w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-foreground/5 transition-colors"
            >
              <Settings className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm text-foreground">Account Settings</span>
            </button>

            {/* Admin Panel Option - Only show for admin users */}
            {RoleService.isAdmin(user) && (
              <button
                onClick={handleAdminClick}
                className="w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-foreground/5 transition-colors"
              >
                <Shield className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm text-foreground">Admin Panel</span>
              </button>
            )}

            {/* Divider */}
            <div className="my-2 border-t border-border/20"></div>

            {/* Sign Out Option */}
            <button
              onClick={handleSignOut}
              className="w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-red-500/10 transition-colors text-red-500"
            >
              <LogOut className="w-4 h-4" />
              <span className="text-sm">Sign Out</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
