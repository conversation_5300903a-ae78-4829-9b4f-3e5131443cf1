import React, { useState } from 'react';
import { Plus, Heart, MoreHorizontal, User, Disc, FileText, Share, Link } from 'lucide-react';
import { Button } from '../atoms/Button';
import { OverflowMenu, OverflowMenuItem } from '../atoms/OverflowMenu';
import { AddToPlaylistModal } from '../playlist/AddToPlaylistModal';
import { EngagementService } from '../../services/engagementService';
import { ShareService } from '../../services/shareService';
import { useAuth } from '../../hooks/useAuth';
import { Track } from '../../types';
import { cn } from '../../utils/cn';

interface TrackActionsProps {
  track: Track;
  className?: string;
  isCurrentTrack?: boolean;
  onLikeChange?: (isLiked: boolean) => void;
  onPlaylistAdd?: () => void;
}

export const TrackActions: React.FC<TrackActionsProps> = ({
  track,
  className,
  isCurrentTrack = false,
  onLikeChange,
  onPlaylistAdd
}) => {
  const { user } = useAuth();
  const [isLiked, setIsLiked] = useState(false);
  const [isLiking, setIsLiking] = useState(false);
  const [showAddToPlaylist, setShowAddToPlaylist] = useState(false);

  // Check if track is liked on mount
  React.useEffect(() => {
    if (user && track.id) {
      checkIfLiked();
    }
  }, [user, track.id]);

  const checkIfLiked = async () => {
    if (!user) return;
    
    try {
      const likeEvent = await EngagementService.getUserLike(track.id, 'track', user.id);
      setIsLiked(!!likeEvent);
    } catch (error) {
      console.error('Failed to check like status:', error);
    }
  };

  const handleLike = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!user || isLiking) return;

    setIsLiking(true);
    try {
      if (isLiked) {
        await EngagementService.unlikeItem(track.id, 'track', user.id);
        setIsLiked(false);
        onLikeChange?.(false);
      } else {
        await EngagementService.likeItem(track.id, 'track', user.id);
        setIsLiked(true);
        onLikeChange?.(true);
      }
    } catch (error) {
      console.error('Failed to toggle like:', error);
    } finally {
      setIsLiking(false);
    }
  };

  const handleAddToPlaylist = (e?: React.MouseEvent) => {
    e?.stopPropagation();
    setShowAddToPlaylist(true);
  };

  const overflowMenuItems: OverflowMenuItem[] = [
    {
      id: 'add-to-playlist',
      label: 'Add to playlist',
      icon: Plus,
      onClick: handleAddToPlaylist,
      separator: true // Add separator after this item
    },
    {
      id: 'view-artist',
      label: 'View artist',
      icon: User,
      onClick: () => {
        console.log(`Navigate to artist: ${track.artist}`);
        // TODO: Navigate to artist page
      }
    },
    ...(track.album ? [{
      id: 'view-album',
      label: 'View album',
      icon: Disc,
      onClick: () => {
        console.log(`Navigate to album: ${track.album}`);
        // TODO: Navigate to album page
      }
    }] : []),
    {
      id: 'view-credits',
      label: 'View credits',
      icon: FileText,
      onClick: () => {
        console.log(`View credits for: ${track.title}`);
        // TODO: Show credits modal or navigate to credits page
      },
      separator: true // Add separator after this item
    },
    {
      id: 'share',
      label: 'Share',
      icon: Share,
      submenu: [
        {
          id: 'share-native',
          label: 'Share via...',
          icon: Share,
          onClick: async () => {
            try {
              const success = await ShareService.shareTrack(track);
              if (success) {
                console.log(`✅ Track "${track.title}" shared successfully`);
              } else {
                console.error(`❌ Failed to share track "${track.title}"`);
              }
            } catch (error) {
              console.error('Error sharing track:', error);
            }
          }
        },
        {
          id: 'copy-link',
          label: 'Copy link to song',
          icon: Link,
          onClick: async () => {
            try {
              const success = await ShareService.copyTrackLink(track);
              if (success) {
                console.log(`✅ Track link "${track.title}" copied successfully`);
              } else {
                console.error(`❌ Failed to copy track link "${track.title}"`);
              }
            } catch (error) {
              console.error('Error copying track link:', error);
            }
          }
        }
      ]
    }
  ];

  if (!user) {
    return null; // Don't show actions if user is not logged in
  }

  return (
    <>
      <div className={cn("flex items-center space-x-0", className)}>
        {/* Like Button */}
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            "h-8 w-8 p-0 transition-opacity",
            isCurrentTrack ? "opacity-100" : "opacity-0 group-hover:opacity-100"
          )}
          onClick={handleLike}
          disabled={isLiking}
          title={isLiked ? "Unlike this track" : "Like this track"}
        >
          <Heart
            className={cn(
              "w-4 h-4 transition-colors",
              isLiked ? "text-red-500 fill-red-500" : "text-foreground"
            )}
          />
        </Button>

        {/* Overflow Menu */}
        <OverflowMenu
          items={overflowMenuItems}
          placement="top-left"
          buttonClassName={cn(
            "h-8 w-8 p-0",
            isCurrentTrack ? "opacity-100" : ""
          )}
        />
      </div>

      {/* Add to Playlist Modal */}
      <AddToPlaylistModal
        isOpen={showAddToPlaylist}
        onClose={() => setShowAddToPlaylist(false)}
        trackId={track.id}
        trackTitle={track.title}
        onSuccess={() => {
          onPlaylistAdd?.();
          console.log(`✅ Track "${track.title}" added to playlist`);
        }}
      />
    </>
  );
};
