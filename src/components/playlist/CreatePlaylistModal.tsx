import React, { useState } from 'react';
import { Button } from '../atoms/Button';
import { Card } from '../atoms/Card';
import { useAuth } from '../../hooks/useAuth';
import { MusicService } from '../../services/musicService';
import { cn } from '../../utils/cn';
import {
  X,
  Music,
  Folder,
  Loader2,
  Plus
} from 'lucide-react';

interface CreatePlaylistModalProps {
  isOpen: boolean;
  onClose: () => void;
  onPlaylistCreated?: (playlistId: string) => void;
}

export const CreatePlaylistModal: React.FC<CreatePlaylistModalProps> = ({
  isOpen,
  onClose,
  onPlaylistCreated
}) => {
  const { user } = useAuth();
  const [step, setStep] = useState<'options' | 'create'>('options');
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    isPublic: false
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  if (!isOpen) return null;

  const handleClose = () => {
    if (!loading) {
      setStep('options');
      setFormData({ name: '', description: '', isPublic: false });
      setErrors({});
      onClose();
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Playlist name is required';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Playlist name must be at least 2 characters';
    } else if (formData.name.trim().length > 100) {
      newErrors.name = 'Playlist name must be less than 100 characters';
    }

    if (formData.description && formData.description.length > 500) {
      newErrors.description = 'Description must be less than 500 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleCreatePlaylist = async () => {
    if (!user || !validateForm()) return;

    setLoading(true);
    try {
      // Prepare playlist data, excluding undefined fields
      const playlistData: any = {
        name: formData.name.trim(),
        tracks: [],
        createdBy: user.id,
        isPublic: formData.isPublic,
        collaborators: []
      };

      // Only add description if it's not empty
      if (formData.description.trim()) {
        playlistData.description = formData.description.trim();
      }

      const playlist = await MusicService.createPlaylist(playlistData);

      console.log('✅ Playlist created successfully:', playlist.id);
      onPlaylistCreated?.(playlist.id);
      handleClose();
    } catch (error: any) {
      console.error('Failed to create playlist:', error);
      setErrors({ general: error.message || 'Failed to create playlist' });
    } finally {
      setLoading(false);
    }
  };

  const createOptions = [
    {
      id: 'playlist',
      title: 'Playlist',
      description: 'Build a playlist with songs, or episodes',
      icon: Music,
      action: () => setStep('create')
    },
    {
      id: 'folder',
      title: 'Folder',
      description: 'Organize your playlists',
      icon: Folder,
      disabled: true,
      comingSoon: true
    }
  ];

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4" style={{ zIndex: 10000 }}>
      <Card className="w-full max-w-md" variant="glass">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                <Plus className="w-5 h-5 text-primary" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-foreground">
                  {step === 'options' ? 'Create' : 'Create Playlist'}
                </h2>
                <p className="text-sm text-muted-foreground">
                  {step === 'options' ? 'Choose what to create' : 'Add a name and description'}
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="h-8 w-8 p-0"
              disabled={loading}
            >
              <X className="w-4 h-4" />
            </Button>
          </div>

          {/* Content */}
          {step === 'options' ? (
            <div className="space-y-3">
              {createOptions.map((option) => {
                const Icon = option.icon;
                return (
                  <button
                    key={option.id}
                    onClick={option.action}
                    disabled={option.disabled}
                    className={cn(
                      "w-full flex items-center space-x-4 p-4 rounded-xl text-left transition-all duration-200",
                      option.disabled
                        ? "bg-foreground/5 cursor-not-allowed opacity-60"
                        : "bg-foreground/5 hover:bg-foreground/10 cursor-pointer"
                    )}
                  >
                    <div className={cn(
                      "w-12 h-12 rounded-lg flex items-center justify-center",
                      option.id === 'playlist' && "bg-green-500/20",
                      option.id === 'folder' && "bg-blue-500/20"
                    )}>
                      <Icon className={cn(
                        "w-6 h-6",
                        option.id === 'playlist' && "text-green-500",
                        option.id === 'folder' && "text-blue-500"
                      )} />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <h3 className="font-semibold text-foreground">{option.title}</h3>
                        {option.comingSoon && (
                          <span className="text-xs bg-primary/20 text-primary px-2 py-0.5 rounded-full">
                            Soon
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground">{option.description}</p>
                    </div>
                  </button>
                );
              })}
            </div>
          ) : (
            <div className="space-y-4">
              {/* General Error */}
              {errors.general && (
                <div className="p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
                  <p className="text-sm text-red-500">{errors.general}</p>
                </div>
              )}

              {/* Playlist Name */}
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Name *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="My Playlist #1"
                  className={cn(
                    "w-full px-3 py-2 bg-foreground/5 border rounded-lg",
                    "focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary/30",
                    "placeholder:text-muted-foreground",
                    errors.name ? "border-red-500/50" : "border-border/20"
                  )}
                  disabled={loading}
                />
                {errors.name && (
                  <p className="text-sm text-red-500 mt-1">{errors.name}</p>
                )}
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Add an optional description"
                  rows={3}
                  className={cn(
                    "w-full px-3 py-2 bg-foreground/5 border rounded-lg resize-none",
                    "focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary/30",
                    "placeholder:text-muted-foreground",
                    errors.description ? "border-red-500/50" : "border-border/20"
                  )}
                  disabled={loading}
                />
                {errors.description && (
                  <p className="text-sm text-red-500 mt-1">{errors.description}</p>
                )}
              </div>

              {/* Public/Private Toggle */}
              <div className="flex items-center justify-between p-3 bg-foreground/5 rounded-lg">
                <div>
                  <h4 className="text-sm font-medium text-foreground">Make public</h4>
                  <p className="text-xs text-muted-foreground">Anyone can search for and view this playlist</p>
                </div>
                <button
                  onClick={() => setFormData(prev => ({ ...prev, isPublic: !prev.isPublic }))}
                  disabled={loading}
                  className={cn(
                    "relative w-11 h-6 rounded-full transition-colors duration-200",
                    formData.isPublic ? "bg-primary" : "bg-foreground/20"
                  )}
                >
                  <div className={cn(
                    "absolute top-0.5 w-5 h-5 bg-white rounded-full transition-transform duration-200",
                    formData.isPublic ? "translate-x-5" : "translate-x-0.5"
                  )} />
                </button>
              </div>

              {/* Actions */}
              <div className="flex space-x-3 pt-2">
                <Button
                  variant="outline"
                  onClick={() => setStep('options')}
                  disabled={loading}
                  className="flex-1"
                >
                  Back
                </Button>
                <Button
                  variant="primary"
                  onClick={handleCreatePlaylist}
                  disabled={loading || !formData.name.trim()}
                  className="flex-1"
                >
                  {loading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    'Create'
                  )}
                </Button>
              </div>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};
