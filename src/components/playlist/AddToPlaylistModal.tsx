import React, { useState, useEffect } from 'react';
import { Button } from '../atoms/Button';
import { Card } from '../atoms/Card';
import { useAuth } from '../../hooks/useAuth';
import { MusicService } from '../../services/musicService';
import { EngagementService } from '../../services/engagementService';
import { Playlist } from '../../types';
import { cn } from '../../utils/cn';
import {
  X,
  Music,
  Plus,
  Check,
  Loader2,
  Search,
  Heart
} from 'lucide-react';

interface AddToPlaylistModalProps {
  isOpen: boolean;
  onClose: () => void;
  trackId: string;
  trackTitle?: string;
  onSuccess?: () => void;
}

export const AddToPlaylistModal: React.FC<AddToPlaylistModalProps> = ({
  isOpen,
  onClose,
  trackId,
  trackTitle,
  onSuccess
}) => {
  const { user } = useAuth();
  const [playlists, setPlaylists] = useState<Playlist[]>([]);
  const [loading, setLoading] = useState(true);
  const [addingToPlaylist, setAddingToPlaylist] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [showCreateNew, setShowCreateNew] = useState(false);
  const [newPlaylistName, setNewPlaylistName] = useState('');
  const [creatingPlaylist, setCreatingPlaylist] = useState(false);
  const [likedSongsCount, setLikedSongsCount] = useState(0);
  const [isTrackLiked, setIsTrackLiked] = useState(false);
  const [addingToLikedSongs, setAddingToLikedSongs] = useState(false);

  // Load user playlists and liked songs data
  useEffect(() => {
    if (isOpen && user) {
      loadPlaylists();
      loadLikedSongsData();
    }
  }, [isOpen, user]);

  // Early return after all hooks
  if (!isOpen) return null;

  const loadPlaylists = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);
    try {
      const userPlaylists = await MusicService.getPlaylistsByUser(user.id);
      setPlaylists(userPlaylists);
    } catch (error: any) {
      console.error('Failed to load playlists:', error);
      setError(error.message || 'Failed to load playlists');
    } finally {
      setLoading(false);
    }
  };

  const loadLikedSongsData = async () => {
    if (!user) return;

    try {
      // Get liked songs count
      const likedTrackIds = await EngagementService.getUserLikedTracks(user.id);
      setLikedSongsCount(likedTrackIds.length);

      // Check if current track is liked
      const likeEvent = await EngagementService.getUserLike(trackId, 'track', user.id);
      setIsTrackLiked(!!likeEvent);
    } catch (error: any) {
      console.error('Failed to load liked songs data:', error);
    }
  };

  const handleAddToPlaylist = async (playlistId: string) => {
    if (!user || addingToPlaylist) return;

    setAddingToPlaylist(playlistId);
    try {
      await MusicService.addTrackToPlaylist(playlistId, trackId);
      console.log('✅ Track added to playlist successfully');
      onSuccess?.();
      onClose();
    } catch (error: any) {
      console.error('Failed to add track to playlist:', error);
      setError(error.message || 'Failed to add track to playlist');
    } finally {
      setAddingToPlaylist(null);
    }
  };

  const handleAddToLikedSongs = async () => {
    if (!user || addingToLikedSongs) return;

    setAddingToLikedSongs(true);
    try {
      if (isTrackLiked) {
        // Track is already liked, so this is essentially a no-op
        console.log('💚 Track already in Liked Songs');
      } else {
        // Add to liked songs
        await EngagementService.likeItem(trackId, 'track', user.id);
        setIsTrackLiked(true);
        setLikedSongsCount(prev => prev + 1);
        console.log('💚 Track added to Liked Songs');
      }
      onSuccess?.();
      onClose();
    } catch (error: any) {
      console.error('Failed to add track to Liked Songs:', error);
      setError(error.message || 'Failed to add track to Liked Songs');
    } finally {
      setAddingToLikedSongs(false);
    }
  };

  const handleCreateAndAdd = async () => {
    if (!user || !newPlaylistName.trim() || creatingPlaylist) return;

    setCreatingPlaylist(true);
    try {
      // Create new playlist
      const playlist = await MusicService.createPlaylist({
        name: newPlaylistName.trim(),
        tracks: [],
        createdBy: user.id,
        isPublic: false,
        collaborators: []
      });

      // Add track to the new playlist
      await MusicService.addTrackToPlaylist(playlist.id, trackId);
      
      console.log('✅ Created playlist and added track successfully');
      onSuccess?.();
      onClose();
    } catch (error: any) {
      console.error('Failed to create playlist and add track:', error);
      setError(error.message || 'Failed to create playlist');
    } finally {
      setCreatingPlaylist(false);
    }
  };

  const handleClose = () => {
    if (!addingToPlaylist && !creatingPlaylist) {
      setSearchQuery('');
      setShowCreateNew(false);
      setNewPlaylistName('');
      setError(null);
      onClose();
    }
  };

  const filteredPlaylists = playlists.filter(playlist =>
    playlist.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4" style={{ zIndex: 10000 }}>
      <Card className="w-full max-w-md max-h-[80vh] overflow-hidden" variant="glass">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                <Music className="w-5 h-5 text-primary" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-foreground">Add to Playlist</h2>
                {trackTitle && (
                  <p className="text-sm text-muted-foreground truncate max-w-[200px]">
                    {trackTitle}
                  </p>
                )}
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="h-8 w-8 p-0"
              disabled={addingToPlaylist !== null || creatingPlaylist}
            >
              <X className="w-4 h-4" />
            </Button>
          </div>

          {/* Error Message */}
          {error && (
            <div className="mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
              <p className="text-sm text-red-500">{error}</p>
            </div>
          )}

          {/* Search */}
          <div className="relative mb-4">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Find a playlist"
              className="w-full pl-10 pr-3 py-2 bg-foreground/5 border border-border/20 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary/30 placeholder:text-muted-foreground"
              disabled={loading || addingToPlaylist !== null || creatingPlaylist}
            />
          </div>

          {/* Create New Playlist */}
          <div className="mb-4">
            {!showCreateNew ? (
              <button
                onClick={() => setShowCreateNew(true)}
                disabled={loading || addingToPlaylist !== null || creatingPlaylist}
                className="w-full flex items-center space-x-3 p-3 rounded-lg bg-foreground/5 hover:bg-foreground/10 transition-colors text-left"
              >
                <div className="w-10 h-10 bg-primary/20 rounded-lg flex items-center justify-center">
                  <Plus className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <p className="font-medium text-foreground">Create playlist</p>
                  <p className="text-sm text-muted-foreground">Make a new playlist with this song</p>
                </div>
              </button>
            ) : (
              <div className="space-y-3">
                <input
                  type="text"
                  value={newPlaylistName}
                  onChange={(e) => setNewPlaylistName(e.target.value)}
                  placeholder="Playlist name"
                  className="w-full px-3 py-2 bg-foreground/5 border border-border/20 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary/30 placeholder:text-muted-foreground"
                  disabled={creatingPlaylist}
                  autoFocus
                />
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setShowCreateNew(false);
                      setNewPlaylistName('');
                    }}
                    disabled={creatingPlaylist}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={handleCreateAndAdd}
                    disabled={!newPlaylistName.trim() || creatingPlaylist}
                    className="flex-1"
                  >
                    {creatingPlaylist ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Creating...
                      </>
                    ) : (
                      'Create & Add'
                    )}
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* Saved in Section */}
          <div className="mb-4">
            <h3 className="text-sm font-medium text-muted-foreground mb-3">Saved in</h3>

            {/* Liked Songs */}
            <button
              onClick={handleAddToLikedSongs}
              disabled={addingToLikedSongs || addingToPlaylist !== null || creatingPlaylist}
              className={cn(
                "w-full flex items-center space-x-3 p-3 rounded-lg transition-colors text-left mb-2",
                addingToLikedSongs
                  ? "bg-primary/10"
                  : "hover:bg-foreground/5"
              )}
            >
              <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                <Heart className="w-5 h-5 text-white" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="font-medium text-foreground">Liked Songs</p>
                <p className="text-sm text-muted-foreground flex items-center">
                  <span className="text-green-500 mr-1">↗</span>
                  {likedSongsCount} songs
                </p>
              </div>
              <div className="flex-shrink-0">
                {addingToLikedSongs ? (
                  <Loader2 className="w-5 h-5 animate-spin text-primary" />
                ) : isTrackLiked ? (
                  <Check className="w-5 h-5 text-green-500" />
                ) : (
                  <Plus className="w-5 h-5 text-muted-foreground" />
                )}
              </div>
            </button>
          </div>

          {/* Most relevant Section */}
          <div className="mb-2">
            <h3 className="text-sm font-medium text-muted-foreground mb-3">Most relevant</h3>
          </div>

          {/* Playlists List */}
          <div className="max-h-[300px] overflow-y-auto">
            {loading ? (
              <div className="text-center py-8">
                <Loader2 className="w-6 h-6 animate-spin text-primary mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">Loading playlists...</p>
              </div>
            ) : filteredPlaylists.length > 0 ? (
              <div className="space-y-2">
                {filteredPlaylists.map((playlist) => (
                  <button
                    key={playlist.id}
                    onClick={() => handleAddToPlaylist(playlist.id)}
                    disabled={addingToPlaylist !== null || creatingPlaylist}
                    className={cn(
                      "w-full flex items-center space-x-3 p-3 rounded-lg transition-colors text-left",
                      addingToPlaylist === playlist.id
                        ? "bg-primary/10"
                        : "hover:bg-foreground/5"
                    )}
                  >
                    <div className="w-10 h-10 rounded-lg overflow-hidden bg-gradient-to-br from-blue-400 to-purple-500">
                      {playlist.coverUrl ? (
                        <img src={playlist.coverUrl} alt={playlist.name} className="w-full h-full object-cover" />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <Music className="w-5 h-5 text-white" />
                        </div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-foreground truncate">{playlist.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {playlist.tracks.length} songs
                      </p>
                    </div>
                    <div className="flex-shrink-0">
                      {addingToPlaylist === playlist.id ? (
                        <Loader2 className="w-5 h-5 animate-spin text-primary" />
                      ) : (
                        <Plus className="w-5 h-5 text-muted-foreground" />
                      )}
                    </div>
                  </button>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Music className="w-12 h-12 text-muted-foreground mx-auto mb-3 opacity-50" />
                <p className="text-sm text-muted-foreground mb-2">
                  {searchQuery ? 'No playlists found' : 'No playlists yet'}
                </p>
                <p className="text-xs text-muted-foreground">
                  {searchQuery ? 'Try a different search term' : 'Create your first playlist above'}
                </p>
              </div>
            )}
          </div>
        </div>
      </Card>
    </div>
  );
};
