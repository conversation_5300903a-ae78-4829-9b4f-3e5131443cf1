import React, { useState } from 'react';
import { Track } from '../../types';
import { Button } from '../atoms/Button';
import { useMusicStore } from '../../store/musicStore';
import { useAuth } from '../../hooks/useAuth';
import { EngagementService } from '../../services/engagementService';
import { formatDuration } from '../../utils/formatters';

interface TrackCardProps {
  track: Track;
  showAlbumInfo?: boolean;
  className?: string;
}

export const TrackCard: React.FC<TrackCardProps> = ({ 
  track, 
  showAlbumInfo = true, 
  className = '' 
}) => {
  const { user } = useAuth();
  const { setCurrentTrack, togglePlay, currentTrack, isPlaying } = useMusicStore();
  const [isLiked, setIsLiked] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const isCurrentTrack = currentTrack?.id === track.id;
  const isCurrentlyPlaying = isCurrentTrack && isPlaying;

  const handlePlay = () => {
    if (isCurrentTrack) {
      togglePlay();
    } else {
      setCurrentTrack(track);
      // Play tracking is now handled in useAudioPlayer hook when playback actually starts
    }
  };

  const handleLike = async () => {
    if (!user) return;
    
    setIsLoading(true);
    try {
      if (isLiked) {
        await EngagementService.unlikeItem(track.id, 'track', user.id);
        setIsLiked(false);
      } else {
        await EngagementService.likeItem(track.id, 'track', user.id);
        setIsLiked(true);
      }
    } catch (error) {
      console.error('Failed to toggle like:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    if (!user) return;
    
    setIsLoading(true);
    try {
      if (isSaved) {
        await EngagementService.unsaveItem(track.id, 'track', user.id);
        setIsSaved(false);
      } else {
        await EngagementService.saveItem(track.id, 'track', user.id);
        setIsSaved(true);
      }
    } catch (error) {
      console.error('Failed to toggle save:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Generate a color based on track title for consistent visual identity
  const getTrackColor = (title: string) => {
    const colors = [
      'from-blue-400 to-purple-500',
      'from-pink-400 to-red-500',
      'from-green-400 to-blue-500',
      'from-orange-400 to-pink-500',
      'from-purple-400 to-indigo-500',
      'from-cyan-400 to-blue-500',
      'from-yellow-400 to-orange-500',
      'from-red-400 to-pink-500',
      'from-indigo-400 to-purple-500',
      'from-teal-400 to-green-500'
    ];
    
    const hash = title.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);
    
    return colors[Math.abs(hash) % colors.length];
  };

  return (
    <div className={`flex items-center space-x-3 p-3 rounded-xl hover:bg-foreground/5 transition-colors cursor-pointer group ${className}`}>
      {/* Track Cover */}
      <div className="relative">
        {track.coverUrl ? (
          <img 
            src={track.coverUrl} 
            alt={track.title}
            className="w-12 h-12 rounded-lg object-cover"
          />
        ) : (
          <div className={`w-12 h-12 bg-gradient-to-br ${getTrackColor(track.title)} rounded-lg`}></div>
        )}
        
        {/* Play Button Overlay */}
        <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center rounded-lg">
          <Button 
            variant="primary" 
            size="sm" 
            className="h-6 w-6 p-0 rounded-full"
            onClick={handlePlay}
          >
            <span className="text-xs">
              {isCurrentlyPlaying ? '⏸️' : '▶️'}
            </span>
          </Button>
        </div>
      </div>

      {/* Track Info */}
      <div className="flex-1 min-w-0">
        <h4 className="text-sm font-medium text-foreground truncate">
          {track.title}
        </h4>
        <p className="text-xs text-muted-foreground truncate">
          {track.artist}
          {showAlbumInfo && track.albumTitle && (
            <span> | {track.albumTitle}</span>
          )}
        </p>
        
        {/* Engagement Metrics */}
        <div className="flex items-center space-x-3 mt-1">
          {track.playCount && track.playCount > 0 && (
            <span className="text-xs text-muted-foreground/70">
              {track.playCount.toLocaleString()} plays
            </span>
          )}
          {track.likeCount && track.likeCount > 0 && (
            <span className="text-xs text-muted-foreground/70">
              {track.likeCount.toLocaleString()} likes
            </span>
          )}
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center space-x-2">
        {/* Duration */}
        <span className="text-xs text-muted-foreground">
          {formatDuration(track.duration)}
        </span>

        {/* Like Button */}
        {user && (
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-7 w-7 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
            onClick={handleLike}
            disabled={isLoading}
          >
            <span className={`text-xs ${isLiked ? 'text-red-500' : 'text-muted-foreground'}`}>
              {isLiked ? '❤️' : '🤍'}
            </span>
          </Button>
        )}

        {/* Save Button */}
        {user && (
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-7 w-7 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
            onClick={handleSave}
            disabled={isLoading}
          >
            <span className={`text-xs ${isSaved ? 'text-green-500' : 'text-muted-foreground'}`}>
              {isSaved ? '📌' : '📍'}
            </span>
          </Button>
        )}

        {/* Main Play Button */}
        <Button 
          variant="ghost" 
          size="sm" 
          className="h-7 w-7 p-0"
          onClick={handlePlay}
        >
          <span className="text-sm">
            {isCurrentlyPlaying ? '⏸️' : '▶️'}
          </span>
        </Button>
      </div>
    </div>
  );
};
