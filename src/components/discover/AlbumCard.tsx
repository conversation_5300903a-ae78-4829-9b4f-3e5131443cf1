import React, { useState } from 'react';
import { Album } from '../../types';
import { Button } from '../atoms/Button';
import { useMusicStore } from '../../store/musicStore';
import { useAuth } from '../../hooks/useAuth';
import { EngagementService } from '../../services/engagementService';
import { ShareService } from '../../services/shareService';
import { MusicService } from '../../services/musicService';
import { Share, Link, MoreHorizontal } from 'lucide-react';
import { OverflowMenu } from '../atoms/OverflowMenu';

interface AlbumCardProps {
  album: Album;
  className?: string;
}

export const AlbumCard: React.FC<AlbumCardProps> = ({ album, className = '' }) => {
  const { user } = useAuth();
  const { setCurrentTrack, setQueue, setCurrentAlbum } = useMusicStore();
  const [isLiked, setIsLiked] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handlePlay = async () => {
    try {
      // Get all tracks from the album
      const tracks = await MusicService.getTracksByAlbum(album.id);
      if (tracks.length > 0) {
        // Set the current album for Play mode display
        setCurrentAlbum(album);

        // Set the first track as current and the rest as queue
        setCurrentTrack(tracks[0]);
        setQueue(tracks);

        // Play tracking is now handled in useAudioPlayer hook when playback actually starts
      }
    } catch (error) {
      console.error('Failed to play album:', error);
    }
  };

  const handleLike = async () => {
    if (!user) return;
    
    setIsLoading(true);
    try {
      if (isLiked) {
        await EngagementService.unlikeItem(album.id, 'album', user.id);
        setIsLiked(false);
      } else {
        await EngagementService.likeItem(album.id, 'album', user.id);
        setIsLiked(true);
      }
    } catch (error) {
      console.error('Failed to toggle like:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      if (isSaved) {
        await EngagementService.unsaveItem(album.id, 'album', user.id);
        setIsSaved(false);
      } else {
        await EngagementService.saveItem(album.id, 'album', user.id);
        setIsSaved(true);
      }
    } catch (error) {
      console.error('Failed to toggle save:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleShare = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      const success = await ShareService.shareAlbum(album);
      if (success) {
        console.log(`✅ Album "${album.title}" shared successfully`);
      } else {
        console.error(`❌ Failed to share album "${album.title}"`);
      }
    } catch (error) {
      console.error('Error sharing album:', error);
    }
  };

  const handleCopyLink = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      const success = await ShareService.copyAlbumLink(album);
      if (success) {
        console.log(`✅ Album link "${album.title}" copied successfully`);
      } else {
        console.error(`❌ Failed to copy album link "${album.title}"`);
      }
    } catch (error) {
      console.error('Error copying album link:', error);
    }
  };

  // Generate a color based on album title for consistent visual identity
  const getAlbumColor = (title: string) => {
    const colors = [
      'from-purple-400 to-pink-500',
      'from-blue-400 to-cyan-500',
      'from-green-400 to-emerald-500',
      'from-orange-400 to-red-500',
      'from-indigo-400 to-purple-500',
      'from-pink-400 to-rose-500',
      'from-cyan-400 to-blue-500',
      'from-yellow-400 to-orange-500',
      'from-teal-400 to-green-500',
      'from-red-400 to-pink-500'
    ];
    
    const hash = title.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);
    
    return colors[Math.abs(hash) % colors.length];
  };

  return (
    <div className={`group cursor-pointer ${className}`}>
      {/* Album Cover */}
      <div className="relative">
        {album.coverUrl ? (
          <img 
            src={album.coverUrl} 
            alt={album.title}
            className="aspect-square rounded-xl mb-2 object-cover w-full"
          />
        ) : (
          <div className={`aspect-square bg-gradient-to-br ${getAlbumColor(album.title)} rounded-xl mb-2`}></div>
        )}
        
        {/* Hover Overlay with Actions */}
        <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center rounded-xl mb-2">
          <div className="flex items-center space-x-2">
            {/* Like Button */}
            {user && (
              <Button 
                variant="primary" 
                size="sm" 
                className="h-8 w-8 p-0 rounded-full"
                onClick={handleLike}
                disabled={isLoading}
              >
                <span className={`text-xs ${isLiked ? 'text-red-500' : ''}`}>
                  {isLiked ? '❤️' : '🤍'}
                </span>
              </Button>
            )}

            {/* Play Button */}
            <Button
              variant="primary"
              size="sm"
              className="h-10 w-10 p-0 rounded-full"
              onClick={handlePlay}
            >
              <span className="text-sm">▶️</span>
            </Button>

            {/* Share & Copy Link Menu */}
            <OverflowMenu
              items={[
                {
                  id: 'share',
                  label: 'Share',
                  icon: Share,
                  submenu: [
                    {
                      id: 'share-native',
                      label: 'Share via...',
                      icon: Share,
                      onClick: handleShare
                    },
                    {
                      id: 'copy-link',
                      label: 'Copy link to album',
                      icon: Link,
                      onClick: handleCopyLink
                    }
                  ]
                }
              ]}
              placement="top-left"
              buttonClassName="h-8 w-8 p-0 rounded-full bg-primary text-primary-foreground hover:bg-primary/90"
              buttonContent={<MoreHorizontal className="w-3 h-3" />}
              title="Share options"
            />

            {/* Save Button */}
            {user && (
              <Button 
                variant="primary" 
                size="sm" 
                className="h-8 w-8 p-0 rounded-full"
                onClick={handleSave}
                disabled={isLoading}
              >
                <span className={`text-xs ${isSaved ? 'text-green-500' : ''}`}>
                  {isSaved ? '📌' : '📍'}
                </span>
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Album Info */}
      <div className="space-y-1">
        <h4 className="font-medium text-foreground text-sm truncate">
          {album.title}
        </h4>
        <p className="text-xs text-muted-foreground truncate">
          {album.artist}
        </p>
        <p className="text-xs text-muted-foreground/70">
          {album.trackCount || album.trackIds.length} tracks
        </p>
        
        {/* Engagement Metrics */}
        <div className="flex items-center space-x-2 text-xs text-muted-foreground/70">
          {album.playCount && album.playCount > 0 && (
            <span>{album.playCount.toLocaleString()} plays</span>
          )}
          {album.likeCount && album.likeCount > 0 && (
            <span>{album.likeCount.toLocaleString()} likes</span>
          )}
        </div>
      </div>
    </div>
  );
};
