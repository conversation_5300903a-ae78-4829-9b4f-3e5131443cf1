import React, { useState, useEffect } from 'react';
import { Track, Album } from '../../types';
import { MusicService } from '../../services/musicService';
import { AlbumService } from '../../services/albumService';
import { TrackCard } from './TrackCard';
import { AlbumCard } from './AlbumCard';
import { Card } from '../atoms/Card';
import { useResponsive } from '../../hooks/useResponsive';

interface DiscoverSectionsProps {
  showAllSections?: boolean; // If false, shows limited content for Home page
  className?: string;
}

/**
 * Reusable Discover Content Sections
 * Used in both HomePage (mobile only, limited content) and ExplorePage (all breakpoints, full content)
 */
export const DiscoverSections: React.FC<DiscoverSectionsProps> = ({ 
  showAllSections = true, 
  className = '' 
}) => {
  const { isMobile } = useResponsive();
  const [trendingTracks, setTrendingTracks] = useState<Track[]>([]);
  const [featuredAlbums, setFeaturedAlbums] = useState<Album[]>([]);
  const [featuredSingles, setFeaturedSingles] = useState<Track[]>([]);
  const [newReleases, setNewReleases] = useState<Track[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadDiscoverContent();
  }, []);

  const loadDiscoverContent = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Determine content limits based on usage context
      const limits = showAllSections 
        ? { trending: 10, albums: 8, singles: 10, releases: 12 } // Full content for ExplorePage
        : { trending: 5, albums: 4, singles: 5, releases: 6 };   // Limited content for HomePage

      // Load discover content in parallel
      const [trending, albums, singles, releases] = await Promise.all([
        MusicService.getTrendingTracks(limits.trending),
        AlbumService.getPublishedAlbums(limits.albums),
        MusicService.getPublishedSingles(limits.singles),
        MusicService.getNewReleases(limits.releases)
      ]);

      setTrendingTracks(trending);
      setFeaturedAlbums(albums);
      setFeaturedSingles(singles);
      setNewReleases(releases);
    } catch (err: any) {
      console.error('Failed to load discover content:', err);
      setError('Failed to load content. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        {[1, 2, 3].map((i) => (
          <Card key={i} className="p-4">
            <div className="animate-pulse">
              <div className="h-6 bg-foreground/10 rounded mb-3 w-1/3"></div>
              <div className="space-y-2">
                {[1, 2, 3].map((j) => (
                  <div key={j} className="h-16 bg-foreground/5 rounded"></div>
                ))}
              </div>
            </div>
          </Card>
        ))}
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <Card className={`p-6 ${className}`} variant="glass">
        <div className="text-center space-y-3">
          <div className="w-12 h-12 bg-gradient-to-br from-red-400 to-red-500 rounded-xl mx-auto flex items-center justify-center">
            <span className="text-lg">⚠️</span>
          </div>
          <h2 className="text-lg font-bold text-foreground">Failed to Load</h2>
          <p className="text-sm text-muted-foreground">{error}</p>
        </div>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Featured Albums Section */}
      {featuredAlbums.length > 0 && (
        <section id="featured-albums">
          <Card className="p-4">
            <h2 className="text-lg font-semibold text-foreground mb-3">Featured Albums</h2>
            <div className={`grid gap-3 ${
              isMobile 
                ? 'grid-cols-2' 
                : showAllSections 
                  ? 'grid-cols-2 md:grid-cols-4' 
                  : 'grid-cols-2 md:grid-cols-3'
            }`}>
              {featuredAlbums.map((album) => (
                <AlbumCard key={album.id} album={album} />
              ))}
            </div>
          </Card>
        </section>
      )}

      {/* Trending Now Section */}
      {trendingTracks.length > 0 && (
        <section id="trending-now">
          <Card className="p-4">
            <div className="flex items-center justify-between mb-3">
              <h2 className="text-lg font-semibold text-foreground">Trending Now</h2>
              <span className="text-xs text-muted-foreground">
                Based on plays and likes
              </span>
            </div>
            <div className="space-y-1">
              {trendingTracks.map((track, index) => (
                <div key={track.id} className="flex items-center space-x-3">
                  <span className="text-sm font-bold text-muted-foreground w-6 text-center">
                    {index + 1}
                  </span>
                  <div className="flex-1">
                    <TrackCard track={track} showAlbumInfo={true} />
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </section>
      )}

      {/* Featured Singles Section - Only show if we have content and it's full sections */}
      {featuredSingles.length > 0 && showAllSections && (
        <section id="featured-singles">
          <Card className="p-4">
            <h2 className="text-lg font-semibold text-foreground mb-3">Featured Singles</h2>
            <div className="space-y-1">
              {featuredSingles.map((track) => (
                <TrackCard key={track.id} track={track} showAlbumInfo={false} />
              ))}
            </div>
          </Card>
        </section>
      )}

      {/* New Releases Section - Simplified for limited view */}
      {newReleases.length > 0 && (
        <section id="new-releases">
          <Card className="p-4">
            <h2 className="text-lg font-semibold text-foreground mb-3">New Releases</h2>
            <div className={`grid gap-3 ${
              isMobile 
                ? 'grid-cols-2' 
                : showAllSections 
                  ? 'grid-cols-2 md:grid-cols-3' 
                  : 'grid-cols-2 md:grid-cols-3'
            }`}>
              {newReleases.slice(0, showAllSections ? 6 : 4).map((track) => (
                <div key={track.id} className="group cursor-pointer">
                  <div className="relative">
                    {track.coverUrl ? (
                      <img
                        src={track.coverUrl}
                        alt={track.title}
                        className="aspect-square rounded-xl mb-2 object-cover w-full"
                      />
                    ) : (
                      <div className="aspect-square bg-gradient-to-br from-purple-400 to-pink-500 rounded-xl mb-2"></div>
                    )}
                  </div>
                  <h4 className="font-medium text-foreground text-sm truncate">
                    {track.title}
                  </h4>
                  <p className="text-xs text-muted-foreground truncate">
                    {track.artist}
                  </p>
                </div>
              ))}
            </div>
          </Card>
        </section>
      )}

      {/* Empty State - Only show if no content at all */}
      {!isLoading &&
       trendingTracks.length === 0 &&
       featuredAlbums.length === 0 &&
       featuredSingles.length === 0 &&
       newReleases.length === 0 && (
        <Card className="p-8" variant="glass">
          <div className="text-center space-y-3">
            <div className="w-16 h-16 bg-gradient-to-br from-gray-400 to-gray-500 rounded-xl mx-auto flex items-center justify-center">
              <span className="text-2xl">🎵</span>
            </div>
            <h2 className="text-lg font-bold text-foreground">No Music Available</h2>
            <p className="text-sm text-muted-foreground max-w-md mx-auto">
              There's no published music to discover yet. Check back later or encourage artists to publish their work!
            </p>
          </div>
        </Card>
      )}
    </div>
  );
};
