import React from 'react';
import { cn } from '../../utils/cn';
import { DivideIcon as LucideIcon } from 'lucide-react';

interface ToggleButtonProps {
  icon: LucideIcon;
  label: string;
  isActive: boolean;
  onClick: () => void;
  variant?: 'play' | 'explore' | 'chat';
  isCollapsed?: boolean;
}

export const ToggleButton: React.FC<ToggleButtonProps> = ({
  icon: Icon,
  label,
  isActive,
  onClick,
  variant = 'play',
  isCollapsed = false
}) => {
  const variantStyles = {
    play: {
      active: 'bg-gradient-to-r from-green-500/20 to-emerald-500/20 text-green-600 dark:text-green-400 border-green-500/30 shadow-[0_0_20px_rgba(34,197,94,0.15)]',
      inactive: 'text-foreground/60 hover:text-green-500 hover:bg-green-500/5 border-transparent'
    },
    explore: {
      active: 'bg-gradient-to-r from-blue-500/20 to-cyan-500/20 text-blue-600 dark:text-blue-400 border-blue-500/30 shadow-[0_0_20px_rgba(59,130,246,0.15)]',
      inactive: 'text-foreground/60 hover:text-blue-500 hover:bg-blue-500/5 border-transparent'
    },
    chat: {
      active: 'bg-gradient-to-r from-orange-500/20 to-red-500/20 text-orange-600 dark:text-orange-400 border-orange-500/30 shadow-[0_0_20px_rgba(249,115,22,0.15)]',
      inactive: 'text-foreground/60 hover:text-orange-500 hover:bg-orange-500/5 border-transparent'
    }
  };

  const currentStyle = isActive ? variantStyles[variant].active : variantStyles[variant].inactive;

  return (
    <button
      onClick={onClick}
      className={cn(
        'relative w-full flex items-center rounded-2xl px-4 py-3 text-left transition-all duration-300 font-medium text-sm',
        'border hover:scale-[1.02] active:scale-[0.98]',
        currentStyle,
        isCollapsed ? 'justify-center' : ''
      )}
      title={isCollapsed ? label : undefined}
    >
      {/* Animated glow effect when active */}
      {isActive && (
        <div className="absolute inset-0 rounded-2xl opacity-50 animate-pulse">
          <div className={cn(
            "absolute inset-0 rounded-2xl blur-sm",
            variant === 'play' && "bg-gradient-to-r from-green-500/10 to-emerald-500/10",
            variant === 'explore' && "bg-gradient-to-r from-blue-500/10 to-cyan-500/10",
            variant === 'chat' && "bg-gradient-to-r from-orange-500/10 to-red-500/10"
          )} />
        </div>
      )}
      
      {/* Icon with pulse animation when active */}
      <Icon className={cn(
        "w-5 h-5 flex-shrink-0 transition-all duration-300",
        isActive && "animate-pulse"
      )} />
      
      {!isCollapsed && (
        <span className="ml-3 relative z-10">{label}</span>
      )}
      
      {/* Active indicator dot */}
      {isActive && (
        <div className={cn(
          "absolute right-3 w-2 h-2 rounded-full",
          variant === 'play' && "bg-green-500",
          variant === 'explore' && "bg-blue-500",
          variant === 'chat' && "bg-orange-500"
        )} />
      )}
    </button>
  );
};