import React, { useState, useRef, useEffect } from 'react';
import { MoreHorizontal, ChevronRight } from 'lucide-react';
import { cn } from '../../utils/cn';
import { Button } from './Button';

export interface OverflowMenuItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  onClick?: () => void;
  variant?: 'default' | 'destructive';
  disabled?: boolean;
  separator?: boolean; // Add separator after this item
  submenu?: OverflowMenuItem[]; // Submenu items
}

interface OverflowMenuProps {
  items: OverflowMenuItem[];
  className?: string;
  buttonClassName?: string;
  menuClassName?: string;
  placement?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
}

export const OverflowMenu: React.FC<OverflowMenuProps> = ({
  items,
  className,
  buttonClassName,
  menuClassName,
  placement = 'bottom-right'
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [actualPlacement, setActualPlacement] = useState(placement);
  const [openSubmenu, setOpenSubmenu] = useState<string | null>(null);
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [isOpen]);

  // Close menu on escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      return () => {
        document.removeEventListener('keydown', handleEscape);
      };
    }
  }, [isOpen]);

  const handleItemClick = (item: OverflowMenuItem) => {
    if (!item.disabled) {
      if (item.submenu) {
        // Toggle submenu
        setOpenSubmenu(openSubmenu === item.id ? null : item.id);
      } else if (item.onClick) {
        // Execute action and close menu
        item.onClick();
        setIsOpen(false);
        setOpenSubmenu(null);
      }
    }
  };

  // Calculate optimal placement to prevent overflow
  const calculatePlacement = () => {
    if (!buttonRef.current) return placement;

    const buttonRect = buttonRef.current.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const menuWidth = 160; // min-w-[160px]
    const menuHeight = items.length * 40 + 16; // Approximate height

    // Find the main card container (look for closest element with overflow-visible or main content area)
    let container = buttonRef.current.closest('[data-main-card]') ||
                   buttonRef.current.closest('.overflow-y-auto') ||
                   buttonRef.current.closest('main');

    if (!container) {
      container = document.body;
    }

    const containerRect = container.getBoundingClientRect();

    // Check horizontal space
    const spaceRight = containerRect.right - buttonRect.right;
    const spaceLeft = buttonRect.left - containerRect.left;

    // Check vertical space
    const spaceBelow = containerRect.bottom - buttonRect.bottom;
    const spaceAbove = buttonRect.top - containerRect.top;

    // Determine optimal placement
    let optimalPlacement = placement;

    // Horizontal preference: right if space, otherwise left
    const preferRight = spaceRight >= menuWidth;
    const preferLeft = spaceLeft >= menuWidth;

    // Vertical preference: below if space, otherwise above
    const preferBelow = spaceBelow >= menuHeight;
    const preferAbove = spaceAbove >= menuHeight;

    if (preferBelow && preferRight) {
      optimalPlacement = 'bottom-right';
    } else if (preferBelow && preferLeft) {
      optimalPlacement = 'bottom-left';
    } else if (preferAbove && preferRight) {
      optimalPlacement = 'top-right';
    } else if (preferAbove && preferLeft) {
      optimalPlacement = 'top-left';
    } else {
      // Fallback: choose based on available space
      if (spaceRight >= spaceLeft) {
        optimalPlacement = preferBelow ? 'bottom-right' : 'top-right';
      } else {
        optimalPlacement = preferBelow ? 'bottom-left' : 'top-left';
      }
    }

    return optimalPlacement;
  };

  const getMenuPositionClasses = () => {
    switch (actualPlacement) {
      case 'bottom-left':
        return 'top-full right-full mr-2 mt-1';
      case 'top-right':
        return 'bottom-full right-0 mb-1';
      case 'top-left':
        return 'bottom-full right-full mr-2 mb-1';
      case 'bottom-right':
      default:
        return 'top-full right-0 mt-1';
    }
  };

  return (
    <div className={cn("relative", className)} ref={menuRef}>
      {/* Overflow Button */}
      <Button
        ref={buttonRef}
        variant="ghost"
        size="sm"
        className={cn(
          "h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity",
          isOpen && "opacity-100",
          buttonClassName
        )}
        onClick={(e) => {
          e.stopPropagation();
          if (!isOpen) {
            // Calculate optimal placement before opening
            const optimalPlacement = calculatePlacement();
            setActualPlacement(optimalPlacement);
          }
          setIsOpen(!isOpen);
        }}
        title="More options"
      >
        <MoreHorizontal className="w-4 h-4" />
      </Button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div
          className={cn(
            "absolute z-50 min-w-[160px] max-w-[200px] bg-card rounded-lg shadow-xl overflow-hidden",
            "backdrop-blur-sm",
            getMenuPositionClasses(),
            menuClassName
          )}
          style={{
            boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
            // Ensure menu doesn't extend beyond viewport
            maxWidth: 'calc(100vw - 32px)'
          }}
        >
          <div className="py-1">
            {items.map((item, index) => {
              const Icon = item.icon;
              const hasSubmenu = item.submenu && item.submenu.length > 0;
              const isSubmenuOpen = openSubmenu === item.id;

              return (
                <React.Fragment key={item.id}>
                  <button
                    onClick={() => handleItemClick(item)}
                    disabled={item.disabled}
                    className={cn(
                      "w-full flex items-center justify-between px-3 py-2 text-left text-sm transition-colors",
                      "focus:outline-none focus:bg-foreground/5",
                      item.disabled
                        ? "opacity-50 cursor-not-allowed"
                        : "hover:bg-foreground/5",
                      item.variant === 'destructive'
                        ? "text-red-500 hover:bg-red-500/10 focus:bg-red-500/10"
                        : "text-foreground"
                    )}
                  >
                    <div className="flex items-center space-x-3">
                      <Icon className="w-4 h-4 flex-shrink-0" />
                      <span className="truncate">{item.label}</span>
                    </div>
                    {hasSubmenu && (
                      <ChevronRight className={cn(
                        "w-4 h-4 flex-shrink-0 transition-transform",
                        isSubmenuOpen && "rotate-90"
                      )} />
                    )}
                  </button>

                  {/* Submenu */}
                  {hasSubmenu && isSubmenuOpen && (
                    <div className="ml-4 border-l border-border/30 pl-2 py-1">
                      {item.submenu!.map((subItem) => {
                        const SubIcon = subItem.icon;
                        return (
                          <button
                            key={subItem.id}
                            onClick={() => {
                              if (!subItem.disabled && subItem.onClick) {
                                subItem.onClick();
                                setIsOpen(false);
                                setOpenSubmenu(null);
                              }
                            }}
                            disabled={subItem.disabled}
                            className={cn(
                              "w-full flex items-center space-x-3 px-3 py-2 text-left text-sm transition-colors",
                              "focus:outline-none focus:bg-foreground/5",
                              subItem.disabled
                                ? "opacity-50 cursor-not-allowed"
                                : "hover:bg-foreground/5",
                              subItem.variant === 'destructive'
                                ? "text-red-500 hover:bg-red-500/10 focus:bg-red-500/10"
                                : "text-foreground"
                            )}
                          >
                            <SubIcon className="w-4 h-4 flex-shrink-0" />
                            <span className="truncate">{subItem.label}</span>
                          </button>
                        );
                      })}
                    </div>
                  )}

                  {item.separator && index < items.length - 1 && (
                    <div className="my-1 border-t border-border/50" />
                  )}
                </React.Fragment>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};
