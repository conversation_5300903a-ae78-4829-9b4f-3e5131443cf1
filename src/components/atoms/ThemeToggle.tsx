import React from 'react';
import { <PERSON>, <PERSON>, Monitor } from 'lucide-react';
import { useThemeStore } from '../../store/themeStore';
import { cn } from '../../utils/cn';

interface ThemeToggleProps {
  variant?: 'button' | 'switch';
  showLabel?: boolean;
  className?: string;
}

export const ThemeToggle: React.FC<ThemeToggleProps> = ({
  variant = 'button',
  showLabel = false,
  className
}) => {
  const { theme, resolvedTheme, setTheme, toggleTheme } = useThemeStore();

  if (variant === 'switch') {
    return (
      <button
        onClick={toggleTheme}
        className={cn(
          'relative inline-flex h-10 w-20 items-center rounded-full transition-all duration-300 ease-in-out',
          'bg-secondary/30 hover:bg-secondary/40 border border-border/20 dark:border-border/10',
          'focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2 focus:ring-offset-background',
          'shadow-sm hover:shadow-md',
          className
        )}
        title={`Switch to ${resolvedTheme === 'light' ? 'dark' : 'light'} mode`}
        aria-label={`Switch to ${resolvedTheme === 'light' ? 'dark' : 'light'} mode`}
      >
        {/* Track background */}
        <div className="absolute inset-0 rounded-full bg-gradient-to-r from-violet-200 via-purple-200 to-pink-200 dark:from-violet-900 dark:via-purple-900 dark:to-pink-900 opacity-50 transition-opacity duration-300" />
        
        {/* Sliding thumb */}
        <div
          className={cn(
            'relative inline-block h-8 w-8 transform rounded-full transition-all duration-300 ease-in-out',
            'bg-background dark:bg-foreground shadow-lg border border-border/20 dark:border-0',
            resolvedTheme === 'light' ? 'translate-x-1' : 'translate-x-11'
          )}
        >
          {/* Icon container */}
          <div className="absolute inset-0 flex items-center justify-center">
            {resolvedTheme === 'light' ? (
              <Sun className="h-4 w-4 text-violet-500 transition-all duration-300" />
            ) : (
              <Moon className="h-4 w-4 text-purple-400 transition-all duration-300" />
            )}
          </div>
        </div>

        {/* Label */}
        {showLabel && (
          <span className="ml-3 text-sm font-medium text-foreground/80">
            {resolvedTheme === 'light' ? 'Light' : 'Dark'}
          </span>
        )}
      </button>
    );
  }

  // Button variant with dropdown-like behavior
  return (
    <div className="relative">
      <button
        onClick={() => {
          // Cycle through themes: light -> dark -> system -> light
          if (theme === 'light') setTheme('dark');
          else if (theme === 'dark') setTheme('system');
          else setTheme('light');
        }}
        className={cn(
          'relative flex items-center justify-center h-10 w-10 rounded-full transition-all duration-300 ease-in-out',
          'bg-background/80 hover:bg-background border border-border/20 dark:border-border/10',
          'shadow-sm hover:shadow-md hover:scale-105 active:scale-95',
          'focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2 focus:ring-offset-background',
          className
        )}
        title={`Current: ${theme} theme (${resolvedTheme}). Click to cycle.`}
        aria-label={`Current: ${theme} theme. Click to cycle through themes.`}
      >
        {/* Background gradient based on current theme */}
        <div className={cn(
          "absolute inset-0 rounded-full opacity-20 transition-all duration-300",
          theme === 'light' && "bg-gradient-to-br from-violet-400 to-purple-400",
          theme === 'dark' && "bg-gradient-to-br from-violet-600 to-purple-600",
          theme === 'system' && "bg-gradient-to-br from-violet-500 to-pink-500"
        )} />

        {/* Icon with smooth transitions */}
        <div className="relative z-10 transition-all duration-300">
          {theme === 'light' && (
            <Sun className="h-5 h-5 text-violet-600 dark:text-violet-400 transition-colors duration-300" />
          )}
          {theme === 'dark' && (
            <Moon className="h-5 w-5 text-purple-600 dark:text-purple-400 transition-colors duration-300" />
          )}
          {theme === 'system' && (
            <Monitor className="h-5 w-5 text-pink-600 dark:text-pink-400 transition-colors duration-300" />
          )}
        </div>

        {/* Active indicator */}
        <div className={cn(
          "absolute -top-1 -right-1 w-3 h-3 rounded-full transition-all duration-300",
          theme === 'light' && "bg-violet-500 shadow-violet-500/50",
          theme === 'dark' && "bg-purple-500 shadow-purple-500/50",
          theme === 'system' && "bg-pink-500 shadow-pink-500/50",
          "shadow-lg"
        )} />

        {/* Label */}
        {showLabel && (
          <span className="absolute -bottom-8 left-1/2 -translate-x-1/2 text-xs font-medium text-foreground/60 whitespace-nowrap">
            {theme.charAt(0).toUpperCase() + theme.slice(1)}
          </span>
        )}
      </button>
    </div>
  );
};