import React from 'react';
import { cn } from '../../utils/cn';

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'interactive' | 'glass';
  hover?: boolean;
  allowOverflow?: boolean;
  rounded?: string; // Allow custom border radius
}

export const Card: React.FC<CardProps> = ({
  children,
  className,
  variant = 'default',
  hover = false,
  allowOverflow = false,
  rounded = 'rounded-3xl', // Default border radius, can be overridden
  ...props
}) => {
  const baseStyles = `${rounded} transition-all duration-300 ${allowOverflow ? 'overflow-visible' : 'overflow-hidden'}`;

  const variants = {
    default: 'bg-card border border-border/20 dark:border-0 shadow-[0_2px_9px_rgb(0,0,0,0.024)] dark:shadow-[0_2px_9px_rgba(0,0,0,0.075)]',
    interactive: 'bg-card border border-border/20 dark:border-0 shadow-[0_2px_9px_rgb(0,0,0,0.024)] dark:shadow-[0_2px_9px_rgba(0,0,0,0.075)] cursor-pointer',
    glass: 'bg-card/90 backdrop-blur-md border border-border/20 dark:border-0 shadow-[0_2px_9px_rgb(0,0,0,0.024)] dark:shadow-[0_2px_9px_rgba(0,0,0,0.075)]'
  };

  const hoverStyles = hover ? 'hover:shadow-[0_4px_12px_rgb(0,0,0,0.036)] dark:hover:shadow-[0_4px_12px_rgba(0,0,0,0.105)] hover:scale-[1.02]' : '';

  return (
    <div
      {...props}
      className={cn(baseStyles, variants[variant], hoverStyles, className)}
    >
      {children}
    </div>
  );
};