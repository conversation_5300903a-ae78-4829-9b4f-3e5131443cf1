import React, { useState, useEffect } from 'react';
import { Wifi, WifiOff, AlertCircle } from 'lucide-react';
import { cn } from '../../utils/cn';

interface ConnectionStatusProps {
  className?: string;
}

export const ConnectionStatus: React.FC<ConnectionStatusProps> = ({ className }) => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [showStatus, setShowStatus] = useState(false);

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      setShowStatus(true);
      // Hide the status after 3 seconds when back online
      setTimeout(() => setShowStatus(false), 3000);
    };

    const handleOffline = () => {
      setIsOnline(false);
      setShowStatus(true);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Show status initially if offline
    if (!navigator.onLine) {
      setShowStatus(true);
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  if (!showStatus) return null;

  return (
    <div className={cn(
      "fixed top-4 right-4 z-70 flex items-center space-x-2 px-3 py-2 rounded-lg shadow-lg transition-all duration-300",
      isOnline 
        ? "bg-green-500 text-white" 
        : "bg-red-500 text-white",
      className
    )}>
      {isOnline ? (
        <>
          <Wifi className="w-4 h-4" />
          <span className="text-sm font-medium">Back online</span>
        </>
      ) : (
        <>
          <WifiOff className="w-4 h-4" />
          <span className="text-sm font-medium">You're offline</span>
        </>
      )}
    </div>
  );
};

interface FirestoreStatusProps {
  className?: string;
}

export const FirestoreStatus: React.FC<FirestoreStatusProps> = ({ className }) => {
  const [hasFirestoreError, setHasFirestoreError] = useState(false);
  const [showError, setShowError] = useState(false);

  useEffect(() => {
    // Listen for Firestore errors in console
    const originalError = console.error;
    console.error = (...args) => {
      const message = args.join(' ');
      if (message.includes('firestore') || message.includes('Firestore') || message.includes('400')) {
        setHasFirestoreError(true);
        setShowError(true);
        // Hide after 5 seconds
        setTimeout(() => setShowError(false), 5000);
      }
      originalError.apply(console, args);
    };

    return () => {
      console.error = originalError;
    };
  }, []);

  if (!showError || !hasFirestoreError) return null;

  return (
    <div className={cn(
      "fixed top-16 right-4 z-50 flex items-center space-x-2 px-3 py-2 rounded-lg shadow-lg bg-yellow-500 text-white transition-all duration-300",
      className
    )}>
      <AlertCircle className="w-4 h-4" />
      <span className="text-sm font-medium">Database sync issues - working offline</span>
    </div>
  );
};
