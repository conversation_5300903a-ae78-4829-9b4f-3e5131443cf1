import React, { useState } from 'react';
import { Database, CheckCircle, AlertCircle } from 'lucide-react';
import { Card } from '../atoms/Card';
import { Button } from '../atoms/Button';
import { CarouselService } from '../../services/carouselService';
import { useAuth } from '../../hooks/useAuth';

interface CarouselSeederProps {
  onSeedingComplete?: () => void;
}

export const CarouselSeeder: React.FC<CarouselSeederProps> = ({ onSeedingComplete }) => {
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  // Default carousel slides to seed the database
  const defaultSlides = [
    {
      title: 'Plead Album',
      subtitle: 'Probe x Bolt.New',
      description: 'Music produced for the Plead Campaign - A PSA towards AI Impact & Safety.',
      gradient: 'from-violet-600 via-purple-600 to-indigo-600',
      accentColor: 'violet',
      icon: 'Headphones',
      albumId: null,
      albumTitle: null,
      isActive: true,
      order: 1,
      createdBy: user?.uid || 'system'
    },
    {
      title: 'VibeCode Album',
      subtitle: 'AiProbe.co x Bolt.New',
      description: 'With ❤️ for all the Vibe Coders Worldwide - Feel the Vibe in the Bolt.New Hackathon.',
      gradient: 'from-red-600 via-pink-600 to-rose-600',
      accentColor: 'red',
      icon: 'Music',
      albumId: null,
      albumTitle: null,
      isActive: true,
      order: 2,
      createdBy: user?.uid || 'system'
    }
  ];

  const handleSeed = async () => {
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      console.log('Starting carousel slides seeding...');

      // Check if slides already exist
      const existingSlides = await CarouselService.getAllSlides();

      if (existingSlides.length > 0) {
        console.log(`Found ${existingSlides.length} existing slides. Skipping seeding.`);
        setError('Slides already exist. Delete existing slides first if you want to reseed.');
        return;
      }

      // Create default slides
      for (const slideData of defaultSlides) {
        try {
          const createdSlide = await CarouselService.createSlide(slideData);
          console.log(`Created slide: ${createdSlide.title}`);
        } catch (error: any) {
          console.error(`Failed to create slide "${slideData.title}":`, error.message);
          throw error;
        }
      }

      console.log('Carousel slides seeding completed successfully!');
      setSuccess(true);
      onSeedingComplete?.();
    } catch (err: any) {
      console.error('Failed to seed carousel slides:', err.message);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="p-4 border-dashed border-2 border-border/50">
      <div className="text-center">
        <Database className="w-8 h-8 text-muted-foreground mx-auto mb-3" />
        <h4 className="font-medium text-foreground mb-2">Initialize Carousel Slides</h4>
        <p className="text-sm text-muted-foreground mb-4">
          Create default carousel slides to get started
        </p>

        {success && (
          <div className="flex items-center justify-center space-x-2 text-green-600 mb-4">
            <CheckCircle className="w-4 h-4" />
            <span className="text-sm">Default slides created successfully!</span>
          </div>
        )}

        {error && (
          <div className="flex items-center justify-center space-x-2 text-red-600 mb-4">
            <AlertCircle className="w-4 h-4" />
            <span className="text-sm">{error}</span>
          </div>
        )}

        <Button
          onClick={handleSeed}
          disabled={loading || success}
          variant={success ? "ghost" : "default"}
          className="w-full"
        >
          {loading ? 'Creating slides...' : success ? 'Slides created' : 'Create Default Slides'}
        </Button>
      </div>
    </Card>
  );
};
