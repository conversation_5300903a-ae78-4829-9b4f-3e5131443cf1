import React, { useState, useEffect } from 'react';
import { Music, Headphones, MessageCircle, Sparkles, Check } from 'lucide-react';
import { Card } from '../atoms/Card';
import { Button } from '../atoms/Button';
import { CarouselService } from '../../services/carouselService';
import { CarouselSlide, Album } from '../../types';
import { useAuth } from '../../hooks/useAuth';
import { cn } from '../../utils/cn';

interface CarouselSlideFormProps {
  slide?: CarouselSlide | null;
  albums: Album[];
  onSave: () => void;
  onCancel: () => void;
}

const ICON_OPTIONS = [
  { name: 'Headphones', icon: Headphones, label: 'Headphones' },
  { name: 'Music', icon: Music, label: 'Music' },
  { name: 'MessageCircle', icon: MessageCircle, label: 'Chat' },
  { name: 'Sparkles', icon: Sparkles, label: 'Sparkles' }
];

const GRADIENT_OPTIONS = [
  { name: 'from-violet-600 via-purple-600 to-indigo-600', label: 'Purple Gradient', accent: 'violet' },
  { name: 'from-red-600 via-pink-600 to-rose-600', label: 'Red Gradient', accent: 'red' },
  { name: 'from-blue-600 via-cyan-600 to-teal-600', label: 'Blue Gradient', accent: 'blue' },
  { name: 'from-orange-600 via-red-600 to-pink-600', label: 'Orange Gradient', accent: 'orange' },
  { name: 'from-green-600 via-emerald-600 to-teal-600', label: 'Green Gradient', accent: 'green' }
];

export const CarouselSlideForm: React.FC<CarouselSlideFormProps> = ({
  slide,
  albums,
  onSave,
  onCancel
}) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const [formData, setFormData] = useState({
    title: slide?.title || '',
    subtitle: slide?.subtitle || '',
    description: slide?.description || '',
    icon: slide?.icon || 'Music',
    gradient: slide?.gradient || 'from-violet-600 via-purple-600 to-indigo-600',
    accentColor: slide?.accentColor || 'violet',
    albumId: slide?.albumId || '',
    isActive: slide?.isActive ?? true,
    order: slide?.order || 1
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }
    if (!formData.subtitle.trim()) {
      newErrors.subtitle = 'Subtitle is required';
    }
    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm() || !user) return;

    setLoading(true);
    setError(null);

    try {
      const selectedAlbum = albums.find(album => album.id === formData.albumId);
      
      const slideData = {
        title: formData.title.trim(),
        subtitle: formData.subtitle.trim(),
        description: formData.description.trim(),
        icon: formData.icon,
        gradient: formData.gradient,
        accentColor: formData.accentColor,
        ...(formData.albumId && formData.albumId.trim() ? {
          albumId: formData.albumId.trim(),
          albumTitle: selectedAlbum?.title || null
        } : {
          albumId: null,
          albumTitle: null
        }),
        isActive: formData.isActive,
        order: formData.order,
        createdBy: user.id
      };

      if (slide) {
        // Update existing slide
        await CarouselService.updateSlide(slide.id, slideData);
      } else {
        // Create new slide
        await CarouselService.createSlide(slideData);
      }

      onSave();
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleGradientChange = (gradient: string, accent: string) => {
    setFormData(prev => ({
      ...prev,
      gradient,
      accentColor: accent
    }));
  };

  const selectedIcon = ICON_OPTIONS.find(opt => opt.name === formData.icon);
  const IconComponent = selectedIcon?.icon || Music;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[10000] flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <h3 className="text-lg font-semibold mb-6">
            {slide ? 'Edit Slide' : 'Create New Slide'}
          </h3>

          {error && (
            <div className="mb-4 p-3 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg">
              <p className="text-red-600 dark:text-red-400 text-sm">{error}</p>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Preview */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-foreground mb-2">Preview</label>
              <Card className="p-4 bg-secondary/30">
                <div className="flex items-center space-x-4">
                  <div className={cn(
                    "w-12 h-12 rounded-lg flex items-center justify-center",
                    `bg-gradient-to-br ${formData.gradient}`
                  )}>
                    <IconComponent className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h4 className="font-medium text-foreground">{formData.title || 'Slide Title'}</h4>
                    <p className="text-sm text-muted-foreground">{formData.subtitle || 'Slide Subtitle'}</p>
                    <p className="text-xs text-muted-foreground mt-1">{formData.description || 'Slide description...'}</p>
                  </div>
                </div>
              </Card>
            </div>

            {/* Basic Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Title *
                </label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary/20"
                  placeholder="e.g., Plead Album"
                />
                {errors.title && <p className="text-red-500 text-xs mt-1">{errors.title}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Subtitle *
                </label>
                <input
                  type="text"
                  value={formData.subtitle}
                  onChange={(e) => setFormData(prev => ({ ...prev, subtitle: e.target.value }))}
                  className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary/20"
                  placeholder="e.g., Probe x Bolt.New"
                />
                {errors.subtitle && <p className="text-red-500 text-xs mt-1">{errors.subtitle}</p>}
              </div>
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Description *
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
                className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary/20"
                placeholder="Describe what this slide represents..."
              />
              {errors.description && <p className="text-red-500 text-xs mt-1">{errors.description}</p>}
            </div>

            {/* Icon Selection */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">Icon</label>
              <div className="grid grid-cols-4 gap-2">
                {ICON_OPTIONS.map((option) => {
                  const OptionIcon = option.icon;
                  return (
                    <button
                      key={option.name}
                      type="button"
                      onClick={() => setFormData(prev => ({ ...prev, icon: option.name }))}
                      className={cn(
                        "p-3 border rounded-lg flex flex-col items-center space-y-1 transition-colors",
                        formData.icon === option.name
                          ? "border-primary bg-primary/10"
                          : "border-border hover:border-border/60"
                      )}
                    >
                      <OptionIcon className="w-5 h-5" />
                      <span className="text-xs">{option.label}</span>
                    </button>
                  );
                })}
              </div>
            </div>

            {/* Gradient Selection */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">Color Gradient</label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {GRADIENT_OPTIONS.map((option) => (
                  <button
                    key={option.name}
                    type="button"
                    onClick={() => handleGradientChange(option.name, option.accent)}
                    className={cn(
                      "relative p-3 border rounded-lg transition-colors",
                      formData.gradient === option.name
                        ? "border-primary"
                        : "border-border hover:border-border/60"
                    )}
                  >
                    <div className={cn(
                      "w-full h-8 rounded bg-gradient-to-r",
                      option.name
                    )} />
                    <p className="text-xs mt-1">{option.label}</p>
                    {formData.gradient === option.name && (
                      <Check className="absolute top-1 right-1 w-4 h-4 text-primary" />
                    )}
                  </button>
                ))}
              </div>
            </div>

            {/* Album Link */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Link to Album (Optional)
              </label>
              <select
                value={formData.albumId}
                onChange={(e) => setFormData(prev => ({ ...prev, albumId: e.target.value }))}
                className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary/20"
              >
                <option value="">No album linked</option>
                {albums.map((album) => (
                  <option key={album.id} value={album.id}>
                    {album.title} by {album.artist}
                  </option>
                ))}
              </select>
              <p className="text-xs text-muted-foreground mt-1">
                When linked, the Listen button will play this album
              </p>
            </div>

            {/* Settings */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Display Order
                </label>
                <input
                  type="number"
                  min="1"
                  value={formData.order}
                  onChange={(e) => setFormData(prev => ({ ...prev, order: parseInt(e.target.value) || 1 }))}
                  className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary/20"
                />
              </div>

              <div className="flex items-center space-x-2 pt-8">
                <input
                  type="checkbox"
                  id="isActive"
                  checked={formData.isActive}
                  onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                  className="rounded border-border"
                />
                <label htmlFor="isActive" className="text-sm font-medium text-foreground">
                  Active (visible on homepage)
                </label>
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-border">
              <Button
                type="button"
                variant="ghost"
                onClick={onCancel}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={loading}
                className="bg-gradient-to-r from-violet-600 to-purple-600 text-white"
              >
                {loading ? 'Saving...' : (slide ? 'Update Slide' : 'Create Slide')}
              </Button>
            </div>
          </form>
        </div>
      </Card>
    </div>
  );
};
