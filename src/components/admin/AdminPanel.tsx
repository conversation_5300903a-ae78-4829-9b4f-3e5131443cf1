import React, { useState, useEffect } from 'react';
import { Shield, Users, Music, Settings, Search, Crown, Mic, Headphones, Presentation } from 'lucide-react';
import { Card } from '../atoms/Card';
import { Button } from '../atoms/Button';
import { CarouselManagement } from './CarouselManagement';
import { useAuth } from '../../hooks/useAuth';
import { RoleService } from '../../services/roleService';
import { UserManagementService } from '../../services/userManagementService';
import { User, UserRole } from '../../types';
import { cn } from '../../utils/cn';

interface AdminPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

export const AdminPanel: React.FC<AdminPanelProps> = ({ isOpen, onClose }) => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<'users' | 'carousel'>('users');
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRole, setSelectedRole] = useState<UserRole | 'all'>('all');
  const [statistics, setStatistics] = useState<{
    total: number;
    admins: number;
    artists: number;
    listeners: number;
  } | null>(null);

  // Check if user has admin access
  if (!user || !RoleService.isAdmin(user)) {
    return null;
  }

  if (!isOpen) return null;

  const loadUsers = async () => {
    if (!user) return;
    
    setLoading(true);
    setError(null);
    
    try {
      let userData: User[];
      
      if (selectedRole === 'all') {
        userData = await UserManagementService.getAllUsers(user);
      } else {
        userData = await UserManagementService.getUsersByRole(user, selectedRole);
      }
      
      // Filter by search term if provided
      if (searchTerm) {
        userData = userData.filter(u => 
          u.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
          u.displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
          u.username.toLowerCase().includes(searchTerm.toLowerCase())
        );
      }
      
      setUsers(userData);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const loadStatistics = async () => {
    if (!user) return;
    
    try {
      const stats = await UserManagementService.getUserStatistics(user);
      setStatistics(stats);
    } catch (err: any) {
      console.warn('Failed to load statistics:', err.message);
    }
  };

  useEffect(() => {
    if (isOpen && user) {
      loadUsers();
      loadStatistics();
    }
  }, [isOpen, user, selectedRole, searchTerm]);

  const handleRoleChange = async (targetUserId: string, newRole: UserRole) => {
    if (!user) return;
    
    try {
      await UserManagementService.updateUserRole(user, targetUserId, newRole);
      await loadUsers();
      await loadStatistics();
    } catch (err: any) {
      setError(err.message);
    }
  };

  const getRoleIcon = (role: UserRole) => {
    switch (role) {
      case 'admin':
        return <Crown className="w-4 h-4 text-yellow-500" />;
      case 'artist':
        return <Mic className="w-4 h-4 text-purple-500" />;
      case 'listener':
        return <Headphones className="w-4 h-4 text-blue-500" />;
    }
  };

  const getRoleBadgeColor = (role: UserRole) => {
    switch (role) {
      case 'admin':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'artist':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'listener':
        return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4" style={{ zIndex: 10000 }}>
      <Card className="w-full max-w-6xl max-h-[90vh] overflow-hidden">
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-border/10">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-full flex items-center justify-center">
                <Shield className="w-5 h-5 text-white" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-foreground">Admin Panel</h2>
                <p className="text-sm text-muted-foreground">Manage users and platform settings</p>
              </div>
            </div>
            <Button variant="ghost" onClick={onClose}>
              ×
            </Button>
          </div>

          {/* Tabs */}
          <div className="border-b border-border/10">
            <div className="flex space-x-8 px-6">
              <button
                onClick={() => setActiveTab('users')}
                className={cn(
                  "py-4 px-2 border-b-2 font-medium text-sm transition-colors",
                  activeTab === 'users'
                    ? "border-primary text-primary"
                    : "border-transparent text-muted-foreground hover:text-foreground"
                )}
              >
                <Users className="w-4 h-4 inline mr-2" />
                User Management
              </button>
              <button
                onClick={() => setActiveTab('carousel')}
                className={cn(
                  "py-4 px-2 border-b-2 font-medium text-sm transition-colors",
                  activeTab === 'carousel'
                    ? "border-primary text-primary"
                    : "border-transparent text-muted-foreground hover:text-foreground"
                )}
              >
                <Presentation className="w-4 h-4 inline mr-2" />
                Carousel Management
              </button>
            </div>
          </div>

          {/* Tab Content */}
          {activeTab === 'users' && (
            <>
              {/* Statistics */}
          {statistics && (
            <div className="p-6 border-b border-border/10">
              <div className="grid grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-foreground">{statistics.total}</div>
                  <div className="text-sm text-muted-foreground">Total Users</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-600">{statistics.admins}</div>
                  <div className="text-sm text-muted-foreground">Admins</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">{statistics.artists}</div>
                  <div className="text-sm text-muted-foreground">Artists</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{statistics.listeners}</div>
                  <div className="text-sm text-muted-foreground">Listeners</div>
                </div>
              </div>
            </div>
          )}

          {/* Filters */}
          <div className="p-6 border-b border-border/10">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                  <input
                    type="text"
                    placeholder="Search users by email, name, or username..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-border/20 rounded-xl bg-background text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary/20"
                  />
                </div>
              </div>
              <select
                value={selectedRole}
                onChange={(e) => setSelectedRole(e.target.value as UserRole | 'all')}
                className="px-4 py-2 border border-border/20 rounded-xl bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary/20"
              >
                <option value="all">All Roles</option>
                <option value="admin">Admins</option>
                <option value="artist">Artists</option>
                <option value="listener">Listeners</option>
              </select>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <div className="p-4 bg-red-50 border-l-4 border-red-400 text-red-700">
              {error}
            </div>
          )}

          {/* Users List */}
          <div className="flex-1 overflow-y-auto p-6">
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin w-8 h-8 border-2 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
                <p className="text-muted-foreground">Loading users...</p>
              </div>
            ) : users.length === 0 ? (
              <div className="text-center py-8">
                <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">No users found</p>
              </div>
            ) : (
              <div className="space-y-3">
                {users.map((targetUser) => (
                  <div
                    key={targetUser.id}
                    className="flex items-center justify-between p-4 border border-border/10 rounded-xl hover:bg-foreground/5 transition-colors"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                        <span className="text-white font-semibold">
                          {targetUser.displayName.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <div>
                        <div className="font-medium text-foreground">{targetUser.displayName}</div>
                        <div className="text-sm text-muted-foreground">{targetUser.email}</div>
                        <div className="text-xs text-muted-foreground">@{targetUser.username}</div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <div className={cn(
                        "flex items-center space-x-2 px-3 py-1 rounded-full border text-xs font-medium",
                        getRoleBadgeColor(targetUser.role)
                      )}>
                        {getRoleIcon(targetUser.role)}
                        <span>{RoleService.getRoleDisplayName(targetUser.role)}</span>
                      </div>
                      
                      {targetUser.id !== user.id && (
                        <select
                          value={targetUser.role}
                          onChange={(e) => handleRoleChange(targetUser.id, e.target.value as UserRole)}
                          className="px-3 py-1 border border-border/20 rounded-lg bg-background text-foreground text-sm focus:outline-none focus:ring-2 focus:ring-primary/20"
                        >
                          <option value="admin">Admin</option>
                          <option value="artist">Artist</option>
                          <option value="listener">Listener</option>
                        </select>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
            </>
          )}

          {/* Carousel Management Tab */}
          {activeTab === 'carousel' && (
            <div className="flex-1 overflow-y-auto p-6">
              <CarouselManagement />
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};
