import React, { useState, useEffect } from 'react';
import { Plus, Edit3, Trash2, Eye, EyeOff, GripVertical, Music, Headphones, MessageCircle, Sparkles } from 'lucide-react';
import { Card } from '../atoms/Card';
import { Button } from '../atoms/Button';
import { CarouselService } from '../../services/carouselService';
import { AlbumService } from '../../services/albumService';
import { CarouselSlideForm } from './CarouselSlideForm';
import { CarouselSeeder } from './CarouselSeeder';
import { CarouselSlide, Album } from '../../types';
import { cn } from '../../utils/cn';

interface CarouselManagementProps {
  className?: string;
}

const ICON_OPTIONS = [
  { name: 'Headphones', icon: Headphones, label: 'Headphones' },
  { name: 'Music', icon: Music, label: 'Music' },
  { name: 'MessageCircle', icon: MessageCircle, label: 'Chat' },
  { name: 'Sparkles', icon: Sparkles, label: 'Sparkles' }
];

const GRADIENT_OPTIONS = [
  { name: 'from-violet-600 via-purple-600 to-indigo-600', label: 'Purple Gradient', accent: 'violet' },
  { name: 'from-red-600 via-pink-600 to-rose-600', label: 'Red Gradient', accent: 'red' },
  { name: 'from-blue-600 via-cyan-600 to-teal-600', label: 'Blue Gradient', accent: 'blue' },
  { name: 'from-orange-600 via-red-600 to-pink-600', label: 'Orange Gradient', accent: 'orange' },
  { name: 'from-green-600 via-emerald-600 to-teal-600', label: 'Green Gradient', accent: 'green' }
];

export const CarouselManagement: React.FC<CarouselManagementProps> = ({ className }) => {
  const [slides, setSlides] = useState<CarouselSlide[]>([]);
  const [albums, setAlbums] = useState<Album[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingSlide, setEditingSlide] = useState<CarouselSlide | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const [slidesData, albumsData] = await Promise.all([
        CarouselService.getAllSlides(),
        AlbumService.getPublishedAlbums()
      ]);
      
      setSlides(slidesData);
      setAlbums(albumsData);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleStatus = async (slideId: string, currentStatus: boolean) => {
    try {
      await CarouselService.toggleSlideStatus(slideId, !currentStatus);
      await loadData();
    } catch (err: any) {
      setError(err.message);
    }
  };

  const handleDeleteSlide = async (slideId: string) => {
    if (!confirm('Are you sure you want to delete this slide?')) return;
    
    try {
      await CarouselService.deleteSlide(slideId);
      await loadData();
    } catch (err: any) {
      setError(err.message);
    }
  };

  if (loading) {
    return (
      <div className={cn("flex items-center justify-center h-64", className)}>
        <div className="text-muted-foreground">Loading carousel slides...</div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-foreground">Carousel Management</h3>
          <p className="text-sm text-muted-foreground">Manage home page carousel slides and their album links</p>
        </div>
        <Button
          onClick={() => setShowCreateForm(true)}
          className="bg-gradient-to-r from-violet-600 to-purple-600 text-white"
        >
          <Plus className="w-4 h-4 mr-2" />
          Add Slide
        </Button>
      </div>

      {error && (
        <Card className="p-4 border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/20">
          <p className="text-red-600 dark:text-red-400 text-sm">{error}</p>
        </Card>
      )}

      {/* Slides List */}
      <div className="space-y-4">
        {slides.length === 0 ? (
          <div className="space-y-6">
            <Card className="p-8 text-center">
              <div className="text-muted-foreground">
                <Music className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium mb-2">No carousel slides yet</p>
                <p className="text-sm mb-6">Create your first slide to get started</p>
              </div>
            </Card>

            <CarouselSeeder onSeedingComplete={loadData} />
          </div>
        ) : (
          slides.map((slide) => {
            const IconComponent = ICON_OPTIONS.find(opt => opt.name === slide.icon)?.icon || Music;
            const linkedAlbum = albums.find(album => album.id === slide.albumId);
            
            return (
              <Card key={slide.id} className="p-4">
                <div className="flex items-center space-x-4">
                  {/* Drag Handle */}
                  <div className="cursor-move text-muted-foreground">
                    <GripVertical className="w-4 h-4" />
                  </div>

                  {/* Slide Preview */}
                  <div className="flex-1 flex items-center space-x-4">
                    {/* Icon */}
                    <div className={cn(
                      "w-12 h-12 rounded-lg flex items-center justify-center",
                      `bg-gradient-to-br ${slide.gradient}`
                    )}>
                      <IconComponent className="w-6 h-6 text-white" />
                    </div>

                    {/* Content */}
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="font-medium text-foreground">{slide.title}</h4>
                        <span className="text-xs text-muted-foreground">#{slide.order}</span>
                        {!slide.isActive && (
                          <span className="text-xs bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                            Inactive
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground mb-1">{slide.subtitle}</p>
                      {linkedAlbum && (
                        <div className="flex items-center space-x-1 text-xs text-blue-600 dark:text-blue-400">
                          <Music className="w-3 h-3" />
                          <span>Linked to: {linkedAlbum.title}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleToggleStatus(slide.id, slide.isActive)}
                      className={cn(
                        "w-8 h-8 p-0",
                        slide.isActive ? "text-green-600" : "text-gray-400"
                      )}
                    >
                      {slide.isActive ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
                    </Button>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setEditingSlide(slide)}
                      className="w-8 h-8 p-0"
                    >
                      <Edit3 className="w-4 h-4" />
                    </Button>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteSlide(slide.id)}
                      className="w-8 h-8 p-0 text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </Card>
            );
          })
        )}
      </div>

      {/* Create/Edit Form Modal */}
      {(showCreateForm || editingSlide) && (
        <CarouselSlideForm
          slide={editingSlide}
          albums={albums}
          onSave={() => {
            setShowCreateForm(false);
            setEditingSlide(null);
            loadData();
          }}
          onCancel={() => {
            setShowCreateForm(false);
            setEditingSlide(null);
          }}
        />
      )}
    </div>
  );
};
