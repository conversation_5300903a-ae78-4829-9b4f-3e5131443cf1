import React, { useState, useRef } from 'react';
import { X, Upload, Music, Image, FileAudio, AlertCircle, Disc, Play, ChevronRight, ChevronLeft, Plus } from 'lucide-react';
import { Card } from '../atoms/Card';
import { Button } from '../atoms/Button';
import { cn } from '../../utils/cn';
import { useUploadStore } from '../../store/uploadStore';
import { useMusicStore } from '../../store/musicStore';
import { useAuth } from '../../hooks/useAuth';
import { RoleService } from '../../services/roleService';
import { MusicService } from '../../services/musicService';
import { AlbumService } from '../../services/albumService';
import { UploadTrack } from '../../types';
import { getAudioMetadata, formatFileSize, estimateBitrate } from '../../utils/audioUtils';
import { writeBatch, doc, getDoc } from 'firebase/firestore';
import { db } from '../../lib/firebase';

interface UploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUploadComplete?: () => void;
}

export const UploadModal: React.FC<UploadModalProps> = ({
  isOpen,
  onClose,
  onUploadComplete
}) => {
  const { user } = useAuth();

  // Debug user state
  React.useEffect(() => {
    console.log('🔍 UploadModal user state changed:', user);
    console.log('🔍 User ID:', user?.id);
    console.log('🔍 User object:', user);

    // Also check Firebase auth state
    const checkFirebaseAuth = async () => {
      const { auth } = await import('../../lib/firebase');
      console.log('🔍 Firebase auth.currentUser:', auth.currentUser);
      console.log('🔍 Firebase user.uid:', auth.currentUser?.uid);
    };
    checkFirebaseAuth();
  }, [user]);
  const { triggerLibraryRefresh } = useMusicStore();
  const {
    session,
    isUploading,
    error,
    startSession,
    clearSession,
    setCurrentStep,
    setSingleTrack,
    updateSingleMetadata,
    setSingleCover,
    setAlbumMetadata,
    setAlbumCover,
    addTrackToAlbum,
    updateAlbumTrack,
    removeTrackFromAlbum,
    setTrackProgress,
    setTrackStatus,
    setUploading,
    setPublishStatus,
    setError
  } = useUploadStore();

  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const coverInputRef = useRef<HTMLInputElement>(null);

  if (!isOpen) return null;

  // Check if user has upload permissions
  if (!user || !RoleService.canUploadMusic(user)) {
    console.log('🔴 Upload modal blocked - user:', user, 'canUpload:', user ? RoleService.canUploadMusic(user) : false);
    return null;
  }

  console.log('🟢 Upload modal allowed - user:', user, 'canUpload:', RoleService.canUploadMusic(user));

  const handleClose = () => {
    if (!isUploading) {
      clearSession();
      onClose();
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    const files = Array.from(e.dataTransfer.files);
    handleFiles(files);
  };

  const handleFiles = (files: File[]) => {
    const audioFiles = files.filter(file => file.type.startsWith('audio/'));

    // Ensure we have a fallback artist name
    const artistName = user?.displayName || user?.email?.split('@')[0] || 'Unknown Artist';

    if (session?.type === 'single' && audioFiles.length > 0) {
      const file = audioFiles[0];
      const track: UploadTrack = {
        id: Math.random().toString(36).substring(2, 11),
        file,
        metadata: {
          title: file.name.replace(/\.[^/.]+$/, ''),
          artist: artistName,
        },
        progress: 0,
        status: 'pending'
      };
      setSingleTrack(track);
      setCurrentStep(2);
    } else if (session?.type === 'album') {
      audioFiles.forEach(file => {
        const track: UploadTrack = {
          id: Math.random().toString(36).substring(2, 11),
          file,
          metadata: {
            title: file.name.replace(/\.[^/.]+$/, ''),
            artist: artistName,
          },
          progress: 0,
          status: 'pending'
        };
        addTrackToAlbum(track);
      });
      if (audioFiles.length > 0) {
        setCurrentStep(2);
      }
    }
  };

  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFiles(Array.from(e.target.files));
    }
  };

  const handleCoverSelect = () => {
    coverInputRef.current?.click();
  };

  const handleCoverInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      if (session?.type === 'single') {
        setSingleCover(file);
      } else if (session?.type === 'album') {
        setAlbumCover(file);
      }
    }
  };

  const uploadTrack = async (track: UploadTrack): Promise<string> => {
    // Import Firebase auth to check current user directly
    const { auth } = await import('../../lib/firebase');
    const firebaseUser = auth.currentUser;

    console.log('🔍 uploadTrack called with user:', user);
    console.log('🔍 Firebase auth.currentUser:', firebaseUser);
    console.log('🔍 Firebase user.uid:', firebaseUser?.uid);
    console.log('🔍 user.id:', user?.id);
    console.log('🔍 user object keys:', user ? Object.keys(user) : 'user is null');
    console.log('🔍 typeof user.id:', typeof user?.id);
    console.log('🔍 user.id === undefined:', user?.id === undefined);
    console.log('🔍 user.id === null:', user?.id === null);
    console.log('🔍 user.id === "":', user?.id === '');

    if (!user) throw new Error('User not authenticated');
    if (!firebaseUser) throw new Error('Firebase user not authenticated');

    // Use Firebase user ID as fallback if user.id is undefined
    const userId = user.id || firebaseUser.uid;
    if (!userId) throw new Error(`Both user.id and firebaseUser.uid are missing. User object: ${JSON.stringify(user)}`);

    setTrackStatus(track.id, 'uploading');

    try {
      // Extract comprehensive audio metadata first
      setTrackProgress(track.id, 10);
      let audioMetadata;
      try {
        audioMetadata = await getAudioMetadata(track.file);
        console.log(`Extracted metadata for ${track.metadata.title}:`, audioMetadata);
      } catch (metadataError) {
        console.warn('Failed to extract audio metadata:', metadataError);
        // Continue with basic metadata if extraction fails
        audioMetadata = {
          duration: 0,
          fileSize: track.file.size,
          format: track.file.type
        };
      }

      // Upload audio file
      setTrackProgress(track.id, 30);
      const audioUrl = await MusicService.uploadTrackFile(track.file, userId);

      // Upload cover if exists
      setTrackProgress(track.id, 70);
      let coverUrl: string | undefined;
      if (track.coverFile) {
        coverUrl = await MusicService.uploadCoverImage(track.coverFile, userId);
      }

      // Create track document with enhanced metadata
      setTrackProgress(track.id, 90);
      const trackData: any = {
        title: track.metadata.title,
        artist: track.metadata.artist,
        duration: audioMetadata.duration,
        url: audioUrl,
        uploadedBy: userId,
        status: 'draft', // Always create as draft initially
        createdAt: new Date(),
        updatedAt: new Date(),

        // Enhanced metadata
        fileSize: audioMetadata.fileSize,
        format: audioMetadata.format,
        bitrate: audioMetadata.bitrate || estimateBitrate(audioMetadata.fileSize, audioMetadata.duration)
      };

      // Only add optional fields if they have values
      if (coverUrl) trackData.coverUrl = coverUrl;
      if (track.metadata.genre) trackData.genre = track.metadata.genre;
      if (track.metadata.mood) trackData.mood = track.metadata.mood;
      if (track.metadata.trackNumber) trackData.trackNumber = track.metadata.trackNumber;
      // Note: albumId is omitted for single tracks since it's optional

      const createdTrack = await MusicService.createTrack(trackData);

      setTrackProgress(track.id, 100);
      setTrackStatus(track.id, 'completed');

      return createdTrack.id;
    } catch (error: any) {
      setTrackStatus(track.id, 'error', error.message);
      throw error;
    }
  };

  const handleUpload = async () => {
    // Import Firebase auth to check current user directly
    const { auth } = await import('../../lib/firebase');
    const firebaseUser = auth.currentUser;

    console.log('🔍 handleUpload called with user:', user);
    console.log('🔍 handleUpload user.id:', user?.id);
    console.log('🔍 handleUpload Firebase user:', firebaseUser);
    console.log('🔍 handleUpload session:', session);

    if (!user || !session) return;
    if (!firebaseUser) throw new Error('Firebase user not authenticated');

    // Use Firebase user ID as fallback if user.id is undefined
    const userId = user.id || firebaseUser.uid;
    if (!userId) throw new Error(`Both user.id and firebaseUser.uid are missing`);

    console.log('🟡 handleUpload called with session:', session);
    console.log('🟡 Session status at start of handleUpload:', session.status);
    console.log('🟡 Using userId:', userId);

    setUploading(true);
    setError(null);

    // Get the latest session state to avoid closure issues
    const currentSession = useUploadStore.getState().session;
    console.log('🟡 Current session from store:', currentSession);
    console.log('🟡 Current session status from store:', currentSession?.status);

    if (!currentSession) {
      console.error('🔴 No current session found in store');
      setUploading(false);
      return;
    }

    try {
      console.log('🟡 Starting upload process. Session status:', currentSession.status);

      if (currentSession.type === 'single' && currentSession.single) {
        const trackId = await uploadTrack(currentSession.single);
        console.log('🟢 Track uploaded with ID:', trackId);

        // If user chose to publish, publish the track after upload is complete
        console.log('🟡 Checking if should publish. Session status:', currentSession.status);
        if (currentSession.status === 'published') {
          console.log('🟢 Attempting to publish track with ID:', trackId);
          try {
            await MusicService.publishTrack(trackId);
            console.log('🟢 Track published successfully:', trackId);
          } catch (publishError: any) {
            console.error('🔴 Failed to publish track:', publishError);
            // Don't throw here - let the user know the upload succeeded but publishing failed
            setError(`Upload successful, but failed to publish: ${publishError.message}. You can publish it later from your drafts.`);
            return; // Exit early to prevent closing modal
          }
        } else {
          console.log('🟡 Track saved as draft. Session status:', currentSession.status);
        }
      } else if (currentSession.type === 'album' && currentSession.album) {
        console.log('Starting album upload process');

        // Upload all tracks first
        const trackIds: string[] = [];
        for (const track of currentSession.album.tracks) {
          const trackId = await uploadTrack(track);
          trackIds.push(trackId);
          console.log('Album track uploaded:', trackId);
        }

        // Upload album cover if exists
        let coverUrl: string | undefined;
        if (currentSession.album.coverFile) {
          coverUrl = await AlbumService.uploadAlbumCover(currentSession.album.coverFile, userId, 'temp');
          console.log('Album cover uploaded:', coverUrl);
        }

        // Create album
        const albumData: any = {
          title: currentSession.album.metadata.title,
          artist: currentSession.album.metadata.artist,
          trackIds,
          uploadedBy: userId,
          status: 'draft' // Always create as draft initially
        };

        // Only add optional fields if they have values
        if (currentSession.album.metadata.description) albumData.description = currentSession.album.metadata.description;
        if (coverUrl) albumData.coverUrl = coverUrl;
        if (currentSession.album.metadata.genre) albumData.genre = currentSession.album.metadata.genre;

        const album = await AlbumService.createAlbum(albumData);
        console.log('Album created with ID:', album.id);

        // Update tracks to reference this album and inherit cover if needed
        const batch = writeBatch(db);
        for (let i = 0; i < trackIds.length; i++) {
          const trackRef = doc(db, 'tracks', trackIds[i]);
          const trackDoc = await getDoc(trackRef);

          if (trackDoc.exists()) {
            const trackData = trackDoc.data();
            const updateData: any = {
              albumId: album.id,
              albumTitle: album.title,
              trackNumber: i + 1,
              updatedAt: new Date()
            };

            // If track doesn't have a cover but album does, inherit album cover
            if (!trackData.coverUrl && coverUrl) {
              updateData.coverUrl = coverUrl;
            }

            batch.update(trackRef, updateData);
          }
        }
        await batch.commit();
        console.log('Tracks updated with album reference and cover inheritance');

        if (currentSession.status === 'published') {
          console.log('Attempting to publish album with ID:', album.id);
          try {
            await AlbumService.publishAlbum(album.id);
            console.log('Album published successfully:', album.id);
          } catch (publishError: any) {
            console.error('Failed to publish album:', publishError);
            // Don't throw here - let the user know the upload succeeded but publishing failed
            setError(`Upload successful, but failed to publish: ${publishError.message}. You can publish it later from your drafts.`);
            return; // Exit early to prevent closing modal
          }
        }
      }

      console.log('Upload process completed successfully');

      // Success - close modal and trigger refresh
      handleClose();
      // Trigger library refresh to update profile page
      triggerLibraryRefresh();
      // Small delay to ensure database is updated before refreshing
      setTimeout(() => {
        onUploadComplete?.();
      }, 500);
    } catch (error: any) {
      console.error('Upload process failed:', error);
      setError(error.message);
    } finally {
      setUploading(false);
    }
  };

  const renderStepContent = () => {
    if (!session) {
      return renderTypeSelection();
    }

    switch (session.currentStep) {
      case 1:
        return renderTypeSelection();
      case 2:
        return renderFileUpload();
      case 3:
        return session.type === 'album' ? renderAlbumMetadata() : renderReview();
      case 4:
        return renderReview();
      default:
        return renderTypeSelection();
    }
  };

  const renderTypeSelection = () => (
    <div className="text-center space-y-6">
      <div>
        <h3 className="text-xl font-semibold text-foreground mb-2">What would you like to upload?</h3>
        <p className="text-muted-foreground">Choose between uploading a single track or creating an album</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-md mx-auto">
        <Button
          variant="outline"
          className="h-32 flex-col space-y-3 hover:bg-primary/5"
          onClick={() => {
            startSession('single');
            setCurrentStep(2);
          }}
        >
          <Play className="w-8 h-8 text-primary" />
          <div>
            <div className="font-semibold">Single Track</div>
            <div className="text-sm text-muted-foreground">Upload one song</div>
          </div>
        </Button>

        <Button
          variant="outline"
          className="h-32 flex-col space-y-3 hover:bg-primary/5"
          onClick={() => {
            startSession('album');
            setCurrentStep(2);
          }}
        >
          <Disc className="w-8 h-8 text-primary" />
          <div>
            <div className="font-semibold">Album</div>
            <div className="text-sm text-muted-foreground">Upload multiple songs</div>
          </div>
        </Button>
      </div>
    </div>
  );

  const renderFileUpload = () => {
    const hasFiles = session?.type === 'single' ? !!session.single : (session?.album?.tracks.length || 0) > 0;

    return (
      <div className="space-y-6">
        <div className="text-center">
          <h3 className="text-xl font-semibold text-foreground mb-2">
            {session?.type === 'single' ? 'Upload Your Track' : 'Upload Album Tracks'}
          </h3>
          <p className="text-muted-foreground">
            {session?.type === 'single'
              ? 'Select your audio file and add track details'
              : 'Add multiple tracks to create your album'
            }
          </p>
        </div>

        {/* File Drop Area */}
        {!hasFiles && (
          <div
            className={cn(
              'border-2 border-dashed rounded-2xl p-12 text-center transition-colors',
              dragActive
                ? 'border-primary bg-primary/5'
                : 'border-border/20 hover:border-border/40'
            )}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <div className="space-y-4">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full mx-auto flex items-center justify-center">
                <FileAudio className="w-8 h-8 text-white" />
              </div>
              <div>
                <h4 className="text-lg font-semibold text-foreground mb-2">
                  Drop your music files here
                </h4>
                <p className="text-sm text-muted-foreground mb-4">
                  Supports MP3, WAV, FLAC, and other audio formats
                </p>
                <Button onClick={handleFileSelect}>
                  <Upload className="w-4 h-4 mr-2" />
                  Choose Files
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Single Track Form */}
        {session?.type === 'single' && session.single && (
          <Card className="p-6">
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                  <Music className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h4 className="font-medium text-foreground">{session.single.file.name}</h4>
                  <p className="text-sm text-muted-foreground">
                    {formatFileSize(session.single.file.size)}
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <input
                  type="text"
                  placeholder="Track Title *"
                  value={session.single.metadata.title}
                  onChange={(e) => updateSingleMetadata({ title: e.target.value })}
                  className="px-3 py-2 bg-secondary/20 border border-border/20 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 text-foreground"
                />
                <input
                  type="text"
                  placeholder="Artist *"
                  value={session.single.metadata.artist}
                  onChange={(e) => updateSingleMetadata({ artist: e.target.value })}
                  className="px-3 py-2 bg-secondary/20 border border-border/20 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 text-foreground"
                />
                <select
                  value={session.single.metadata.genre || ''}
                  onChange={(e) => updateSingleMetadata({ genre: e.target.value })}
                  className="px-3 py-2 bg-secondary/20 border border-border/20 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 text-foreground"
                >
                  <option value="">Select Genre</option>
                  <option value="Afropop">Afropop</option>
                  <option value="Afrobeat">Afrobeat</option>
                  <option value="Amapiano">Amapiano</option>
                  <option value="Afro-fusion">Afro-fusion</option>
                  <option value="Highlife">Highlife</option>
                  <option value="Dancehall">Dancehall</option>
                  <option value="Reggae">Reggae</option>
                  <option value="Electronic">Electronic</option>
                  <option value="Hip Hop">Hip Hop</option>
                  <option value="Rock">Rock</option>
                  <option value="Pop">Pop</option>
                  <option value="Jazz">Jazz</option>
                  <option value="Classical">Classical</option>
                  <option value="Indie">Indie</option>
                  <option value="R&B">R&B</option>
                </select>
                <select
                  value={session.single.metadata.mood || ''}
                  onChange={(e) => updateSingleMetadata({ mood: e.target.value })}
                  className="px-3 py-2 bg-secondary/20 border border-border/20 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 text-foreground"
                >
                  <option value="">Select Mood</option>
                  <option value="Vibe">Vibe</option>
                  <option value="Energetic">Energetic</option>
                  <option value="Chill">Chill</option>
                  <option value="Uplifting">Uplifting</option>
                  <option value="Groovy">Groovy</option>
                  <option value="Melancholic">Melancholic</option>
                  <option value="Dark">Dark</option>
                  <option value="Romantic">Romantic</option>
                  <option value="Peaceful">Peaceful</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-foreground mb-2">Cover Image</label>
                <Button variant="outline" onClick={handleCoverSelect}>
                  <Image className="w-4 h-4 mr-2" />
                  {session.single.coverFile ? 'Change Cover' : 'Add Cover'}
                </Button>
                {session.single.coverFile && (
                  <p className="text-sm text-muted-foreground mt-2">
                    {session.single.coverFile.name}
                  </p>
                )}
              </div>
            </div>
          </Card>
        )}

        {/* Album Tracks List */}
        {session?.type === 'album' && session.album && session.album.tracks.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-semibold text-foreground">Album Tracks ({session.album.tracks.length})</h4>
              <Button variant="outline" onClick={handleFileSelect}>
                <Plus className="w-4 h-4 mr-2" />
                Add More Tracks
              </Button>
            </div>

            <div className="space-y-3">
              {session.album.tracks.map((track, index) => (
                <Card key={track.id} className="p-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                          <span className="text-white text-sm font-medium">{index + 1}</span>
                        </div>
                        <div>
                          <h5 className="font-medium text-foreground">{track.file.name}</h5>
                          <p className="text-sm text-muted-foreground">
                            {formatFileSize(track.file.size)}
                          </p>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeTrackFromAlbum(track.id)}
                        className="h-8 w-8 p-0 text-muted-foreground hover:text-red-500"
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      <input
                        type="text"
                        placeholder="Track Title *"
                        value={track.metadata.title}
                        onChange={(e) => updateAlbumTrack(track.id, {
                          metadata: { ...track.metadata, title: e.target.value }
                        })}
                        className="px-3 py-2 bg-secondary/20 border border-border/20 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 text-foreground text-sm"
                      />
                      <input
                        type="text"
                        placeholder="Artist *"
                        value={track.metadata.artist}
                        onChange={(e) => updateAlbumTrack(track.id, {
                          metadata: { ...track.metadata, artist: e.target.value }
                        })}
                        className="px-3 py-2 bg-secondary/20 border border-border/20 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 text-foreground text-sm"
                      />
                      <select
                        value={track.metadata.genre || ''}
                        onChange={(e) => updateAlbumTrack(track.id, {
                          metadata: { ...track.metadata, genre: e.target.value }
                        })}
                        className="px-3 py-2 bg-secondary/20 border border-border/20 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 text-foreground text-sm"
                      >
                        <option value="">Select Genre</option>
                        <option value="Afropop">Afropop</option>
                        <option value="Afrobeat">Afrobeat</option>
                        <option value="Amapiano">Amapiano</option>
                        <option value="Afro-fusion">Afro-fusion</option>
                        <option value="Highlife">Highlife</option>
                        <option value="Dancehall">Dancehall</option>
                        <option value="Reggae">Reggae</option>
                        <option value="Electronic">Electronic</option>
                        <option value="Hip Hop">Hip Hop</option>
                        <option value="Rock">Rock</option>
                        <option value="Pop">Pop</option>
                        <option value="Jazz">Jazz</option>
                        <option value="Classical">Classical</option>
                        <option value="Indie">Indie</option>
                        <option value="R&B">R&B</option>
                      </select>
                      <select
                        value={track.metadata.mood || ''}
                        onChange={(e) => updateAlbumTrack(track.id, {
                          metadata: { ...track.metadata, mood: e.target.value }
                        })}
                        className="px-3 py-2 bg-secondary/20 border border-border/20 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 text-foreground text-sm"
                      >
                        <option value="">Select Mood</option>
                        <option value="Vibe">Vibe</option>
                        <option value="Energetic">Energetic</option>
                        <option value="Chill">Chill</option>
                        <option value="Uplifting">Uplifting</option>
                        <option value="Groovy">Groovy</option>
                        <option value="Melancholic">Melancholic</option>
                        <option value="Dark">Dark</option>
                        <option value="Romantic">Romantic</option>
                        <option value="Peaceful">Peaceful</option>
                      </select>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderAlbumMetadata = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-xl font-semibold text-foreground mb-2">Album Details</h3>
        <p className="text-muted-foreground">Add information about your album</p>
      </div>

      <Card className="p-6">
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <input
              type="text"
              placeholder="Album Title *"
              value={session?.album?.metadata.title || ''}
              onChange={(e) => setAlbumMetadata({
                title: e.target.value,
                artist: session?.album?.metadata.artist || '',
                description: session?.album?.metadata.description,
                genre: session?.album?.metadata.genre
              })}
              className="px-3 py-2 bg-secondary/20 border border-border/20 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 text-foreground"
            />
            <input
              type="text"
              placeholder="Artist *"
              value={session?.album?.metadata.artist || ''}
              onChange={(e) => setAlbumMetadata({
                title: session?.album?.metadata.title || '',
                artist: e.target.value,
                description: session?.album?.metadata.description,
                genre: session?.album?.metadata.genre
              })}
              className="px-3 py-2 bg-secondary/20 border border-border/20 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 text-foreground"
            />
          </div>

          <textarea
            placeholder="Album Description"
            value={session?.album?.metadata.description || ''}
            onChange={(e) => setAlbumMetadata({
              title: session?.album?.metadata.title || '',
              artist: session?.album?.metadata.artist || '',
              description: e.target.value,
              genre: session?.album?.metadata.genre
            })}
            className="w-full px-3 py-2 bg-secondary/20 border border-border/20 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 text-foreground"
            rows={3}
          />

          <select
            value={session?.album?.metadata.genre || ''}
            onChange={(e) => setAlbumMetadata({
              title: session?.album?.metadata.title || '',
              artist: session?.album?.metadata.artist || '',
              description: session?.album?.metadata.description,
              genre: e.target.value
            })}
            className="px-3 py-2 bg-secondary/20 border border-border/20 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 text-foreground"
          >
            <option value="">Select Genre</option>
            <option value="Afropop">Afropop</option>
            <option value="Afrobeat">Afrobeat</option>
            <option value="Amapiano">Amapiano</option>
            <option value="Afro-fusion">Afro-fusion</option>
            <option value="Highlife">Highlife</option>
            <option value="Dancehall">Dancehall</option>
            <option value="Reggae">Reggae</option>
            <option value="Electronic">Electronic</option>
            <option value="Hip Hop">Hip Hop</option>
            <option value="Rock">Rock</option>
            <option value="Pop">Pop</option>
            <option value="Jazz">Jazz</option>
            <option value="Classical">Classical</option>
            <option value="Indie">Indie</option>
            <option value="R&B">R&B</option>
          </select>

          <div>
            <label className="block text-sm font-medium text-foreground mb-2">Album Cover</label>
            <Button variant="outline" onClick={handleCoverSelect}>
              <Image className="w-4 h-4 mr-2" />
              {session?.album?.coverFile ? 'Change Cover' : 'Add Cover'}
            </Button>
            {session?.album?.coverFile && (
              <p className="text-sm text-muted-foreground mt-2">
                {session.album.coverFile.name}
              </p>
            )}
          </div>
        </div>
      </Card>
    </div>
  );

  const renderReview = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-xl font-semibold text-foreground mb-2">Review & Publish</h3>
        <p className="text-muted-foreground">
          Review your {session?.type} before publishing or saving as draft
        </p>
      </div>

      {session?.type === 'single' && session.single && (
        <Card className="p-6">
          <div className="space-y-4">
            <h4 className="font-semibold text-foreground">Single Track</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Title:</span>
                <p className="font-medium">{session.single.metadata.title}</p>
              </div>
              <div>
                <span className="text-muted-foreground">Artist:</span>
                <p className="font-medium">{session.single.metadata.artist}</p>
              </div>
              {session.single.metadata.genre && (
                <div>
                  <span className="text-muted-foreground">Genre:</span>
                  <p className="font-medium">{session.single.metadata.genre}</p>
                </div>
              )}
              {session.single.metadata.mood && (
                <div>
                  <span className="text-muted-foreground">Mood:</span>
                  <p className="font-medium">{session.single.metadata.mood}</p>
                </div>
              )}
            </div>
          </div>
        </Card>
      )}

      {session?.type === 'album' && session.album && (
        <div className="space-y-4">
          <Card className="p-6">
            <div className="space-y-4">
              <h4 className="font-semibold text-foreground">Album Information</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Title:</span>
                  <p className="font-medium">{session.album.metadata.title}</p>
                </div>
                <div>
                  <span className="text-muted-foreground">Artist:</span>
                  <p className="font-medium">{session.album.metadata.artist}</p>
                </div>
                {session.album.metadata.genre && (
                  <div>
                    <span className="text-muted-foreground">Genre:</span>
                    <p className="font-medium">{session.album.metadata.genre}</p>
                  </div>
                )}
                <div>
                  <span className="text-muted-foreground">Tracks:</span>
                  <p className="font-medium">{session.album.tracks.length}</p>
                </div>
              </div>
              {session.album.metadata.description && (
                <div>
                  <span className="text-muted-foreground">Description:</span>
                  <p className="font-medium">{session.album.metadata.description}</p>
                </div>
              )}
            </div>
          </Card>

          <Card className="p-6">
            <h4 className="font-semibold text-foreground mb-4">Track List</h4>
            <div className="space-y-2">
              {session.album.tracks.map((track, index) => (
                <div key={track.id} className="flex items-center justify-between p-3 bg-secondary/10 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <span className="text-sm text-muted-foreground w-6">{index + 1}.</span>
                    <div>
                      <p className="font-medium">{track.metadata.title}</p>
                      <p className="text-sm text-muted-foreground">{track.metadata.artist}</p>
                    </div>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {formatFileSize(track.file.size)}
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </div>
      )}
    </div>
  );

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4" style={{ zIndex: 10000 }}>
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto" variant="glass">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold text-foreground">Upload Music</h2>
              {session && (
                <div className="flex items-center space-x-2 mt-2">
                  <div className="text-sm text-muted-foreground">
                    Step {session.currentStep} of {session.totalSteps}
                  </div>
                  <div className="flex-1 bg-secondary/20 rounded-full h-2 max-w-32">
                    <div
                      className="bg-primary h-2 rounded-full transition-all duration-300"
                      style={{ width: `${(session.currentStep / session.totalSteps) * 100}%` }}
                    />
                  </div>
                </div>
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="h-8 w-8 p-0"
              disabled={isUploading}
            >
              <X className="w-4 h-4" />
            </Button>
          </div>

          {/* Error Display */}
          {error && (
            <div className="mb-6 p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
              <div className="flex items-center space-x-2">
                <AlertCircle className="w-5 h-5 text-red-500" />
                <p className="text-red-500">{error}</p>
              </div>
            </div>
          )}

          {/* Step Content */}
          <div className="mb-6">
            {renderStepContent()}
          </div>

          {/* Navigation */}
          {session && session.currentStep > 1 && (
            <div className="flex items-center justify-between pt-6 border-t border-border/20">
              <Button
                variant="ghost"
                onClick={() => setCurrentStep(session.currentStep - 1)}
                disabled={isUploading}
              >
                <ChevronLeft className="w-4 h-4 mr-2" />
                Back
              </Button>

              <div className="flex items-center space-x-3">
                {session.currentStep === session.totalSteps && (
                  <>
                    <Button
                      variant="outline"
                      onClick={() => {
                        console.log('🔴 Save as Draft button clicked');
                        setPublishStatus('draft');
                        setTimeout(handleUpload, 50);
                      }}
                      disabled={isUploading}
                    >
                      {isUploading && session.status === 'draft' ? 'Saving...' : 'Save as Draft'}
                    </Button>
                    <Button
                      onClick={() => {
                        console.log('🔴 Publish button clicked');
                        setPublishStatus('published');
                        // Small delay to ensure state update
                        setTimeout(handleUpload, 50);
                      }}
                      disabled={isUploading}
                      className="bg-gradient-to-r from-purple-500 to-pink-500 text-white"
                    >
                      {isUploading && session.status === 'published' ? 'Publishing...' :
                       isUploading ? 'Uploading...' : 'Publish'}
                    </Button>
                  </>
                )}

                {session.currentStep < session.totalSteps && (
                  <Button
                    onClick={() => {
                      console.log('🔵 Next button clicked, current step:', session.currentStep);
                      console.log('🔵 Can proceed:', canProceedToNextStep());
                      if (canProceedToNextStep()) {
                        console.log('🔵 Moving to step:', session.currentStep + 1);
                        setCurrentStep(session.currentStep + 1);
                      } else {
                        console.log('🔴 Cannot proceed to next step');
                      }
                    }}
                    disabled={!canProceedToNextStep()}
                  >
                    Next
                    <ChevronRight className="w-4 h-4 ml-2" />
                  </Button>
                )}
              </div>
            </div>
          )}

          {/* Hidden File Inputs */}
          <input
            ref={fileInputRef}
            type="file"
            multiple={session?.type === 'album'}
            accept="audio/*"
            onChange={handleFileInputChange}
            className="hidden"
          />
          <input
            ref={coverInputRef}
            type="file"
            accept="image/*"
            onChange={handleCoverInputChange}
            className="hidden"
          />
        </div>
      </Card>
    </div>
  );

  function canProceedToNextStep(): boolean {
    if (!session) {
      console.log('🔴 canProceedToNextStep: No session');
      return false;
    }

    switch (session.currentStep) {
      case 1:
        return true; // Type selection
      case 2:
        if (session.type === 'single') {
          const hasTitle = !!session.single?.metadata.title;
          const hasArtist = !!session.single?.metadata.artist;
          console.log('🔍 Single track validation:', {
            hasTitle,
            hasArtist,
            title: session.single?.metadata.title,
            artist: session.single?.metadata.artist
          });
          return hasTitle && hasArtist;
        } else {
          const hasTracksWithMetadata = (session.album?.tracks.length || 0) > 0 &&
                 (session.album?.tracks.every(t => t.metadata.title && t.metadata.artist) || false);
          console.log('🔍 Album tracks validation:', {
            trackCount: session.album?.tracks.length || 0,
            hasTracksWithMetadata,
            tracks: session.album?.tracks.map(t => ({ title: t.metadata.title, artist: t.metadata.artist }))
          });
          return hasTracksWithMetadata;
        }
      case 3:
        if (session.type === 'album') {
          const hasAlbumTitle = !!session.album?.metadata.title;
          const hasAlbumArtist = !!session.album?.metadata.artist;
          console.log('🔍 Album metadata validation:', {
            hasAlbumTitle,
            hasAlbumArtist,
            title: session.album?.metadata.title,
            artist: session.album?.metadata.artist
          });
          return hasAlbumTitle && hasAlbumArtist;
        }
        return true;
      default:
        return true;
    }
  }
};