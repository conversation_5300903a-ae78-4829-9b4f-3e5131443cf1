import React from 'react';
import { Upload, Lock, Crown, Mic, Info } from 'lucide-react';
import { Card } from '../atoms/Card';
import { Button } from '../atoms/Button';
import { useAuth } from '../../hooks/useAuth';
import { RoleService } from '../../services/roleService';
import { cn } from '../../utils/cn';

interface UploadAccessInfoProps {
  isOpen: boolean;
  onClose: () => void;
}

export const UploadAccessInfo: React.FC<UploadAccessInfoProps> = ({ isOpen, onClose }) => {
  const { user } = useAuth();

  if (!isOpen || !user) return null;

  const canUpload = RoleService.canUploadMusic(user);
  const isAdmin = RoleService.isAdmin(user);
  const isArtist = RoleService.isArtist(user);
  const message = RoleService.getUploadAccessMessage(user);

  if (canUpload) {
    // This shouldn't normally show for users who can upload
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <div className="p-6">
          <div className="text-center mb-6">
            <div className={cn(
              "w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center",
              isArtist ? "bg-gradient-to-br from-purple-500 to-pink-500" : "bg-gradient-to-br from-gray-400 to-gray-600"
            )}>
              {isArtist ? (
                <Mic className="w-8 h-8 text-white" />
              ) : (
                <Lock className="w-8 h-8 text-white" />
              )}
            </div>
            <h2 className="text-xl font-semibold text-foreground mb-2">
              Upload Access
            </h2>
            <p className="text-muted-foreground">
              {message}
            </p>
          </div>

          <div className="space-y-4">
            {/* Current Role Info */}
            <div className="bg-foreground/5 rounded-xl p-4">
              <div className="flex items-center space-x-3 mb-2">
                {user.role === 'admin' && <Crown className="w-5 h-5 text-yellow-500" />}
                {user.role === 'artist' && <Mic className="w-5 h-5 text-purple-500" />}
                {user.role === 'listener' && <Upload className="w-5 h-5 text-blue-500" />}
                <span className="font-medium text-foreground">
                  {RoleService.getRoleDisplayName(user.role)}
                </span>
              </div>
              <p className="text-sm text-muted-foreground">
                {RoleService.getRoleDescription(user.role)}
              </p>
            </div>

            {/* Upload Access Levels */}
            <div className="space-y-3">
              <h3 className="font-medium text-foreground flex items-center">
                <Info className="w-4 h-4 mr-2" />
                Upload Access Levels
              </h3>
              
              <div className="space-y-2 text-sm">
                <div className="flex items-center justify-between p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <Crown className="w-4 h-4 text-yellow-600" />
                    <span className="font-medium text-yellow-800">Administrator</span>
                  </div>
                  <span className="text-green-600 font-medium">✓ Full Access</span>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-purple-50 border border-purple-200 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <Mic className="w-4 h-4 text-purple-600" />
                    <span className="font-medium text-purple-800">Artist</span>
                  </div>
                  <span className="text-orange-600 font-medium">⏳ Coming Soon</span>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <Upload className="w-4 h-4 text-gray-600" />
                    <span className="font-medium text-gray-800">Listener</span>
                  </div>
                  <span className="text-gray-600 font-medium">✗ No Access</span>
                </div>
              </div>
            </div>

            {/* Action based on role */}
            {user.role === 'artist' && (
              <div className="bg-purple-50 border border-purple-200 rounded-xl p-4">
                <div className="flex items-start space-x-3">
                  <Mic className="w-5 h-5 text-purple-600 flex-shrink-0 mt-0.5" />
                  <div className="text-sm text-purple-800">
                    <p className="font-medium mb-1">Artist Upload Coming Soon</p>
                    <p>
                      We're working on enabling upload access for artists. 
                      You'll be notified when this feature becomes available.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {user.role === 'listener' && (
              <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
                <div className="flex items-start space-x-3">
                  <Info className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
                  <div className="text-sm text-blue-800">
                    <p className="font-medium mb-1">Interested in Uploading Music?</p>
                    <p>
                      Contact an administrator if you're an artist who would like to upload music to the platform.
                    </p>
                  </div>
                </div>
              </div>
            )}

            <Button
              onClick={onClose}
              variant="outline"
              className="w-full"
            >
              Got it
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};
