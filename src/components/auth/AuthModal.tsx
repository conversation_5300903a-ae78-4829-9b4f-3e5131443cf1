import React, { useState } from 'react';
import { X, Mail, Lock, User, Eye, EyeOff } from 'lucide-react';
import { Card } from '../atoms/Card';
import { Button } from '../atoms/Button';
import { useAuth } from '../../hooks/useAuth';
import { cn } from '../../utils/cn';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  mode: 'login' | 'signup';
  onModeChange: (mode: 'login' | 'signup') => void;
}

export const AuthModal: React.FC<AuthModalProps> = ({
  isOpen,
  onClose,
  mode,
  onModeChange
}) => {
  const { signIn, signUp, signInWithGoogle, signInWithApple, signInWithTwitter, loading } = useAuth();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    displayName: '',
    confirmPassword: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  if (!isOpen) return null;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrors({});

    // Basic validation
    if (!formData.email || !formData.password) {
      setErrors({ general: 'Please fill in all required fields' });
      return;
    }

    if (mode === 'signup') {
      if (!formData.displayName) {
        setErrors({ displayName: 'Display name is required' });
        return;
      }
      if (formData.password !== formData.confirmPassword) {
        setErrors({ confirmPassword: 'Passwords do not match' });
        return;
      }
      if (formData.password.length < 6) {
        setErrors({ password: 'Password must be at least 6 characters' });
        return;
      }
    }

    try {
      if (mode === 'login') {
        await signIn(formData.email, formData.password);
      } else {
        await signUp(formData.email, formData.password, formData.displayName);
      }
      onClose();
    } catch (error: any) {
      setErrors({ general: error.message || 'Authentication failed' });
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleSocialSignIn = async (provider: 'google' | 'apple' | 'twitter') => {
    try {
      setErrors({});
      console.log(`🔄 Starting ${provider} sign-in from modal...`);

      if (provider === 'google') {
        await signInWithGoogle();
      } else if (provider === 'apple') {
        await signInWithApple();
      } else if (provider === 'twitter') {
        await signInWithTwitter();
      }

      console.log(`✅ ${provider} sign-in successful, closing modal`);
      onClose();
    } catch (error: any) {
      console.log(`❌ ${provider} sign-in error:`, error.message);

      // Handle redirect scenario
      if (error.message === 'redirect_initiated') {
        console.log('🔄 Redirect initiated, closing modal and showing redirect message');
        // Show a brief message before closing modal
        setErrors({ general: `Redirecting to ${provider} sign-in page...` });

        // Close modal after a brief delay to show the message
        setTimeout(() => {
          onClose();
        }, 1500);
        return;
      }

      // Handle other errors with improved messaging
      let errorMessage = error.message || `Failed to sign in with ${provider}`;

      // Provide user-friendly messages for common errors
      if (error.code === 'auth/popup-blocked') {
        errorMessage = 'Popup was blocked. Please allow popups for this site and try again.';
      } else if (error.message === 'Sign-in was cancelled. Please try again.') {
        errorMessage = error.message; // Use the improved message from authService
      } else if (error.message?.includes('Cross-Origin-Opener-Policy')) {
        errorMessage = 'Authentication popup blocked. Redirecting to sign-in page...';
      }

      setErrors({ general: errorMessage });
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4" style={{ zIndex: 10000 }}>
      <Card className="w-full max-w-md" variant="glass">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-foreground">
              {mode === 'login' ? 'Welcome Back' : 'Join Vibes'}
            </h2>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>

          {/* Social Sign-In Buttons */}
          <div className="space-y-3 mb-6">
            <Button
              type="button"
              variant="ghost"
              className="w-full flex items-center justify-center space-x-3 py-3 border border-border/20 hover:bg-secondary/20"
              onClick={() => handleSocialSignIn('google')}
              disabled={loading}
            >
              <svg className="w-5 h-5" viewBox="0 0 24 24">
                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              <span className="text-foreground">Continue with Google</span>
            </Button>

            <Button
              type="button"
              variant="ghost"
              className="w-full flex items-center justify-center space-x-3 py-3 border border-border/20 hover:bg-secondary/20"
              onClick={() => handleSocialSignIn('apple')}
              disabled={loading}
            >
              <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 ********** 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
              </svg>
              <span className="text-foreground">Continue with Apple</span>
            </Button>

            <Button
              type="button"
              variant="ghost"
              className="w-full flex items-center justify-center space-x-3 py-3 border border-border/20 hover:bg-secondary/20"
              onClick={() => handleSocialSignIn('twitter')}
              disabled={loading}
            >
              <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
              </svg>
              <span className="text-foreground">Continue with X</span>
            </Button>
          </div>

          {/* Divider */}
          <div className="relative mb-6">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-border/20"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 text-muted-foreground">Or continue with email</span>
            </div>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            {mode === 'signup' && (
              <div className="relative">
                <User className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                <input
                  type="text"
                  placeholder="Display Name"
                  value={formData.displayName}
                  onChange={(e) => handleInputChange('displayName', e.target.value)}
                  className={cn(
                    'w-full pl-10 pr-4 py-3 bg-secondary/20 border border-border/20 rounded-xl',
                    'focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary/40',
                    'placeholder-muted-foreground text-foreground',
                    errors.displayName && 'border-red-500 focus:ring-red-500/20'
                  )}
                  required
                />
                {errors.displayName && (
                  <p className="text-sm text-red-500 mt-1">{errors.displayName}</p>
                )}
              </div>
            )}

            <div className="relative">
              <Mail className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <input
                type="email"
                placeholder="Email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className={cn(
                  'w-full pl-10 pr-4 py-3 bg-secondary/20 border border-border/20 rounded-xl',
                  'focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary/40',
                  'placeholder-muted-foreground text-foreground',
                  errors.email && 'border-red-500 focus:ring-red-500/20'
                )}
                required
              />
              {errors.email && (
                <p className="text-sm text-red-500 mt-1">{errors.email}</p>
              )}
            </div>

            <div className="relative">
              <Lock className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <input
                type={showPassword ? 'text' : 'password'}
                placeholder="Password"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                autoComplete={mode === 'login' ? 'current-password' : 'new-password'}
                className={cn(
                  'w-full pl-10 pr-12 py-3 bg-secondary/20 border border-border/20 rounded-xl',
                  'focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary/40',
                  'placeholder-muted-foreground text-foreground',
                  errors.password && 'border-red-500 focus:ring-red-500/20'
                )}
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
              >
                {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </button>
              {errors.password && (
                <p className="text-sm text-red-500 mt-1">{errors.password}</p>
              )}
            </div>

            {mode === 'signup' && (
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                <input
                  type="password"
                  placeholder="Confirm Password"
                  value={formData.confirmPassword}
                  onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                  autoComplete="new-password"
                  className={cn(
                    'w-full pl-10 pr-4 py-3 bg-secondary/20 border border-border/20 rounded-xl',
                    'focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary/40',
                    'placeholder-muted-foreground text-foreground',
                    errors.confirmPassword && 'border-red-500 focus:ring-red-500/20'
                  )}
                  required
                />
                {errors.confirmPassword && (
                  <p className="text-sm text-red-500 mt-1">{errors.confirmPassword}</p>
                )}
              </div>
            )}

            {errors.general && (
              <p className="text-sm text-red-500 text-center">{errors.general}</p>
            )}

            <Button
              type="submit"
              className="w-full"
              disabled={loading}
            >
              {loading ? 'Please wait...' : (mode === 'login' ? 'Sign In' : 'Create Account')}
            </Button>
          </form>

          {/* Mode Switch */}
          <div className="mt-6 text-center">
            <p className="text-sm text-muted-foreground">
              {mode === 'login' ? "Don't have an account?" : 'Already have an account?'}
              <button
                type="button"
                onClick={() => onModeChange(mode === 'login' ? 'signup' : 'login')}
                className="ml-1 text-primary hover:text-primary/80 font-medium"
              >
                {mode === 'login' ? 'Sign up' : 'Sign in'}
              </button>
            </p>
          </div>
        </div>
      </Card>
    </div>
  );
};