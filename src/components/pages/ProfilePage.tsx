import React, { useState, useEffect, useRef } from 'react';
import { User, Edit, Save, X, Camera, Music, Heart, Users, Disc, Play, MoreHorizontal, Eye, EyeOff, Pause, ChevronDown, ChevronRight, Trash2, Edit3, Upload } from 'lucide-react';
import { Card } from '../atoms/Card';
import { Button } from '../atoms/Button';
import { cn } from '../../utils/cn';
import { User as UserType, Track, Album } from '../../types';
import { useAuth } from '../../hooks/useAuth';
import { useMusicStore } from '../../store/musicStore';
import { isValidUsername } from '../../utils/usernameGenerator';
import { MusicService } from '../../services/musicService';
import { AlbumService } from '../../services/albumService';
import { AuthService } from '../../services/authService';
import { EngagementService } from '../../services/engagementService';
import { EngagementAggregationService, UserEngagementStats } from '../../services/engagementAggregationService';
import { formatDuration } from '../../utils/audioUtils';
import { formatNumber } from '../../utils/formatters';
import { EditTrackModal } from '../profile/EditTrackModal';
import { EditAlbumModal } from '../profile/EditAlbumModal';
import { DeleteConfirmModal } from '../profile/DeleteConfirmModal';
import { OverflowMenu, OverflowMenuItem } from '../atoms/OverflowMenu';


interface ProfilePageProps {
  className?: string;
}

export const ProfilePage: React.FC<ProfilePageProps> = ({ className }) => {
  const { user } = useAuth();
  const {
    currentTrack,
    isPlaying,
    setCurrentTrack,
    togglePlay,
    setQueue,
    setQueueWithFirstTrack,
    appendToQueue,
    setCurrentAlbum,
    libraryRefreshTrigger
  } = useMusicStore();
  const [isEditing, setIsEditing] = useState(false);
  const [editedUser, setEditedUser] = useState<Partial<UserType>>({});
  const [usernameError, setUsernameError] = useState('');

  // Music library state
  const [singles, setSingles] = useState<Track[]>([]);
  const [albums, setAlbums] = useState<Album[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'published' | 'drafts'>('published');
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [expandedAlbums, setExpandedAlbums] = useState<Set<string>>(new Set());
  const [albumTracks, setAlbumTracks] = useState<Record<string, Track[]>>({});

  // Modal states for edit/delete functionality
  const [editTrackModal, setEditTrackModal] = useState<{ isOpen: boolean; track: Track | null }>({ isOpen: false, track: null });
  const [editAlbumModal, setEditAlbumModal] = useState<{ isOpen: boolean; album: Album | null }>({ isOpen: false, album: null });
  const [deleteModal, setDeleteModal] = useState<{ isOpen: boolean; type: 'track' | 'album' | null; item: Track | Album | null }>({ isOpen: false, type: null, item: null });

  // Engagement stats state
  const [engagementStats, setEngagementStats] = useState<UserEngagementStats | null>(null);
  const [engagementLoading, setEngagementLoading] = useState(false);

  // Ref to track current request and prevent duplicates
  const currentRequestRef = useRef<string | null>(null);

  // Load user's music library
  useEffect(() => {
    let isMounted = true;

    const loadMusicLibrary = async () => {
      if (!user) {
        console.log('No user found, skipping music library load');
        return;
      }

      // Create unique request ID to prevent duplicate requests
      const requestId = `${user.id}-${activeTab}-${Date.now()}`;

      // Prevent multiple concurrent requests
      if (currentRequestRef.current) {
        console.log('Already loading, skipping duplicate request');
        return;
      }

      currentRequestRef.current = requestId;
      console.log('Loading music library for user:', user.id, 'tab:', activeTab, 'requestId:', requestId);

      // Check if user has valid ID
      if (!user.id) {
        console.error('🔴 User ID is undefined, cannot load music library');
        setLoading(false);
        return;
      }

      setLoading(true);

      try {
        // Check if user is authenticated
        const currentUser = AuthService.getCurrentUser();
        if (!currentUser) {
          throw new Error('User not authenticated');
        }

        // Load singles and albums separately to avoid Promise.all failing if one fails
        let userSingles: Track[] = [];
        let userAlbums: Album[] = [];

        try {
          console.log('🔵 Fetching singles for tab:', activeTab);
          const statusFilter = activeTab === 'published' ? 'published' : 'draft';
          console.log('🔵 Using status filter:', statusFilter);
          userSingles = await MusicService.getSinglesByUser(user.id, statusFilter);
          console.log('🔵 Singles loaded successfully:', userSingles.length);
          console.log('🔵 Singles data:', userSingles.map(s => ({ id: s.id, title: s.title, status: s.status })));
        } catch (singlesError) {
          console.error('🔴 Failed to load singles:', singlesError);
          // Continue with empty singles array
        }

        try {
          console.log('Fetching albums...');
          userAlbums = await AlbumService.getAlbumsByUser(user.id, activeTab === 'published' ? 'published' : 'draft');
          console.log('Albums loaded successfully:', userAlbums.length);
        } catch (albumsError) {
          console.error('Failed to load albums:', albumsError);
          // Continue with empty albums array
        }

        if (isMounted && currentRequestRef.current === requestId) {
          setSingles(userSingles);
          setAlbums(userAlbums);
          console.log('Music library loaded successfully');

          // Load engagement stats after music library is loaded
          loadEngagementStats();
        }
      } catch (error) {
        if (isMounted && currentRequestRef.current === requestId) {
          console.error('Failed to load music library:', error);
          // Set empty arrays on error
          setSingles([]);
          setAlbums([]);
        }
      } finally {
        if (isMounted && currentRequestRef.current === requestId) {
          setLoading(false);
          currentRequestRef.current = null;
        }
      }
    };

    // Load music library immediately - cache invalidation handles timing
    loadMusicLibrary();

    return () => {
      isMounted = false;
      currentRequestRef.current = null;
    };
  }, [user?.id, activeTab, refreshTrigger, libraryRefreshTrigger]);

  // Load engagement statistics
  const loadEngagementStats = async () => {
    if (!user?.id) return;

    try {
      setEngagementLoading(true);
      const stats = await EngagementAggregationService.getUserEngagementStats(user.id);
      setEngagementStats(stats);
    } catch (error) {
      console.error('Failed to load engagement stats:', error);
    } finally {
      setEngagementLoading(false);
    }
  };

  if (!user) {
    return (
      <div className={cn("h-full flex items-center justify-center", className)}>
        <Card className="p-8 text-center">
          <User className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-foreground mb-2">Sign In Required</h2>
          <p className="text-muted-foreground">Please sign in to view your profile.</p>
        </Card>
      </div>
    );
  }

  const handleEdit = () => {
    setIsEditing(true);
    setEditedUser({
      displayName: user.displayName,
      username: user.username,
      bio: user.bio || ''
    });
    setUsernameError('');
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditedUser({});
    setUsernameError('');
  };

  const handleSave = () => {
    // Validate username
    if (editedUser.username && !isValidUsername(editedUser.username)) {
      setUsernameError('Username must be 3-20 characters and contain only letters and numbers');
      return;
    }

    // TODO: Implement save functionality with AuthService
    console.log('Saving user data:', editedUser);
    setIsEditing(false);
    setUsernameError('');
  };

  const handleInputChange = (field: keyof UserType, value: string) => {
    setEditedUser(prev => ({ ...prev, [field]: value }));
    if (field === 'username') {
      setUsernameError('');
    }
  };

  // Play functionality
  const handlePlayTrack = (track: Track, trackList: Track[], index: number) => {
    if (currentTrack?.id === track.id) {
      togglePlay();
    } else {
      setQueue(trackList);
      setCurrentTrack(track);
      // Play tracking is now handled in useAudioPlayer hook when playback actually starts

      // Automatically start playing the new track
      if (!isPlaying) {
        togglePlay(); // No delay needed with debouncing
      }
    }
  };

  const isCurrentTrack = (trackId: string) => currentTrack?.id === trackId;

  const handleToggleAlbum = async (albumId: string) => {
    const newExpanded = new Set(expandedAlbums);

    if (expandedAlbums.has(albumId)) {
      // Collapse album
      newExpanded.delete(albumId);
    } else {
      // Expand album - fetch tracks if not already loaded
      newExpanded.add(albumId);

      if (!albumTracks[albumId]) {
        try {
          const albumWithTracks = await AlbumService.getAlbumWithTracks(albumId);
          if (albumWithTracks) {
            setAlbumTracks(prev => ({
              ...prev,
              [albumId]: albumWithTracks.tracks
            }));
          }
        } catch (error) {
          console.error('Failed to load album tracks:', error);
        }
      }
    }

    setExpandedAlbums(newExpanded);
  };

  const handlePlayAlbum = async (album: Album) => {
    try {
      // Check if we already have all tracks cached
      let tracks = albumTracks[album.id];

      if (tracks && tracks.length > 0) {
        // Use cached tracks for instant playback
        console.log('🎵 Playing cached album:', album.title);

        // Set the current album for Play mode display
        setCurrentAlbum(album);

        setCurrentTrack(tracks[0]);
        setQueue(tracks);
        if (!isPlaying || currentTrack?.id !== tracks[0].id) {
          togglePlay();
        }

        // Play tracking is now handled in useAudioPlayer hook when playback actually starts
        return;
      }

      // Use progressive loading for uncached albums
      console.log('🎵 Loading album:', album.title);
      const albumWithFirstTrack = await AlbumService.getAlbumWithFirstTrack(album.id);

      if (!albumWithFirstTrack || !albumWithFirstTrack.firstTrack) {
        console.warn('Album has no tracks to play');
        return;
      }

      const { firstTrack, remainingTrackIds } = albumWithFirstTrack;

      // Set the current album for Play mode display
      setCurrentAlbum(album);

      // Start playing first track immediately
      setCurrentTrack(firstTrack);
      setQueueWithFirstTrack(firstTrack);
      if (!isPlaying || currentTrack?.id !== firstTrack.id) {
        setTimeout(() => togglePlay(), 100);
      }

      // Track play event for the album
      if (user) {
        await EngagementService.trackPlay(
          firstTrack.id,
          album.id,
          user.id
        );
      }

      // Load remaining tracks in background
      if (remainingTrackIds.length > 0) {
        AlbumService.getRemainingTracks(remainingTrackIds)
          .then((remainingTracks) => {
            if (remainingTracks.length > 0) {
              const allTracks = [firstTrack, ...remainingTracks];
              // Cache the complete track list
              setAlbumTracks(prev => ({
                ...prev,
                [album.id]: allTracks
              }));
              // Update queue with all tracks
              appendToQueue(remainingTracks);
            }
          })
          .catch((err) => {
            console.warn('Failed to load remaining tracks:', err);
          });
      } else {
        // Cache single track
        setAlbumTracks(prev => ({
          ...prev,
          [album.id]: [firstTrack]
        }));
      }
    } catch (error) {
      console.error('Failed to play album:', error);
    }
  };

  // Edit and Delete handlers
  const handleEditTrack = (track: Track) => {
    setEditTrackModal({ isOpen: true, track });
  };

  const handleEditAlbum = (album: Album) => {
    setEditAlbumModal({ isOpen: true, album });
  };

  const handleDeleteTrack = (track: Track) => {
    setDeleteModal({ isOpen: true, type: 'track', item: track });
  };

  const handleDeleteAlbum = (album: Album) => {
    setDeleteModal({ isOpen: true, type: 'album', item: album });
  };

  const handleTrackUpdated = () => {
    // Refresh the music library after track update
    setRefreshTrigger(prev => prev + 1);
    setEditTrackModal({ isOpen: false, track: null });
  };

  const handleAlbumUpdated = () => {
    // Refresh the music library after album update
    setRefreshTrigger(prev => prev + 1);
    setEditAlbumModal({ isOpen: false, album: null });
  };

  const handleItemDeleted = () => {
    // Refresh the music library after deletion
    setRefreshTrigger(prev => prev + 1);
    setDeleteModal({ isOpen: false, type: null, item: null });
  };

  const handlePublishTrack = async (track: Track) => {
    try {
      await MusicService.publishTrack(track.id);
      setRefreshTrigger(prev => prev + 1);
    } catch (error) {
      console.error('Failed to publish track:', error);
    }
  };

  const handlePublishAlbum = async (album: Album) => {
    try {
      await AlbumService.publishAlbum(album.id);
      setRefreshTrigger(prev => prev + 1);
    } catch (error) {
      console.error('Failed to publish album:', error);
    }
  };



  return (
    <div className={cn("h-full overflow-y-auto overflow-x-visible", className)}>
      <div className="py-6">
        {/* Single Card containing all profile content */}
        <Card className="p-6" allowOverflow data-main-card>
          {/* Profile Header */}
          <div className="flex items-start justify-between mb-6">
            <h1 className="text-2xl font-bold text-foreground">Profile</h1>
            {!isEditing ? (
              <Button
                variant="outline"
                size="sm"
                onClick={handleEdit}
                className="flex items-center space-x-2"
              >
                <Edit className="w-4 h-4" />
                <span>Edit Profile</span>
              </Button>
            ) : (
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCancel}
                  className="flex items-center space-x-2"
                >
                  <X className="w-4 h-4" />
                  <span>Cancel</span>
                </Button>
                <Button
                  variant="primary"
                  size="sm"
                  onClick={handleSave}
                  className="flex items-center space-x-2"
                >
                  <Save className="w-4 h-4" />
                  <span>Save</span>
                </Button>
              </div>
            )}
          </div>

          {/* Profile Content */}
          <div className="flex items-start space-x-6">
            {/* Avatar */}
            <div className="relative">
              {user.photoURL ? (
                <img
                  src={user.photoURL}
                  alt={user.displayName || 'User'}
                  className="w-24 h-24 rounded-full object-cover"
                />
              ) : (
                <div className="w-24 h-24 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full flex items-center justify-center">
                  <User className="w-12 h-12 text-white" />
                </div>
              )}
              {isEditing && (
                <button className="absolute bottom-0 right-0 w-8 h-8 bg-primary rounded-full flex items-center justify-center shadow-lg hover:bg-primary/90 transition-colors">
                  <Camera className="w-4 h-4 text-white" />
                </button>
              )}
            </div>

            {/* User Info */}
            <div className="flex-1 space-y-4">
              {/* Display Name */}
              <div>
                <label className="block text-sm font-medium text-foreground mb-1">
                  Display Name
                </label>
                {isEditing ? (
                  <input
                    type="text"
                    value={editedUser.displayName || ''}
                    onChange={(e) => handleInputChange('displayName', e.target.value)}
                    className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary/20"
                    placeholder="Enter your display name"
                  />
                ) : (
                  <p className="text-lg font-semibold text-foreground">
                    {user.displayName || user.email?.split('@')[0] || 'User'}
                  </p>
                )}
              </div>

              {/* Username */}
              <div>
                <label className="block text-sm font-medium text-foreground mb-1">
                  Username
                </label>
                {isEditing ? (
                  <div>
                    <div className="flex items-center">
                      <span className="text-muted-foreground mr-1">@</span>
                      <input
                        type="text"
                        value={editedUser.username || ''}
                        onChange={(e) => handleInputChange('username', e.target.value)}
                        className={cn(
                          "flex-1 px-3 py-2 border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2",
                          usernameError 
                            ? "border-red-500 focus:ring-red-500/20" 
                            : "border-border focus:ring-primary/20"
                        )}
                        placeholder="Enter your username"
                      />
                    </div>
                    {usernameError && (
                      <p className="text-sm text-red-500 mt-1">{usernameError}</p>
                    )}
                  </div>
                ) : (
                  <p className="text-foreground">@{user.username}</p>
                )}
              </div>



              {/* Bio */}
              <div>
                <label className="block text-sm font-medium text-foreground mb-1">
                  Bio
                </label>
                {isEditing ? (
                  <textarea
                    value={editedUser.bio || ''}
                    onChange={(e) => handleInputChange('bio', e.target.value)}
                    className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary/20 resize-none"
                    rows={3}
                    placeholder="Tell us about yourself..."
                  />
                ) : (
                  <p className="text-muted-foreground">
                    {user.bio || 'No bio added yet.'}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Divider */}
          <div className="my-8 border-t border-border/20"></div>

          {/* Stats Section */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-foreground mb-6">Statistics</h2>
            {engagementLoading ? (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="text-center p-4 rounded-xl bg-foreground/5 animate-pulse">
                    <div className="w-8 h-8 bg-foreground/20 rounded-full mx-auto mb-2"></div>
                    <div className="h-6 bg-foreground/20 rounded mb-2"></div>
                    <div className="h-4 bg-foreground/20 rounded"></div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
                <div className="text-center p-4 rounded-xl bg-foreground/5">
                  <Music className="w-8 h-8 text-primary mx-auto mb-2" />
                  <h3 className="text-lg font-semibold text-foreground">
                    {engagementStats?.totalTracksUploaded || 0}
                  </h3>
                  <p className="text-sm text-muted-foreground">Total Tracks</p>
                </div>

                <div className="text-center p-4 rounded-xl bg-foreground/5">
                  <Disc className="w-8 h-8 text-purple-500 mx-auto mb-2" />
                  <h3 className="text-lg font-semibold text-foreground">
                    {engagementStats?.totalAlbumsUploaded || 0}
                  </h3>
                  <p className="text-sm text-muted-foreground">Albums</p>
                </div>

                <div className="text-center p-4 rounded-xl bg-foreground/5">
                  <Play className="w-8 h-8 text-green-500 mx-auto mb-2" />
                  <h3 className="text-lg font-semibold text-foreground">
                    {formatNumber(engagementStats?.totalPlays || 0)}
                  </h3>
                  <p className="text-sm text-muted-foreground">Total Plays</p>
                </div>

                <div className="text-center p-4 rounded-xl bg-foreground/5">
                  <Heart className="w-8 h-8 text-red-500 mx-auto mb-2" />
                  <h3 className="text-lg font-semibold text-foreground">
                    {formatNumber(engagementStats?.totalLikes || 0)}
                  </h3>
                  <p className="text-sm text-muted-foreground">Total Likes</p>
                </div>

                <div className="text-center p-4 rounded-xl bg-foreground/5">
                  <Users className="w-8 h-8 text-blue-500 mx-auto mb-2" />
                  <h3 className="text-lg font-semibold text-foreground">
                    {formatNumber(engagementStats?.totalSaves || 0)}
                  </h3>
                  <p className="text-sm text-muted-foreground">Total Saves</p>
                </div>

                <div className="text-center p-4 rounded-xl bg-foreground/5">
                  <Music className="w-8 h-8 text-orange-500 mx-auto mb-2" />
                  <h3 className="text-lg font-semibold text-foreground">{singles.length}</h3>
                  <p className="text-sm text-muted-foreground">Singles</p>
                </div>
              </div>
            )}

            {/* Top Performing Tracks */}
            {engagementStats && (engagementStats.mostPlayedTrack || engagementStats.mostLikedTrack) && (
              <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
                {engagementStats.mostPlayedTrack && (
                  <div className="p-4 rounded-xl bg-green-500/10 border border-green-500/20">
                    <h4 className="text-sm font-semibold text-green-600 mb-2">Most Played Track</h4>
                    <p className="font-medium text-foreground">{engagementStats.mostPlayedTrack.track.title}</p>
                    <p className="text-sm text-muted-foreground">
                      {formatNumber(engagementStats.mostPlayedTrack.playCount)} plays
                    </p>
                  </div>
                )}

                {engagementStats.mostLikedTrack && (
                  <div className="p-4 rounded-xl bg-red-500/10 border border-red-500/20">
                    <h4 className="text-sm font-semibold text-red-600 mb-2">Most Liked Track</h4>
                    <p className="font-medium text-foreground">{engagementStats.mostLikedTrack.track.title}</p>
                    <p className="text-sm text-muted-foreground">
                      {formatNumber(engagementStats.mostLikedTrack.likeCount)} likes
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Music Library Section */}
          <div>
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-foreground">Music Library</h2>

              <div className="flex items-center space-x-3">
                {/* Tab Switcher */}
                <div className="flex items-center space-x-1 bg-secondary/20 rounded-lg p-1">
                  <button
                    onClick={() => setActiveTab('published')}
                    className={cn(
                      'px-3 py-1 rounded-md text-sm font-medium transition-colors',
                      activeTab === 'published'
                        ? 'bg-primary text-white'
                        : 'text-muted-foreground hover:text-foreground'
                    )}
                  >
                    <Eye className="w-4 h-4 inline mr-1" />
                    Published
                  </button>
                  <button
                    onClick={() => setActiveTab('drafts')}
                    className={cn(
                      'px-3 py-1 rounded-md text-sm font-medium transition-colors',
                      activeTab === 'drafts'
                        ? 'bg-primary text-white'
                        : 'text-muted-foreground hover:text-foreground'
                    )}
                  >
                    <EyeOff className="w-4 h-4 inline mr-1" />
                    Drafts
                  </button>
                </div>
              </div>
            </div>

            {loading ? (
              <div className="text-center py-8 rounded-xl bg-foreground/5">
                <div className="animate-spin w-8 h-8 border-2 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
                <p className="text-muted-foreground">Loading your music...</p>
              </div>
            ) : (
              <div className="space-y-6">
                {/* Albums Section */}
                {albums.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center">
                      <Disc className="w-5 h-5 mr-2" />
                      Albums ({albums.length})
                    </h3>
                    <div className="space-y-4">
                      {albums.map((album) => (
                        <div key={`album-${album.id}`} className="space-y-2 relative">
                          <Card className="p-4 hover:bg-secondary/10 transition-colors group" allowOverflow>
                            <div className="flex items-start space-x-3">
                              {/* Album Cover with Play Button */}
                              <div className="relative group">
                                {album.coverUrl ? (
                                  <img
                                    src={album.coverUrl}
                                    alt={album.title}
                                    className="w-16 h-16 rounded-lg object-cover"
                                  />
                                ) : (
                                  <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                                    <Disc className="w-8 h-8 text-white" />
                                  </div>
                                )}
                                {/* Play Button Overlay */}
                                <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                                  <Button
                                    variant="primary"
                                    size="sm"
                                    className="rounded-full h-8 w-8 p-0"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handlePlayAlbum(album);
                                    }}
                                  >
                                    <Play className="w-4 h-4" />
                                  </Button>
                                </div>
                              </div>

                              {/* Album Info */}
                              <div className="flex-1 min-w-0">
                                <h4 className="font-semibold text-foreground truncate">{album.title}</h4>
                                <p className="text-sm text-muted-foreground truncate">{album.artist}</p>
                                <div className="flex items-center space-x-2 mt-2 text-xs text-muted-foreground">
                                  <span>{album.trackCount || 0} tracks</span>
                                  {album.status === 'draft' && (
                                    <span className="px-2 py-1 bg-yellow-500/20 text-yellow-600 rounded-full">
                                      Draft
                                    </span>
                                  )}
                                </div>
                              </div>

                              {/* Expand/Collapse Button */}
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0"
                                onClick={() => handleToggleAlbum(album.id)}
                              >
                                {expandedAlbums.has(album.id) ? (
                                  <ChevronDown className="w-4 h-4" />
                                ) : (
                                  <ChevronRight className="w-4 h-4" />
                                )}
                              </Button>

                              {/* Overflow Menu */}
                              <OverflowMenu
                                items={[
                                  {
                                    id: 'edit',
                                    label: 'Edit album',
                                    icon: Edit3,
                                    onClick: () => handleEditAlbum(album)
                                  },
                                  ...(album.status === 'draft' ? [{
                                    id: 'publish',
                                    label: 'Publish album',
                                    icon: Upload,
                                    onClick: () => handlePublishAlbum(album)
                                  }] : []),
                                  {
                                    id: 'delete',
                                    label: 'Delete album',
                                    icon: Trash2,
                                    onClick: () => handleDeleteAlbum(album),
                                    variant: 'destructive' as const
                                  }
                                ]}
                                placement="top-left"
                              />
                            </div>
                          </Card>

                          {/* Expanded Album Tracks */}
                          {expandedAlbums.has(album.id) && albumTracks[album.id] && (
                            <Card className="ml-6 p-3 bg-secondary/5" allowOverflow>
                              <div className="space-y-1">
                                {albumTracks[album.id].map((track, index) => (
                                  <div
                                    key={`album-track-${album.id}-${track.id}`}
                                    className="flex items-center space-x-3 p-2 rounded-lg hover:bg-foreground/5 transition-colors cursor-pointer group"
                                    onClick={() => handlePlayTrack(track, albumTracks[album.id], index)}
                                  >
                                    {/* Track Number / Play Button */}
                                    <div className="w-6 flex items-center justify-center">
                                      {isCurrentTrack(track.id) && isPlaying ? (
                                        <Pause className="w-3 h-3 text-primary" />
                                      ) : (
                                        <>
                                          <span className="text-xs text-muted-foreground group-hover:hidden">
                                            {index + 1}
                                          </span>
                                          <Play className="w-3 h-3 text-primary hidden group-hover:block" />
                                        </>
                                      )}
                                    </div>

                                    {/* Track Info */}
                                    <div className="flex-1 min-w-0">
                                      <h5 className={cn(
                                        "text-sm font-medium truncate",
                                        isCurrentTrack(track.id) ? "text-primary" : "text-foreground"
                                      )}>
                                        {track.title}
                                      </h5>
                                      <p className="text-xs text-muted-foreground truncate">{track.artist}</p>
                                      {(track.playCount || track.likeCount) && (
                                        <div className="flex items-center space-x-2 mt-1">
                                          {track.playCount && track.playCount > 0 && (
                                            <span className="text-xs text-green-600">
                                              {formatNumber(track.playCount)} plays
                                            </span>
                                          )}
                                          {track.likeCount && track.likeCount > 0 && (
                                            <span className="text-xs text-red-600">
                                              {formatNumber(track.likeCount)} likes
                                            </span>
                                          )}
                                        </div>
                                      )}
                                    </div>

                                    {/* Duration */}
                                    <span className="text-xs text-muted-foreground">
                                      {formatDuration(track.duration)}
                                    </span>

                                    {/* Overflow Menu for Album Tracks */}
                                    <OverflowMenu
                                      items={[
                                        {
                                          id: 'edit',
                                          label: 'Edit track',
                                          icon: Edit3,
                                          onClick: () => handleEditTrack(track)
                                        },
                                        ...(track.status === 'draft' ? [{
                                          id: 'publish',
                                          label: 'Publish track',
                                          icon: Upload,
                                          onClick: () => handlePublishTrack(track)
                                        }] : []),
                                        {
                                          id: 'delete',
                                          label: 'Delete track',
                                          icon: Trash2,
                                          onClick: () => handleDeleteTrack(track),
                                          variant: 'destructive' as const
                                        }
                                      ]}
                                      placement="top-left"
                                      buttonClassName="h-6 w-6 p-0"
                                    />
                                  </div>
                                ))}
                              </div>
                            </Card>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Singles Section */}
                {singles.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center">
                      <Music className="w-5 h-5 mr-2" />
                      Singles ({singles.length})
                    </h3>

                    <div className="space-y-1">
                      {singles.map((track, index) => (
                        <Card
                          key={`single-${track.id}`}
                          className="p-3 hover:bg-secondary/10 transition-colors cursor-pointer group"
                          onClick={() => handlePlayTrack(track, singles, index)}
                          allowOverflow
                        >
                          <div className="flex items-center space-x-3">
                            {/* Track Number / Play Button */}
                            <div className="w-8 flex items-center justify-center">
                              {isCurrentTrack(track.id) && isPlaying ? (
                                <Pause className="w-4 h-4 text-primary" />
                              ) : (
                                <>
                                  <span className="text-sm text-muted-foreground group-hover:hidden">
                                    {index + 1}
                                  </span>
                                  <Play className="w-4 h-4 text-primary hidden group-hover:block" />
                                </>
                              )}
                            </div>

                            {/* Track Cover */}
                            {track.coverUrl ? (
                              <img
                                src={track.coverUrl}
                                alt={track.title}
                                className="w-12 h-12 rounded-lg object-cover"
                              />
                            ) : (
                              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                                <Music className="w-6 h-6 text-white" />
                              </div>
                            )}

                            {/* Track Info */}
                            <div className="flex-1 min-w-0">
                              <h4 className={cn(
                                "font-semibold truncate",
                                isCurrentTrack(track.id) ? "text-primary" : "text-foreground"
                              )}>
                                {track.title}
                              </h4>
                              <p className="text-sm text-muted-foreground truncate">{track.artist}</p>
                              <div className="flex items-center space-x-3 mt-1">
                                {track.genre && (
                                  <span className="text-xs text-muted-foreground">{track.genre}</span>
                                )}
                                {(track.playCount || track.likeCount) && (
                                  <div className="flex items-center space-x-2">
                                    {track.playCount && track.playCount > 0 && (
                                      <span className="text-xs text-green-600">
                                        {formatNumber(track.playCount)} plays
                                      </span>
                                    )}
                                    {track.likeCount && track.likeCount > 0 && (
                                      <span className="text-xs text-red-600">
                                        {formatNumber(track.likeCount)} likes
                                      </span>
                                    )}
                                  </div>
                                )}
                              </div>
                            </div>

                            {/* Duration */}
                            <div className="flex items-center space-x-2">
                              <span className="text-sm text-muted-foreground">
                                {formatDuration(track.duration)}
                              </span>
                            </div>

                            {/* Status and Actions */}
                            <div className="flex items-center space-x-2">
                              {track.status === 'draft' && (
                                <span className="px-2 py-1 bg-yellow-500/20 text-yellow-600 rounded-full text-xs">
                                  Draft
                                </span>
                              )}
                              <OverflowMenu
                                items={[
                                  {
                                    id: 'edit',
                                    label: 'Edit track',
                                    icon: Edit3,
                                    onClick: () => handleEditTrack(track)
                                  },
                                  ...(track.status === 'draft' ? [{
                                    id: 'publish',
                                    label: 'Publish track',
                                    icon: Upload,
                                    onClick: () => handlePublishTrack(track)
                                  }] : []),
                                  {
                                    id: 'delete',
                                    label: 'Delete track',
                                    icon: Trash2,
                                    onClick: () => handleDeleteTrack(track),
                                    variant: 'destructive' as const
                                  }
                                ]}
                                placement="top-left"
                              />
                            </div>
                          </div>
                        </Card>
                      ))}
                    </div>
                  </div>
                )}

                {/* Empty State */}
                {albums.length === 0 && singles.length === 0 && (
                  <div className="text-center py-8 rounded-xl bg-foreground/5">
                    <Music className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">
                      {activeTab === 'published' ? 'No published music yet.' : 'No drafts yet.'}
                    </p>
                    <p className="text-sm text-muted-foreground mt-2">
                      Start uploading tracks to build your music library!
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </Card>
      </div>

      {/* Modals */}
      <EditTrackModal
        isOpen={editTrackModal.isOpen}
        track={editTrackModal.track}
        onClose={() => setEditTrackModal({ isOpen: false, track: null })}
        onTrackUpdated={handleTrackUpdated}
      />

      <EditAlbumModal
        isOpen={editAlbumModal.isOpen}
        album={editAlbumModal.album}
        onClose={() => setEditAlbumModal({ isOpen: false, album: null })}
        onAlbumUpdated={handleAlbumUpdated}
      />

      <DeleteConfirmModal
        isOpen={deleteModal.isOpen}
        type={deleteModal.type}
        item={deleteModal.item}
        onClose={() => setDeleteModal({ isOpen: false, type: null, item: null })}
        onDeleted={handleItemDeleted}
      />
    </div>
  );
};
