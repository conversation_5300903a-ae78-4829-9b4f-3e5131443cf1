import React from 'react';
import { Card } from '../atoms/Card';
import { useAuth } from '../../hooks/useAuth';
import { MessageCircle } from 'lucide-react';
import { TrackChat } from '../organisms/TrackChat';
import { useMusicStore } from '../../store/musicStore';

interface ChatPageProps {
  className?: string;
}

export const ChatPage: React.FC<ChatPageProps> = ({ className }) => {
  const { user } = useAuth();
  const { currentTrack } = useMusicStore();

  return (
    <div className={`h-full flex flex-col ${className || ''}`}>
      {/* Header Section */}
      <div className="flex-shrink-0 p-4">
        <Card className="p-4" variant="glass">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-violet-500 to-purple-500 rounded-xl flex items-center justify-center">
              <MessageCircle className="w-5 h-5 text-white" />
            </div>
            <div>
              <h1 className="text-lg font-bold text-foreground">Track Chat</h1>
              <p className="text-xs text-muted-foreground">
                {currentTrack ? `Chat about "${currentTrack.title}"` : 'Select a track to start chatting'}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Chat Content */}
      <div className="flex-1 overflow-hidden">
        {currentTrack ? (
          <TrackChat className="h-full" />
        ) : (
          <div className="h-full flex items-center justify-center p-4">
            <Card className="p-8 text-center" variant="glass">
              <MessageCircle className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-foreground mb-2">No Track Selected</h3>
              <p className="text-sm text-muted-foreground max-w-sm mx-auto">
                Start playing a track to join the conversation and chat with other listeners in real-time.
              </p>
            </Card>
          </div>
        )}
      </div>

    </div>
  );
};
