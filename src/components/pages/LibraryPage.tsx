import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card } from '../atoms/Card';
import { Button } from '../atoms/Button';
import { useAuth } from '../../hooks/useAuth';
import { Heart, Clock, List, Play, Loader2 } from 'lucide-react';
import { EngagementService } from '../../services/engagementService';
import { MusicService } from '../../services/musicService';
import { useMusicStore } from '../../store/musicStore';
import { Track } from '../../types';
import { formatDuration } from '../../utils/formatters';

interface LibraryPageProps {
  className?: string;
}

export const LibraryPage: React.FC<LibraryPageProps> = ({ className }) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const {
    currentTrack,
    isPlaying,
    setCurrentTrack,
    togglePlay,
    setQueue,
    setCurrentAlbum,
    updateTrackLikeCount
  } = useMusicStore();

  // State for liked songs
  const [likedTracks, setLikedTracks] = useState<Track[]>([]);
  const [likedTracksLoading, setLikedTracksLoading] = useState(false);
  const [playingLikedSongs, setPlayingLikedSongs] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [lastLikedCount, setLastLikedCount] = useState(0);

  // Function to load liked songs
  const loadLikedSongs = async () => {
    if (!user) {
      setLikedTracks([]);
      setLastLikedCount(0);
      return;
    }

    // Don't show loading for background refreshes
    const isInitialLoad = likedTracks.length === 0;
    if (isInitialLoad) {
      setLikedTracksLoading(true);
    }

    try {
      // Get user's liked track IDs
      const likedTrackIds = await EngagementService.getUserLikedTracks(user.id);

      // Only update if count has changed (for efficiency)
      if (likedTrackIds.length === lastLikedCount && !isInitialLoad) {
        return;
      }

      console.log(`📚 Liked songs count changed: ${lastLikedCount} → ${likedTrackIds.length}`);
      setLastLikedCount(likedTrackIds.length);

      if (likedTrackIds.length === 0) {
        setLikedTracks([]);
        return;
      }

      // Load track details for first 10 tracks (for display)
      const trackPromises = likedTrackIds.slice(0, 10).map(async (trackId) => {
        try {
          return await MusicService.getTrackById(trackId);
        } catch (error) {
          console.warn(`Failed to load track ${trackId}:`, error);
          return null;
        }
      });

      const tracks = await Promise.all(trackPromises);
      const validTracks = tracks.filter((track): track is Track => track !== null);

      setLikedTracks(validTracks);
      console.log(`📚 Updated liked songs display: ${validTracks.length} tracks`);
    } catch (error) {
      console.error('Failed to load liked songs:', error);
      setLikedTracks([]);
    } finally {
      if (isInitialLoad) {
        setLikedTracksLoading(false);
      }
    }
  };

  // Load liked songs when user changes or refresh is triggered
  useEffect(() => {
    loadLikedSongs();
  }, [user, refreshTrigger]);

  // Refresh liked songs when window gains focus and periodically
  useEffect(() => {
    if (!user) return;

    // Refresh when window gains focus (user comes back to tab)
    const handleFocus = () => {
      console.log('🔄 Window focused - refreshing liked songs...');
      setRefreshTrigger(prev => prev + 1);
    };

    // Refresh periodically (every 3 seconds for better responsiveness)
    const interval = setInterval(() => {
      setRefreshTrigger(prev => prev + 1);
    }, 3000);

    window.addEventListener('focus', handleFocus);

    return () => {
      clearInterval(interval);
      window.removeEventListener('focus', handleFocus);
    };
  }, [user]);

  // Handle playing all liked songs
  const handlePlayLikedSongs = async () => {
    if (!user || playingLikedSongs) return;

    setPlayingLikedSongs(true);
    try {
      console.log('💚 Playing liked songs from library');

      // Get all user's liked track IDs
      const likedTrackIds = await EngagementService.getUserLikedTracks(user.id);
      if (likedTrackIds.length === 0) {
        console.log('No liked songs found');
        return;
      }

      // Load first track immediately
      const firstTrackId = likedTrackIds[0];
      const firstTrack = await MusicService.getTrackById(firstTrackId);
      if (!firstTrack) {
        throw new Error('First liked track not found');
      }

      // Set current track (no album for liked songs playlist)
      setCurrentAlbum(null);
      setCurrentTrack(firstTrack);

      // Load remaining tracks in background
      const remainingTrackIds = likedTrackIds.slice(1);
      const trackPromises = remainingTrackIds.map(async (trackId) => {
        try {
          return await MusicService.getTrackById(trackId);
        } catch (error) {
          console.warn(`Failed to load track ${trackId}:`, error);
          return null;
        }
      });

      const remainingTracks = await Promise.all(trackPromises);
      const validTracks = remainingTracks.filter((track): track is Track => track !== null);

      // Set queue with all liked songs
      setQueue([firstTrack, ...validTracks]);

      // Start playing if not already playing
      if (!isPlaying || currentTrack?.id !== firstTrack.id) {
        togglePlay();
      }

      console.log(`✅ Liked songs queue loaded: ${validTracks.length + 1} tracks`);
    } catch (error) {
      console.error('Failed to play liked songs:', error);
    } finally {
      setPlayingLikedSongs(false);
    }
  };

  // Handle playing individual liked track
  const handlePlayLikedTrack = async (track: Track, index: number) => {
    if (currentTrack?.id === track.id) {
      togglePlay();
    } else {
      // Set the clicked track as current and load full liked songs queue
      setCurrentAlbum(null);
      setCurrentTrack(track);

      // Load all liked songs as queue starting from clicked track
      try {
        const likedTrackIds = await EngagementService.getUserLikedTracks(user!.id);
        const trackPromises = likedTrackIds.map(async (trackId) => {
          try {
            return await MusicService.getTrackById(trackId);
          } catch (error) {
            return null;
          }
        });

        const allLikedTracks = await Promise.all(trackPromises);
        const validTracks = allLikedTracks.filter((t): t is Track => t !== null);

        // Reorder queue to start from clicked track
        const clickedTrackIndex = validTracks.findIndex(t => t.id === track.id);
        if (clickedTrackIndex !== -1) {
          const reorderedQueue = [
            ...validTracks.slice(clickedTrackIndex),
            ...validTracks.slice(0, clickedTrackIndex)
          ];
          setQueue(reorderedQueue);
        } else {
          setQueue(validTracks);
        }
      } catch (error) {
        console.error('Failed to load liked songs queue:', error);
        setQueue([track]);
      }

      // Start playing if not already playing
      if (!isPlaying) {
        togglePlay();
      }
    }
  };

  // Handle unliking a track
  const handleUnlikeTrack = async (track: Track) => {
    if (!user) return;

    try {
      await EngagementService.unlikeItem(track.id, 'track', user.id);

      // Remove from local state immediately
      setLikedTracks(prev => prev.filter(t => t.id !== track.id));

      // Update local like count immediately
      updateTrackLikeCount(track.id, false);

      // Trigger a refresh to get accurate count
      setRefreshTrigger(prev => prev + 1);

      console.log(`💔 Unliked track: ${track.title}`);
    } catch (error) {
      console.error('Failed to unlike track:', error);
    }
  };

  return (
    <div className={`h-full overflow-y-auto ${className || ''}`}>
      <div className="space-y-6 py-4">
        {/* Header Section */}
        <Card className="p-6" variant="glass">
          <div className="text-center space-y-3">
            <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl mx-auto flex items-center justify-center">
              <span className="text-lg">📚</span>
            </div>
            <h1 className="text-xl font-bold text-foreground">Your Library</h1>
            <p className="text-sm text-muted-foreground max-w-md mx-auto">
              Access your saved music, playlists, and recently played tracks all in one place.
            </p>
            {!user && (
              <p className="text-xs text-orange-500 bg-orange-500/10 px-3 py-2 rounded-lg">
                Sign in to access your personal music library and saved content!
              </p>
            )}
          </div>
        </Card>

        {user ? (
          <>
            {/* Liked Songs Section */}
            <section id="liked-songs">
              <Card className="p-4">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-10 h-10 bg-gradient-to-br from-pink-500 to-red-500 rounded-lg flex items-center justify-center">
                    <Heart className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h2 className="text-lg font-semibold text-foreground">Liked Songs</h2>
                    <p className="text-xs text-muted-foreground">
                      {likedTracksLoading ? 'Loading...' : `${likedTracks.length} songs`}
                    </p>
                  </div>
                  <div className="flex-1"></div>
                  {likedTracks.length > 0 && (
                    <Button
                      variant="primary"
                      size="sm"
                      onClick={handlePlayLikedSongs}
                      disabled={playingLikedSongs}
                      title="Play all liked songs"
                    >
                      {playingLikedSongs ? (
                        <Loader2 className="w-4 h-4 animate-spin" />
                      ) : (
                        <Play className="w-4 h-4" />
                      )}
                    </Button>
                  )}
                </div>

                <div className="space-y-2">
                  {likedTracksLoading ? (
                    // Loading state
                    <div className="flex items-center justify-center py-8">
                      <Loader2 className="w-6 h-6 animate-spin text-muted-foreground" />
                      <span className="ml-2 text-sm text-muted-foreground">Loading your liked songs...</span>
                    </div>
                  ) : likedTracks.length === 0 ? (
                    // Empty state
                    <div className="text-center py-8">
                      <Heart className="w-12 h-12 text-muted-foreground mx-auto mb-3 opacity-50" />
                      <p className="text-sm text-muted-foreground mb-2">No liked songs yet</p>
                      <p className="text-xs text-muted-foreground">
                        Start liking songs to build your personal collection!
                      </p>
                    </div>
                  ) : (
                    // Liked tracks list
                    likedTracks.map((track, index) => (
                      <div
                        key={track.id}
                        className="flex items-center space-x-3 p-2 rounded-xl hover:bg-foreground/5 transition-colors cursor-pointer group"
                        onClick={() => handlePlayLikedTrack(track, index)}
                      >
                        {/* Track Cover */}
                        <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-lg overflow-hidden flex-shrink-0">
                          {track.coverUrl ? (
                            <img
                              src={track.coverUrl}
                              alt={track.title}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center">
                              <span className="text-white text-xs">♪</span>
                            </div>
                          )}
                        </div>

                        {/* Track Info */}
                        <div className="flex-1 min-w-0">
                          <h4 className="text-sm font-medium text-foreground truncate">{track.title}</h4>
                          <p className="text-xs text-muted-foreground truncate">{track.artist}</p>
                        </div>

                        {/* Actions */}
                        <div className="flex items-center space-x-2">
                          <span className="text-xs text-muted-foreground">
                            {formatDuration(track.duration)}
                          </span>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-7 w-7 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleUnlikeTrack(track);
                            }}
                            title="Unlike this song"
                          >
                            <Heart className="w-3 h-3 text-red-500 fill-current" />
                          </Button>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </Card>
            </section>

            {/* Recent Albums Section - Coming Soon */}
            <section id="recent-albums">
              <Card className="p-4">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center">
                    <Clock className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h2 className="text-lg font-semibold text-foreground">Recent Albums</h2>
                    <p className="text-xs text-muted-foreground">Coming soon</p>
                  </div>
                </div>

                <div className="text-center py-8">
                  <Clock className="w-12 h-12 text-muted-foreground mx-auto mb-3 opacity-50" />
                  <p className="text-sm text-muted-foreground mb-2">Recent albums coming soon!</p>
                  <p className="text-xs text-muted-foreground">
                    We're working on tracking your recently played albums.
                  </p>
                </div>
              </Card>
            </section>

            {/* Your Playlists Section */}
            <section id="your-playlists">
              <Card className="p-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-violet-500 rounded-lg flex items-center justify-center">
                      <List className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h2 className="text-lg font-semibold text-foreground">Your Playlists</h2>
                      <p className="text-xs text-muted-foreground">Create and manage your playlists</p>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => navigate('/play')}
                  >
                    Create New
                  </Button>
                </div>

                <div className="text-center py-8">
                  <List className="w-12 h-12 text-muted-foreground mx-auto mb-3 opacity-50" />
                  <p className="text-sm text-muted-foreground mb-2">Playlists are available in Play mode!</p>
                  <p className="text-xs text-muted-foreground">
                    Switch to Play mode to create and manage your playlists.
                  </p>
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={() => navigate('/play')}
                    className="mt-3"
                  >
                    Go to Play Mode
                  </Button>
                </div>
              </Card>
            </section>
          </>
        ) : (
          <Card className="p-8 text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl mx-auto mb-4 flex items-center justify-center">
              <span className="text-2xl">📚</span>
            </div>
            <h2 className="text-xl font-semibold text-foreground mb-2">Build Your Library</h2>
            <p className="text-muted-foreground mb-4">
              Sign in to save your favorite songs, create playlists, and access your music anywhere.
            </p>
            <Button variant="primary">
              Sign In to Get Started
            </Button>
          </Card>
        )}
      </div>
    </div>
  );
};
