import React from 'react';
import { Card } from '../atoms/Card';
import { useAuth } from '../../hooks/useAuth';
import { DiscoverSections } from '../discover/DiscoverSections';

interface ExplorePageProps {
  className?: string;
}

export const ExplorePage: React.FC<ExplorePageProps> = ({ className }) => {
  const { user } = useAuth();



  return (
    <div className={`h-full overflow-y-auto ${className || ''}`}>
      <div className="space-y-6 py-4">
        {/* Header Section */}
        <Card className="p-6" variant="glass">
          <div className="text-center space-y-3">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl mx-auto flex items-center justify-center">
              <span className="text-lg">🧭</span>
            </div>
            <h1 className="text-xl font-bold text-foreground">Discover</h1>
            <p className="text-sm text-muted-foreground max-w-md mx-auto">
              Discover new music, trending tracks, and featured albums from talented artists.
            </p>
            {!user && (
              <p className="text-xs text-orange-500 bg-orange-500/10 px-3 py-2 rounded-lg">
                Sign in to like, save, and get personalized recommendations!
              </p>
            )}
          </div>
        </Card>

        {/* All Discover Content Sections */}
        <DiscoverSections showAllSections={true} />

        {/* Genres & Moods Section */}
        <section id="genres-moods">
          <Card className="p-4">
            <h2 className="text-lg font-semibold text-foreground mb-3">Genres & Moods</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {['Electronic', 'Hip Hop', 'Rock', 'Jazz', 'Classical', 'Pop', 'Indie', 'R&B'].map((genre) => (
                <button
                  key={genre}
                  className="p-3 bg-gradient-to-br from-foreground/5 to-foreground/10 rounded-xl text-left hover:from-foreground/10 hover:to-foreground/15 transition-all duration-200 hover:scale-105"
                  onClick={() => {
                    // TODO: Implement genre filtering
                    console.log('Filter by genre:', genre);
                  }}
                >
                  <h4 className="font-medium text-foreground text-xs">{genre}</h4>
                </button>
              ))}
            </div>
          </Card>
        </section>
      </div>
    </div>
  );
};


