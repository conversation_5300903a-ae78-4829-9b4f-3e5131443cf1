import React, { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { ChevronDown } from 'lucide-react';
import { PlayModeCard } from '../organisms/PlayModeCard';
import { Button } from '../atoms/Button';
import { useAuth } from '../../hooks/useAuth';
import { useMusicStore } from '../../store/musicStore';
import { usePlayModeContext } from '../../hooks/usePlayModeContext';
import { useResponsive } from '../../hooks/useResponsive';
import { extractSharedContentParams, hasSharedContent, cleanSharedContentParams } from '../../utils/urlUtils';
import { MusicService } from '../../services/musicService';
import { AlbumService } from '../../services/albumService';
import { EngagementService } from '../../services/engagementService';

interface PlayPageProps {
  className?: string;
}

export const PlayPage: React.FC<PlayPageProps> = ({ className }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { isMobile } = useResponsive();
  const { setCurrentTrack, setQueue, setCurrentAlbum, togglePlay, isPlaying } = useMusicStore();
  const { activateContextualTab } = usePlayModeContext();

  // Handle back navigation for mobile
  const handleBackNavigation = () => {
    navigate(-1); // Go back to previous page
  };

  // Handle shared content on mount and when URL changes
  useEffect(() => {
    if (hasSharedContent(location.search)) {
      handleSharedContent();
    }
  }, [location.search, user]);

  const handleSharedContent = async () => {
    if (!user) {
      // If user is not logged in, we'll handle this after they log in
      return;
    }

    const params = extractSharedContentParams(location.search);

    try {
      if (params.track) {
        await loadSharedTrack(params.track);
      } else if (params.album) {
        await loadSharedAlbum(params.album);
      } else if (params.playlist) {
        await loadSharedPlaylist(params.playlist);
      } else if (params.liked) {
        await loadSharedLikedSongs();
      }

      // Clean URL parameters after loading content
      const cleanSearch = cleanSharedContentParams(location.search);
      const newUrl = cleanSearch ? `/play?${cleanSearch}` : '/play';
      navigate(newUrl, { replace: true });
    } catch (error) {
      console.error('Failed to load shared content:', error);
      // Navigate to library if shared content fails to load
      navigate('/library', { replace: true });
    }
  };

  const loadSharedTrack = async (trackId: string) => {
    console.log('🔗 Loading shared track:', trackId);

    const track = await MusicService.getTrackById(trackId);
    if (!track) {
      throw new Error('Track not found');
    }

    // Set track and start playing
    setCurrentTrack(track);
    setQueue([track]);
    setCurrentAlbum(null);

    // Start playing for instant gratification
    if (!isPlaying) {
      setTimeout(() => togglePlay(), 100);
    }

    // Track play event
    if (user) {
      EngagementService.trackPlay(track.id, undefined, user.id);
    }

    console.log('✅ Shared track loaded and playing');
  };

  const loadSharedAlbum = async (albumId: string) => {
    console.log('🔗 Loading shared album:', albumId);

    const album = await AlbumService.getAlbumById(albumId);
    if (!album) {
      throw new Error('Album not found');
    }

    const albumWithTracks = await AlbumService.getAlbumWithTracks(albumId);
    if (!albumWithTracks || !albumWithTracks.tracks.length) {
      throw new Error('Album has no tracks');
    }

    // Set album and tracks
    setCurrentAlbum(album);
    setCurrentTrack(albumWithTracks.tracks[0]);
    setQueue(albumWithTracks.tracks);

    // Activate album contextual tab
    activateContextualTab('album', album.title, album);

    // Start playing for instant gratification
    if (!isPlaying) {
      setTimeout(() => togglePlay(), 100);
    }

    // Track play event
    if (user) {
      EngagementService.trackPlay(albumWithTracks.tracks[0].id, album.id, user.id);
    }

    console.log('✅ Shared album loaded and playing');
  };

  const loadSharedPlaylist = async (playlistId: string) => {
    console.log('🔗 Loading shared playlist:', playlistId);

    const playlist = await MusicService.getPlaylistById(playlistId);
    if (!playlist) {
      throw new Error('Playlist not found');
    }

    const tracks = await MusicService.getPlaylistTracksWithData(playlistId);
    if (!tracks.length) {
      throw new Error('Playlist has no tracks');
    }

    // Set playlist and tracks
    setCurrentAlbum(null);
    setCurrentTrack(tracks[0]);
    setQueue(tracks);

    // Activate playlist contextual tab
    activateContextualTab('playlist', playlist.name, playlist);

    // Start playing for instant gratification
    if (!isPlaying) {
      setTimeout(() => togglePlay(), 100);
    }

    // Track play event
    if (user) {
      EngagementService.trackPlay(tracks[0].id, undefined, user.id);
    }

    console.log('✅ Shared playlist loaded and playing');
  };

  const loadSharedLikedSongs = async () => {
    console.log('🔗 Loading shared liked songs');

    if (!user) {
      throw new Error('User must be logged in to view liked songs');
    }

    const likedTrackIds = await EngagementService.getUserLikedTracks(user.id);
    if (!likedTrackIds.length) {
      throw new Error('No liked songs found');
    }

    // Load all liked tracks
    const trackPromises = likedTrackIds.map(async (trackId) => {
      try {
        return await MusicService.getTrackById(trackId);
      } catch (error) {
        console.error(`Failed to load liked track ${trackId}:`, error);
        return null;
      }
    });

    const trackResults = await Promise.all(trackPromises);
    const tracks = trackResults.filter(track => track !== null);

    if (!tracks.length) {
      throw new Error('No valid liked songs found');
    }

    // Set liked songs
    setCurrentAlbum(null);
    setCurrentTrack(tracks[0]);
    setQueue(tracks);

    // Activate liked songs contextual tab
    activateContextualTab('liked', 'Liked', {});

    // Start playing for instant gratification
    if (!isPlaying) {
      setTimeout(() => togglePlay(), 100);
    }

    // Track play event
    if (user) {
      EngagementService.trackPlay(tracks[0].id, undefined, user.id);
    }

    console.log('✅ Shared liked songs loaded and playing');
  };

  // Mobile full-screen experience
  if (isMobile) {
    return (
      <div className="fixed inset-0 z-modal bg-background">
        {/* Mobile full-screen content with padding */}
        <div className="h-full overflow-hidden p-4">
          <PlayModeCard onCollapse={handleBackNavigation} isMobile={true} />
        </div>
      </div>
    );
  }

  // Desktop experience
  return (
    <div className={`h-full ${className || ''}`}>
      <PlayModeCard />
    </div>
  );
};
