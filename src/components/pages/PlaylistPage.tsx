import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '../atoms/Button';
import { Card } from '../atoms/Card';
import { useAuth } from '../../hooks/useAuth';
import { useMusicStore } from '../../store/musicStore';
import { MusicService } from '../../services/musicService';
import { EngagementService } from '../../services/engagementService';
import { ShareService } from '../../services/shareService';
import { TrackActions } from '../molecules/TrackActions';
import { OverflowMenu } from '../atoms/OverflowMenu';
import { Playlist, Track } from '../../types';
import { cn } from '../../utils/cn';
import {
  Play,
  Pause,
  Heart,
  MoreHorizontal,
  Clock,
  Music,
  Shuffle,
  Share,
  Link,
  Edit3,
  Trash2,
  Loader2,
  <PERSON>Lef<PERSON>,
  Plus
} from 'lucide-react';

interface PlaylistPageProps {
  className?: string;
}

export const PlaylistPage: React.FC<PlaylistPageProps> = ({ className }) => {
  const { playlistId } = useParams<{ playlistId: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const {
    currentTrack,
    isPlaying,
    setCurrentTrack,
    togglePlay,
    setQueue,
    setCurrentAlbum
  } = useMusicStore();

  // State
  const [playlist, setPlaylist] = useState<Playlist | null>(null);
  const [tracks, setTracks] = useState<Track[]>([]);
  const [loading, setLoading] = useState(true);
  const [playingPlaylist, setPlayingPlaylist] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load playlist data
  useEffect(() => {
    if (playlistId) {
      loadPlaylistData();
    }
  }, [playlistId]);

  const loadPlaylistData = async () => {
    if (!playlistId) return;

    setLoading(true);
    setError(null);
    try {
      // Load playlist info
      const playlistData = await MusicService.getPlaylistById(playlistId);
      if (!playlistData) {
        setError('Playlist not found');
        return;
      }

      setPlaylist(playlistData);

      // Load tracks
      const playlistTracks = await MusicService.getPlaylistTracksWithData(playlistId);
      setTracks(playlistTracks);

      console.log(`📋 Loaded playlist: ${playlistData.name} with ${playlistTracks.length} tracks`);
    } catch (error: any) {
      console.error('Failed to load playlist:', error);
      setError(error.message || 'Failed to load playlist');
    } finally {
      setLoading(false);
    }
  };

  const handlePlayPlaylist = async () => {
    if (!playlist || !user || tracks.length === 0 || playingPlaylist) return;

    setPlayingPlaylist(true);
    try {
      const firstTrack = tracks[0];

      // Set current track (no album for playlists)
      setCurrentAlbum(null);
      setCurrentTrack(firstTrack);

      // Set queue with all playlist tracks
      setQueue(tracks);

      // Start playing if not already playing
      if (!isPlaying || currentTrack?.id !== firstTrack.id) {
        togglePlay();
      }

      // Track play event
      EngagementService.trackPlay(firstTrack.id, undefined, user.id);

      console.log('✅ Playlist playback started');
    } catch (error) {
      console.error('Failed to play playlist:', error);
    } finally {
      setPlayingPlaylist(false);
    }
  };

  const handleTrackClick = async (track: Track, index: number) => {
    if (!user) return;

    try {
      // Set current track
      setCurrentAlbum(null);
      setCurrentTrack(track);

      // Reorder queue to start from clicked track
      const reorderedQueue = [
        ...tracks.slice(index),
        ...tracks.slice(0, index)
      ];
      setQueue(reorderedQueue);

      // Start playing if not already playing this track
      if (!isPlaying || currentTrack?.id !== track.id) {
        togglePlay();
      }

      // Play tracking is now handled in useAudioPlayer hook when playback actually starts
    } catch (error) {
      console.error('Failed to play track:', error);
    }
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    }).format(date);
  };

  const totalDuration = tracks.reduce((total, track) => total + (track.duration || 0), 0);
  const isOwner = user && playlist && playlist.createdBy === user.id;

  const handleSharePlaylist = async () => {
    if (!playlist) return;

    try {
      const success = await ShareService.sharePlaylist(playlist);
      if (success) {
        console.log(`✅ Playlist "${playlist.name}" shared successfully`);
      } else {
        console.error(`❌ Failed to share playlist "${playlist.name}"`);
      }
    } catch (error) {
      console.error('Error sharing playlist:', error);
    }
  };

  const handleCopyPlaylistLink = async () => {
    if (!playlist) return;

    try {
      const success = await ShareService.copyPlaylistLink(playlist);
      if (success) {
        console.log(`✅ Playlist link "${playlist.name}" copied successfully`);
      } else {
        console.error(`❌ Failed to copy playlist link "${playlist.name}"`);
      }
    } catch (error) {
      console.error('Error copying playlist link:', error);
    }
  };

  if (loading) {
    return (
      <div className={cn("flex-1 flex items-center justify-center", className)}>
        <div className="text-center space-y-4">
          <Loader2 className="w-8 h-8 animate-spin text-primary mx-auto" />
          <p className="text-muted-foreground">Loading playlist...</p>
        </div>
      </div>
    );
  }

  if (error || !playlist) {
    return (
      <div className={cn("flex-1 flex items-center justify-center", className)}>
        <Card className="p-8 text-center max-w-md">
          <div className="w-16 h-16 bg-red-500/10 rounded-xl mx-auto mb-4 flex items-center justify-center">
            <Music className="w-8 h-8 text-red-500" />
          </div>
          <h2 className="text-xl font-semibold text-foreground mb-2">
            {error || 'Playlist Not Found'}
          </h2>
          <p className="text-muted-foreground mb-4">
            The playlist you're looking for doesn't exist or has been removed.
          </p>
          <Button
            variant="primary"
            onClick={() => navigate('/play')}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Library
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <div className={cn("flex-1 overflow-y-auto", className)}>
      {/* Playlist Header */}
      <div className="px-6 pt-4 pb-8">
        <div className="flex items-end space-x-6">
          {/* Playlist Cover */}
          <div className="w-48 h-48 rounded-xl overflow-hidden flex-shrink-0">
            {playlist.coverUrl ? (
              <img
                src={playlist.coverUrl}
                alt={playlist.name}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-purple-600 to-purple-800 flex items-center justify-center">
                <Music className="w-20 h-20 text-white/80" />
              </div>
            )}
          </div>

          {/* Playlist Info */}
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-foreground/60 mb-2">
              {playlist.isPublic ? 'Public Playlist' : 'Private Playlist'}
            </p>
            <h1 className="text-4xl font-bold text-foreground mb-4 truncate">
              {playlist.name}
            </h1>
            {playlist.description && (
              <p className="text-foreground/70 mb-4 line-clamp-2">
                {playlist.description}
              </p>
            )}
            <div className="flex items-center space-x-1 text-sm text-foreground/60">
              <span className="font-medium">Created by you</span>
              <span>•</span>
              <span>{tracks.length} songs</span>
              {totalDuration > 0 && (
                <>
                  <span>•</span>
                  <span>{Math.floor(totalDuration / 60)} min {Math.floor(totalDuration % 60)} sec</span>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="px-6 pb-6">
        <div className="flex items-center space-x-4">
          {/* Play Button */}
          <Button
            variant="primary"
            size="lg"
            onClick={handlePlayPlaylist}
            disabled={tracks.length === 0 || playingPlaylist}
            className="w-14 h-14 rounded-full p-0"
          >
            {playingPlaylist ? (
              <Loader2 className="w-6 h-6 animate-spin" />
            ) : (
              <Play className="w-6 h-6 ml-1" />
            )}
          </Button>

          {/* Secondary Actions */}
          <Button variant="ghost" size="lg" className="w-12 h-12 rounded-full p-0">
            <Shuffle className="w-5 h-5" />
          </Button>

          {/* Share Options Menu */}
          <OverflowMenu
            items={[
              {
                id: 'share',
                label: 'Share',
                icon: Share,
                submenu: [
                  {
                    id: 'share-native',
                    label: 'Share via...',
                    icon: Share,
                    onClick: handleSharePlaylist
                  },
                  {
                    id: 'copy-link',
                    label: 'Copy link to playlist',
                    icon: Link,
                    onClick: handleCopyPlaylistLink
                  }
                ]
              }
            ]}
            placement="bottom-left"
            buttonClassName="w-12 h-12 rounded-full p-0"
            buttonContent={<Share className="w-5 h-5" />}
            title="Share options"
          />

          <Button variant="ghost" size="lg" className="w-12 h-12 rounded-full p-0">
            <MoreHorizontal className="w-5 h-5" />
          </Button>
        </div>
      </div>

      {/* Track List */}
      <div className="px-6">
        {tracks.length > 0 ? (
          <div className="space-y-1">
            {/* Header */}
            <div className="grid grid-cols-[auto_1fr_auto_1fr_auto_auto] gap-4 px-4 py-2 text-sm text-foreground/60 border-b border-border/20">
              <div className="w-8">#</div>
              <div>Title</div>
              <div className="w-12 text-center">
                <Clock className="w-4 h-4 mx-auto" />
              </div>
              <div>Album</div>
              <div className="w-8 text-center">
                <Heart className="w-4 h-4 mx-auto" />
              </div>
              <div className="w-8 text-center">
                <MoreHorizontal className="w-4 h-4 mx-auto" />
              </div>
            </div>

            {/* Tracks */}
            {tracks.map((track, index) => {
              const isCurrentTrack = currentTrack?.id === track.id;
              const isTrackPlaying = isCurrentTrack && isPlaying;

              return (
                <div
                  key={track.id}
                  onClick={() => handleTrackClick(track, index)}
                  className={cn(
                    "grid grid-cols-[auto_1fr_auto_1fr_auto_auto] gap-4 px-4 py-3 rounded-lg cursor-pointer transition-colors group",
                    isCurrentTrack ? "bg-primary/10" : "hover:bg-foreground/5"
                  )}
                >
                  {/* Track Number / Play Icon */}
                  <div className="w-8 flex items-center justify-center">
                    {isTrackPlaying ? (
                      <div className="w-4 h-4 flex items-center justify-center">
                        <div className="flex space-x-0.5">
                          <div className="w-0.5 h-4 bg-primary animate-pulse" />
                          <div className="w-0.5 h-4 bg-primary animate-pulse" style={{ animationDelay: '0.1s' }} />
                          <div className="w-0.5 h-4 bg-primary animate-pulse" style={{ animationDelay: '0.2s' }} />
                        </div>
                      </div>
                    ) : (
                      <>
                        <span className={cn(
                          "text-sm group-hover:hidden",
                          isCurrentTrack ? "text-primary font-medium" : "text-foreground/60"
                        )}>
                          {index + 1}
                        </span>
                        <Play className="w-4 h-4 text-foreground hidden group-hover:block" />
                      </>
                    )}
                  </div>

                  {/* Track Info */}
                  <div className="min-w-0">
                    <p className={cn(
                      "font-medium truncate",
                      isCurrentTrack ? "text-primary" : "text-foreground"
                    )}>
                      {track.title}
                    </p>
                    <p className="text-sm text-foreground/60 truncate">{track.artist}</p>
                  </div>

                  {/* Duration */}
                  <div className="w-12 text-center">
                    <span className="text-sm text-foreground/60">
                      {track.duration ? formatDuration(track.duration) : '--:--'}
                    </span>
                  </div>

                  {/* Album */}
                  <div className="min-w-0">
                    <p className="text-sm text-foreground/60 truncate">
                      {track.albumTitle || 'Single'}
                    </p>
                  </div>

                  {/* Like Button */}
                  <div className="w-8 flex items-center justify-center">
                    <Button
                      variant="ghost"
                      size="sm"
                      className={cn(
                        "h-8 w-8 p-0 transition-opacity",
                        isCurrentTrack ? "opacity-100" : "opacity-0 group-hover:opacity-100"
                      )}
                      onClick={(e) => {
                        e.stopPropagation();
                        console.log(`Like track: ${track.title}`);
                      }}
                      title="Like this track"
                    >
                      <Heart className="w-4 h-4 text-foreground" />
                    </Button>
                  </div>

                  {/* Overflow Menu */}
                  <div className="w-8 flex items-center justify-center">
                    <Button
                      variant="ghost"
                      size="sm"
                      className={cn(
                        "h-8 w-8 p-0 transition-opacity",
                        isCurrentTrack ? "opacity-100" : "opacity-0 group-hover:opacity-100"
                      )}
                      onClick={(e) => {
                        e.stopPropagation();
                        console.log(`More options for: ${track.title}`);
                      }}
                      title="More options"
                    >
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <Card className="p-8 text-center">
            <div className="w-16 h-16 bg-foreground/5 rounded-xl mx-auto mb-4 flex items-center justify-center">
              <Music className="w-8 h-8 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-semibold text-foreground mb-2">
              Your playlist is empty
            </h3>
            <p className="text-muted-foreground mb-4">
              Start building your playlist by adding some tracks!
            </p>
            <Button
              variant="primary"
              onClick={() => navigate('/explore')}
            >
              Find Music
            </Button>
          </Card>
        )}
      </div>
    </div>
  );
};
