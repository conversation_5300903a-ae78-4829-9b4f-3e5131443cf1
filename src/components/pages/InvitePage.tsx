import React, { useState } from 'react';
import { Card } from '../atoms/Card';
import { Button } from '../atoms/Button';
import { useAuth } from '../../hooks/useAuth';
import { Share2, Copy, Mail, MessageCircle, Users, Gift } from 'lucide-react';

interface InvitePageProps {
  className?: string;
}

export const InvitePage: React.FC<InvitePageProps> = ({ className }) => {
  const { user } = useAuth();
  const [copied, setCopied] = useState(false);
  
  const inviteLink = user ? `https://vibes.app/invite/${user.id}` : 'https://vibes.app';

  const handleCopyLink = () => {
    navigator.clipboard.writeText(inviteLink);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className={`h-full overflow-y-auto ${className || ''}`}>
      <div className="space-y-6 py-4">
        {/* Header Section */}
        <Card className="p-6" variant="glass">
          <div className="text-center space-y-3">
            <div className="w-12 h-12 bg-gradient-to-br from-violet-500 to-purple-500 rounded-xl mx-auto flex items-center justify-center">
              <span className="text-lg">🎉</span>
            </div>
            <h1 className="text-xl font-bold text-foreground">Invite Friends</h1>
            <p className="text-sm text-muted-foreground max-w-md mx-auto">
              Share the music experience with your friends and discover new tracks together.
            </p>
            {!user && (
              <p className="text-xs text-orange-500 bg-orange-500/10 px-3 py-2 rounded-lg">
                Sign in to invite friends and share your music discoveries!
              </p>
            )}
          </div>
        </Card>

        {user ? (
          <>
            {/* Invite Link Section */}
            <Card className="p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center">
                  <Share2 className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-foreground">Share Your Invite Link</h2>
                  <p className="text-xs text-muted-foreground">Send this link to invite friends</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2 p-3 bg-foreground/5 rounded-lg">
                <input
                  type="text"
                  value={inviteLink}
                  readOnly
                  className="flex-1 bg-transparent text-sm text-foreground outline-none"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCopyLink}
                  className="flex items-center space-x-2"
                >
                  <Copy className="w-4 h-4" />
                  <span>{copied ? 'Copied!' : 'Copy'}</span>
                </Button>
              </div>
            </Card>

            {/* Social Sharing Section */}
            <Card className="p-6">
              <h2 className="text-lg font-semibold text-foreground mb-4">Share on Social Media</h2>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {[
                  { name: 'Email', icon: Mail, color: 'from-gray-500 to-gray-600' },
                  { name: 'WhatsApp', icon: MessageCircle, color: 'from-green-500 to-green-600' },
                  { name: 'Twitter', icon: Share2, color: 'from-blue-400 to-blue-500' },
                  { name: 'Facebook', icon: Share2, color: 'from-blue-600 to-blue-700' }
                ].map((platform) => (
                  <Button
                    key={platform.name}
                    variant="outline"
                    className="flex flex-col items-center space-y-2 h-20"
                  >
                    <div className={`w-8 h-8 bg-gradient-to-br ${platform.color} rounded-lg flex items-center justify-center`}>
                      <platform.icon className="w-4 h-4 text-white" />
                    </div>
                    <span className="text-xs">{platform.name}</span>
                  </Button>
                ))}
              </div>
            </Card>

            {/* Referral Stats Section */}
            <Card className="p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-500 rounded-lg flex items-center justify-center">
                  <Users className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-foreground">Your Referrals</h2>
                  <p className="text-xs text-muted-foreground">Track your invite success</p>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 rounded-xl bg-foreground/5">
                  <div className="text-2xl font-bold text-foreground">12</div>
                  <div className="text-xs text-muted-foreground">Friends Invited</div>
                </div>
                <div className="text-center p-4 rounded-xl bg-foreground/5">
                  <div className="text-2xl font-bold text-foreground">8</div>
                  <div className="text-xs text-muted-foreground">Joined</div>
                </div>
                <div className="text-center p-4 rounded-xl bg-foreground/5">
                  <div className="text-2xl font-bold text-foreground">67%</div>
                  <div className="text-xs text-muted-foreground">Success Rate</div>
                </div>
              </div>
            </Card>

            {/* Rewards Section */}
            <Card className="p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-lg flex items-center justify-center">
                  <Gift className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-foreground">Referral Rewards</h2>
                  <p className="text-xs text-muted-foreground">Earn rewards for inviting friends</p>
                </div>
              </div>
              
              <div className="space-y-3">
                {[
                  { milestone: '5 Friends', reward: 'Premium Month Free', progress: 8, total: 5, completed: true },
                  { milestone: '10 Friends', reward: 'Exclusive Badge', progress: 8, total: 10, completed: false },
                  { milestone: '25 Friends', reward: 'VIP Features', progress: 8, total: 25, completed: false }
                ].map((reward, i) => (
                  <div key={i} className="p-4 rounded-xl bg-foreground/5">
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <h4 className="font-medium text-foreground text-sm">{reward.milestone}</h4>
                        <p className="text-xs text-muted-foreground">{reward.reward}</p>
                      </div>
                      {reward.completed && (
                        <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">✓</span>
                        </div>
                      )}
                    </div>
                    <div className="w-full bg-foreground/10 rounded-full h-2">
                      <div 
                        className="bg-gradient-to-r from-violet-500 to-purple-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${Math.min((reward.progress / reward.total) * 100, 100)}%` }}
                      ></div>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      {reward.progress}/{reward.total} friends
                    </p>
                  </div>
                ))}
              </div>
            </Card>

            {/* Recent Invites */}
            <Card className="p-6">
              <h2 className="text-lg font-semibold text-foreground mb-4">Recent Invites</h2>
              <div className="space-y-3">
                {[
                  { name: 'Sarah Johnson', status: 'Joined', date: '2 days ago', avatar: 'SJ' },
                  { name: 'Mike Chen', status: 'Pending', date: '1 week ago', avatar: 'MC' },
                  { name: 'Emma Wilson', status: 'Joined', date: '2 weeks ago', avatar: 'EW' }
                ].map((invite, i) => (
                  <div key={i} className="flex items-center space-x-3 p-3 rounded-xl hover:bg-foreground/5 transition-colors">
                    <div className="w-10 h-10 bg-gradient-to-br from-purple-400 to-pink-500 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold text-xs">{invite.avatar}</span>
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-foreground text-sm">{invite.name}</h4>
                      <p className="text-xs text-muted-foreground">{invite.date}</p>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      invite.status === 'Joined' 
                        ? 'bg-green-500/20 text-green-600' 
                        : 'bg-yellow-500/20 text-yellow-600'
                    }`}>
                      {invite.status}
                    </span>
                  </div>
                ))}
              </div>
            </Card>
          </>
        ) : (
          <Card className="p-8 text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-violet-500 to-purple-500 rounded-xl mx-auto mb-4 flex items-center justify-center">
              <span className="text-2xl">🎉</span>
            </div>
            <h2 className="text-xl font-semibold text-foreground mb-2">Start Inviting Friends</h2>
            <p className="text-muted-foreground mb-4">
              Sign in to get your personal invite link and start sharing the music experience with friends.
            </p>
            <Button variant="primary">
              Sign In to Get Started
            </Button>
          </Card>
        )}
      </div>
    </div>
  );
};
