import React from 'react';
import { Link } from 'react-router-dom';
import { Home, ArrowLeft } from 'lucide-react';
import { Card } from '../atoms/Card';
import { Button } from '../atoms/Button';

export const NotFoundPage: React.FC = () => {
  return (
    <div className="h-full flex items-center justify-center p-6">
      <Card className="max-w-md w-full text-center p-8" variant="glass">
        <div className="space-y-6">
          {/* 404 Icon */}
          <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl mx-auto flex items-center justify-center">
            <span className="text-3xl text-white font-bold">404</span>
          </div>

          {/* Error Message */}
          <div className="space-y-3">
            <h1 className="text-2xl font-bold text-foreground">Page Not Found</h1>
            <p className="text-muted-foreground">
              Looks like you've followed a broken link or entered a URL that doesn't exist on this site.
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button
              variant="primary"
              size="md"
              asChild
              className="flex items-center space-x-2"
            >
              <Link to="/">
                <Home className="w-4 h-4" />
                <span>Go Home</span>
              </Link>
            </Button>
            
            <Button
              variant="ghost"
              size="md"
              onClick={() => window.history.back()}
              className="flex items-center space-x-2"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Go Back</span>
            </Button>
          </div>

          {/* Help Text */}
          <div className="pt-4 border-t border-border/20">
            <p className="text-xs text-muted-foreground">
              If you think this is a mistake, please{' '}
              <Link to="/chat" className="text-primary hover:underline">
                contact support
              </Link>
            </p>
          </div>
        </div>
      </Card>
    </div>
  );
};
