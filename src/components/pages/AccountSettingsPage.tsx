import React, { useState } from 'react';
import { Settings, Mail, Shield, Bell, Eye, Trash2, Save, X } from 'lucide-react';
import { Card } from '../atoms/Card';
import { Button } from '../atoms/Button';
import { cn } from '../../utils/cn';
import { User as UserType } from '../../types';
import { useAuth } from '../../hooks/useAuth';

interface AccountSettingsPageProps {
  className?: string;
}

export const AccountSettingsPage: React.FC<AccountSettingsPageProps> = ({ className }) => {
  const { user } = useAuth();
  const [activeSection, setActiveSection] = useState('account');

  if (!user) {
    return (
      <div className={cn("h-full flex items-center justify-center", className)}>
        <Card className="p-8 text-center">
          <Settings className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-foreground mb-2">Sign In Required</h2>
          <p className="text-muted-foreground">Please sign in to access account settings.</p>
        </Card>
      </div>
    );
  }

  const settingsSections = [
    { id: 'account', label: 'Account', icon: Mail },
    { id: 'privacy', label: 'Privacy', icon: Shield },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'appearance', label: 'Appearance', icon: Eye },
  ];

  return (
    <div className={cn("h-full overflow-y-auto", className)}>
      <div className="py-6">
        <Card className="p-6">
          {/* Settings Header */}
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-foreground mb-2">Account Settings</h1>
            <p className="text-muted-foreground">Manage your account preferences and settings</p>
          </div>

          <div className="flex flex-col lg:flex-row gap-6">
            {/* Settings Navigation */}
            <div className="lg:w-64 flex-shrink-0">
              <nav className="space-y-2">
                {settingsSections.map((section) => {
                  const Icon = section.icon;
                  return (
                    <button
                      key={section.id}
                      onClick={() => setActiveSection(section.id)}
                      className={cn(
                        "w-full flex items-center space-x-3 px-4 py-3 rounded-xl text-left transition-colors",
                        activeSection === section.id
                          ? "bg-primary text-white"
                          : "hover:bg-foreground/5 text-foreground"
                      )}
                    >
                      <Icon className="w-5 h-5" />
                      <span className="font-medium">{section.label}</span>
                    </button>
                  );
                })}
              </nav>
            </div>

            {/* Settings Content */}
            <div className="flex-1">
              {/* Account Settings */}
              {activeSection === 'account' && (
                <div className="space-y-6">
                  <div>
                    <h2 className="text-xl font-semibold text-foreground mb-4">Account Information</h2>
                    
                    {/* Email */}
                    <div className="space-y-4">
                      <div className="p-4 rounded-xl bg-foreground/5">
                        <label className="block text-sm font-medium text-foreground mb-2">
                          Email Address
                        </label>
                        <div className="flex items-center justify-between">
                          <p className="text-foreground">{user.email}</p>
                          <Button variant="outline" size="sm">
                            Change Email
                          </Button>
                        </div>
                        <p className="text-xs text-muted-foreground mt-2">
                          This email is used for sign-in and important notifications
                        </p>
                      </div>

                      {/* Password */}
                      <div className="p-4 rounded-xl bg-foreground/5">
                        <label className="block text-sm font-medium text-foreground mb-2">
                          Password
                        </label>
                        <div className="flex items-center justify-between">
                          <p className="text-muted-foreground">••••••••</p>
                          <Button variant="outline" size="sm">
                            Change Password
                          </Button>
                        </div>
                        <p className="text-xs text-muted-foreground mt-2">
                          Last changed 30 days ago
                        </p>
                      </div>

                      {/* Account Created */}
                      <div className="p-4 rounded-xl bg-foreground/5">
                        <label className="block text-sm font-medium text-foreground mb-2">
                          Account Created
                        </label>
                        <p className="text-foreground">
                          {user.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'Unknown'}
                        </p>
                        <p className="text-xs text-muted-foreground mt-2">
                          Member since you joined Vibes
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Danger Zone */}
                  <div className="border-t border-border/20 pt-6">
                    <h3 className="text-lg font-semibold text-foreground mb-4">Danger Zone</h3>
                    <div className="p-4 rounded-xl border border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/20">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium text-red-700 dark:text-red-400">Delete Account</h4>
                          <p className="text-sm text-red-600 dark:text-red-500 mt-1">
                            Permanently delete your account and all associated data
                          </p>
                        </div>
                        <Button variant="outline" size="sm" className="text-red-600 border-red-300 hover:bg-red-50">
                          <Trash2 className="w-4 h-4 mr-2" />
                          Delete Account
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Privacy Settings */}
              {activeSection === 'privacy' && (
                <div className="space-y-6">
                  <div>
                    <h2 className="text-xl font-semibold text-foreground mb-4">Privacy Settings</h2>
                    <p className="text-muted-foreground mb-6">Control who can see your information and activity</p>
                    
                    <div className="space-y-4">
                      <div className="p-4 rounded-xl bg-foreground/5">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="font-medium text-foreground">Profile Visibility</h4>
                            <p className="text-sm text-muted-foreground">Who can see your profile</p>
                          </div>
                          <select className="px-3 py-2 border border-border rounded-lg bg-background text-foreground">
                            <option>Everyone</option>
                            <option>Friends Only</option>
                            <option>Private</option>
                          </select>
                        </div>
                      </div>

                      <div className="p-4 rounded-xl bg-foreground/5">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="font-medium text-foreground">Activity Status</h4>
                            <p className="text-sm text-muted-foreground">Show when you're online</p>
                          </div>
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" className="sr-only peer" defaultChecked />
                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Notifications Settings */}
              {activeSection === 'notifications' && (
                <div className="space-y-6">
                  <div>
                    <h2 className="text-xl font-semibold text-foreground mb-4">Notification Preferences</h2>
                    <p className="text-muted-foreground mb-6">Choose what notifications you want to receive</p>
                    
                    <div className="space-y-4">
                      {[
                        { label: 'New Followers', description: 'When someone follows you' },
                        { label: 'Track Likes', description: 'When someone likes your tracks' },
                        { label: 'Comments', description: 'When someone comments on your tracks' },
                        { label: 'Messages', description: 'When you receive new messages' },
                        { label: 'Weekly Summary', description: 'Weekly activity summary email' },
                      ].map((notification, index) => (
                        <div key={index} className="p-4 rounded-xl bg-foreground/5">
                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-medium text-foreground">{notification.label}</h4>
                              <p className="text-sm text-muted-foreground">{notification.description}</p>
                            </div>
                            <label className="relative inline-flex items-center cursor-pointer">
                              <input type="checkbox" className="sr-only peer" defaultChecked />
                              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                            </label>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Appearance Settings */}
              {activeSection === 'appearance' && (
                <div className="space-y-6">
                  <div>
                    <h2 className="text-xl font-semibold text-foreground mb-4">Appearance</h2>
                    <p className="text-muted-foreground mb-6">Customize how Vibes looks and feels</p>
                    
                    <div className="space-y-4">
                      <div className="p-4 rounded-xl bg-foreground/5">
                        <h4 className="font-medium text-foreground mb-3">Theme</h4>
                        <div className="grid grid-cols-3 gap-3">
                          {['Light', 'Dark', 'System'].map((theme) => (
                            <button
                              key={theme}
                              className={cn(
                                "p-3 rounded-lg border text-center transition-colors",
                                theme === 'Dark' 
                                  ? "border-primary bg-primary/10 text-primary" 
                                  : "border-border hover:bg-foreground/5"
                              )}
                            >
                              {theme}
                            </button>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};
