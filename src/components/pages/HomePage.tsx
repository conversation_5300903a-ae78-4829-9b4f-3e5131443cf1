import React from 'react';
import { WelcomeCarousel } from '../organisms/WelcomeCarousel';
import { DiscoverSections } from '../discover/DiscoverSections';
import { useResponsive } from '../../hooks/useResponsive';

interface HomePageProps {
  className?: string;
}

/**
 * ✅ PRODUCTION READY - This component is working correctly in deployment
 *
 * ⚠️  DEPLOYMENT WARNING:
 * - This component has been tested and is working in production at https://vibes-web.netlify.app/
 * - Any changes should be tested locally first, then deployed incrementally
 * - Do NOT remove or modify imports/components without verifying deployment impact
 * - If adding debug components, remove them carefully after testing
 */
export const HomePage: React.FC<HomePageProps> = ({ className }) => {
  const { isMobile } = useResponsive();

  return (
    <div className={`h-full overflow-y-auto ${className || ''}`}>
      {/* Mobile: py-3 for consistent top/bottom spacing like Spotify */}
      <div className={`${isMobile ? 'space-y-6 py-3' : 'h-full py-4'}`}>
        {/* Main Welcome Carousel - Always shown */}
        <div className={isMobile ? '' : 'h-full'}>
          <WelcomeCarousel />
        </div>

        {/* Discover Content - Mobile only */}
        {isMobile && (
          <DiscoverSections
            showAllSections={false}
            className="mt-6"
          />
        )}
      </div>
    </div>
  );
};
