import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  Home,
  Compass,
  Library,
  MessageCircle,
  User,
  Menu,
  X,
  Play,
  Music,
  Heart,
  Settings
} from 'lucide-react';
// Import filled versions for active states
import {
  Home as HomeFilled,
  Play as PlayFilled,
  Library as LibraryFilled,
  MessageCircle as MessageCircleFilled
} from 'lucide-react';
import { cn } from '../../utils/cn';
import { Button } from '../atoms/Button';
import { Card } from '../atoms/Card';
import { useAuth } from '../../hooks/useAuth';
import { useMusicStore } from '../../store/musicStore';
import { RoleService } from '../../services/roleService';

interface MobileNavigationProps {
  className?: string;
  isMenuOpen?: boolean;
  onMenuToggle?: () => void;
  onAdminPanelOpen?: () => void;
}

interface NavItem {
  icon: React.ComponentType<{ className?: string }>;
  iconFilled: React.ComponentType<{ className?: string }>;
  label: string;
  path: string;
  active: boolean;
  badge?: number;
}

/**
 * Mobile Navigation Component
 * Provides bottom navigation bar for mobile devices
 * Includes hamburger menu for secondary navigation
 */
export const MobileNavigation: React.FC<MobileNavigationProps> = ({
  className,
  isMenuOpen = false,
  onMenuToggle,
  onAdminPanelOpen
}) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { currentTrack } = useMusicStore();

  // Close menu when route changes
  useEffect(() => {
    if (isMenuOpen && onMenuToggle) {
      onMenuToggle();
    }
  }, [location.pathname]);

  // Prevent body scroll when menu is open
  useEffect(() => {
    if (isMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isMenuOpen]);

  // Primary navigation items for bottom bar - 4 main tabs
  const primaryNavItems: NavItem[] = [
    {
      icon: Home,
      iconFilled: HomeFilled,
      label: 'Home',
      path: '/',
      active: location.pathname === '/',
    },
    {
      icon: Play,
      iconFilled: PlayFilled,
      label: 'Play',
      path: '/play',
      active: location.pathname === '/play',
    },
    {
      icon: Library,
      iconFilled: LibraryFilled,
      label: 'Library',
      path: '/library',
      active: location.pathname === '/library',
    },
    {
      icon: MessageCircle,
      iconFilled: MessageCircleFilled,
      label: 'Chat',
      path: '/chat',
      active: location.pathname === '/chat',
    },
  ];

  // Secondary navigation items for header menu (accessed via profile photo)
  const secondaryNavItems: NavItem[] = [
    {
      icon: User,
      iconFilled: User, // User icon doesn't have a filled version
      label: 'Profile',
      path: '/profile',
      active: location.pathname === '/profile',
    },
    {
      icon: Compass,
      iconFilled: Compass, // Compass icon doesn't have a filled version
      label: 'Discover',
      path: '/explore',
      active: location.pathname === '/explore',
    },
    {
      icon: Heart,
      iconFilled: Heart, // Heart icon can be filled via CSS
      label: 'Liked Songs',
      path: '/library?tab=liked',
      active: location.pathname === '/library' && location.search.includes('liked'),
    },
    {
      icon: Music,
      iconFilled: Music, // Music icon doesn't have a filled version
      label: 'Playlists',
      path: '/library?tab=playlists',
      active: location.pathname === '/library' && location.search.includes('playlists'),
    },
    // Add Settings/Admin Panel for admin users
    ...(user && RoleService.isAdmin(user) ? [{
      icon: Settings,
      iconFilled: Settings,
      label: 'Admin Panel',
      path: '#admin',
      active: false,
    }] : []),
  ];

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  const handleMenuItemClick = (path: string) => {
    if (path === '#admin') {
      // Handle admin panel opening
      if (onAdminPanelOpen) {
        onAdminPanelOpen();
      }
    } else {
      navigate(path);
    }

    if (onMenuToggle) {
      onMenuToggle();
    }
  };

  // Mobile navigation now shows on all pages including Play mode

  return (
    <>
      {/* Mobile Bottom Navigation Bar */}
      <nav className={cn(
        'fixed bottom-0 left-0 right-0 z-overlay backdrop-blur-md rounded-t-[2.5rem]',
        'bg-gradient-to-t from-background via-background/95 to-transparent',
        'safe-area-bottom',
        className
      )}>
        <div className="mx-3 mb-2 rounded-[2rem] bg-background/90 backdrop-blur-md border border-border/20 shadow-lg">
            <div className="flex items-center justify-around px-2 py-2">
            {primaryNavItems.map((item) => {
            const Icon = item.active ? item.iconFilled : item.icon;
            const isActive = item.active;

            return (
              <Button
                key={item.label}
                variant="ghost"
                size="sm"
                onClick={() => handleNavigation(item.path)}
                className={cn(
                  'flex flex-col items-center justify-center h-12 w-12 p-1 touch-target',
                  'transition-all duration-200 hover:bg-transparent',
                  isActive
                    ? 'text-primary'
                    : 'text-foreground/60 hover:text-foreground'
                )}
                title={item.label}
              >
                <Icon className={cn(
                  'w-5 h-5 mb-1 transition-all duration-200',
                  isActive && 'scale-105'
                )} />
                <span className={cn(
                  'text-[10px] leading-none transition-all duration-200',
                  isActive ? 'font-bold text-primary' : 'font-medium text-foreground/60'
                )}>
                  {item.label}
                </span>
                {item.badge && (
                  <div className="absolute -top-1 -right-1 bg-primary text-primary-foreground text-xs rounded-full w-5 h-5 flex items-center justify-center">
                    {item.badge}
                  </div>
                )}
              </Button>
            );
            })}
          </div>
        </div>
      </nav>

      {/* Mobile Menu Overlay */}
      {isMenuOpen && (
        <div className="fixed inset-0 z-modal bg-black/50 backdrop-blur-sm animate-fade-in">
          <div
            className="absolute inset-0"
            onClick={onMenuToggle}
          />
          
          {/* Menu Content */}
          <div className="absolute bottom-0 left-0 right-0 animate-slide-up">
            <Card className="rounded-t-3xl rounded-b-none border-0 bg-background">
              <div className="p-6 safe-area-bottom">
                {/* Menu Header */}
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold">Menu</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onMenuToggle}
                    className="h-8 w-8 p-0"
                  >
                    <X className="w-5 h-5" />
                  </Button>
                </div>

                {/* User Info */}
                {user && (
                  <div className="flex items-center space-x-3 p-3 rounded-2xl bg-foreground/5 mb-6">
                    <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                      <User className="w-5 h-5 text-primary-foreground" />
                    </div>
                    <div>
                      <p className="font-medium">{user.displayName || 'User'}</p>
                      <p className="text-sm text-foreground/60">{user.email}</p>
                    </div>
                  </div>
                )}

                {/* Currently Playing */}
                {currentTrack && (
                  <div className="flex items-center space-x-3 p-3 rounded-2xl bg-primary/10 mb-6">
                    <div className="w-10 h-10 bg-primary/20 rounded-lg flex items-center justify-center">
                      <Play className="w-5 h-5 text-primary" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="font-medium truncate">{currentTrack.title}</p>
                      <p className="text-sm text-foreground/60 truncate">{currentTrack.artist}</p>
                    </div>
                  </div>
                )}

                {/* Secondary Navigation */}
                <div className="space-y-2">
                  {secondaryNavItems.map((item) => {
                    const Icon = item.active ? item.iconFilled : item.icon;
                    return (
                      <Button
                        key={item.label}
                        variant="ghost"
                        onClick={() => handleMenuItemClick(item.path)}
                        className={cn(
                          'w-full justify-start h-12 px-4 touch-target',
                          item.active
                            ? 'text-primary bg-primary/10'
                            : 'text-foreground hover:bg-foreground/5'
                        )}
                      >
                        <Icon className={cn(
                          "w-5 h-5 mr-3",
                          item.label === 'Liked Songs' && item.active && "fill-current"
                        )} />
                        <span className={cn(
                          item.active && "font-semibold"
                        )}>
                          {item.label}
                        </span>
                      </Button>
                    );
                  })}
                </div>
              </div>
            </Card>
          </div>
        </div>
      )}
    </>
  );
};
