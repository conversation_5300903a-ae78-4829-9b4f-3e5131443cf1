import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Music, Bell, Settings, UserPlus, Play, Compass, MessageCircle, User, Upload, ArrowLeft, Home, Menu, Search } from 'lucide-react';
import { Button } from '../atoms/Button';
import { Card } from '../atoms/Card';
import { ThemeToggle } from '../atoms/ThemeToggle';
import { AuthModal } from '../auth/AuthModal';
import { ProfileModal } from '../profile/ProfileModal';
import { UploadModal } from '../upload/UploadModal';
import { AdminPanel } from '../admin/AdminPanel';
import { useMusicStore } from '../../store/musicStore';
import { useAuth } from '../../hooks/useAuth';
import { RoleService } from '../../services/roleService';
import { useResponsive } from '../../hooks/useResponsive';
import { cn } from '../../utils/cn';

// Temporary debug logging
const debugUserRole = (user: any) => {
  if (user) {
    console.log('🔍 Header Debug - User object:', user);
    console.log('🔍 Header Debug - User role:', user.role);
    console.log('🔍 Header Debug - Can upload music:', RoleService.canUploadMusic(user));
    console.log('🔍 Header Debug - Is admin:', RoleService.isAdmin(user));
  }
};

interface HeaderProps {
  onMobileMenuToggle?: () => void;
  isMobileMenuOpen?: boolean;
}

export const Header: React.FC<HeaderProps> = ({
  onMobileMenuToggle,
  isMobileMenuOpen = false
}) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { triggerLibraryRefresh, currentTrack, isPlaying } = useMusicStore();
  const { user, signOut } = useAuth();

  // Debug user role on every render
  React.useEffect(() => {
    debugUserRole(user);
  }, [user]);
  const [authModal, setAuthModal] = useState<{ isOpen: boolean; mode: 'login' | 'signup' }>({
    isOpen: false,
    mode: 'login'
  });
  const [profileModal, setProfileModal] = useState(false);
  const [uploadModal, setUploadModal] = useState(false);
  const [adminPanel, setAdminPanel] = useState(false);

  // Dynamic header text based on current route and browse state
  const getHeaderText = () => {
    const path = location.pathname;
    if (path === '/play') return 'Play';
    if (path === '/explore') {
      return 'Discover'; // Always show "Discover" for consistency
    }
    if (path === '/chat') return 'Chat';
    if (path === '/library') return 'Library';
    if (path === '/profile') return 'Profile';
    if (path === '/invite') return 'Invite';
    if (path === '/settings') return 'Settings';
    return 'Vibes';
  };

  // Function to handle back navigation
  const handleBackNavigation = () => {
    if (location.pathname === '/explore') {
      // Navigate back to home from explore page
      navigate('/');
    } else {
      // Regular back navigation
      navigate(-1);
    }
  };

  const handleAuthModalOpen = (mode: 'login' | 'signup') => {
    setAuthModal({ isOpen: true, mode });
  };

  const handleAuthModalClose = () => {
    setAuthModal({ isOpen: false, mode: 'login' });
  };

  const { isMobile, isTablet } = useResponsive();

  // Hide header in mobile Play mode for fullscreen experience
  if (isMobile && location.pathname === '/play') {
    return null;
  }

  return (
    <>
      <header className={cn(
        "fixed top-0 left-0 right-0 z-40 safe-area-top",
        isMobile ? "h-16" : "h-24 bg-background/80 backdrop-blur-md" // Remove background/blur in mobile, keep in desktop
      )} style={{
        paddingLeft: isMobile ? '0px' : '12px',
        paddingRight: isMobile ? '0px' : '12px',
        paddingTop: isMobile ? '0px' : '12px',
        paddingBottom: isMobile ? '0px' : '12px' // Remove padding in mobile view for curved effect
      }}>
        <Card
          className={cn(
            "h-full",
            isMobile ? "mx-3 mb-2 mt-3 bg-background/20 backdrop-blur-xl border-white/10" : "" // Cool transparent glass effect in mobile
          )}
          variant={isMobile ? "glass" : "glass"}
          rounded={isMobile ? "rounded-[2rem]" : "rounded-3xl"} // Fully rounded in mobile view
        >
          <div className={cn(
            "flex items-center h-full",
            isMobile ? "px-3" : "px-6"
          )}>
            {/* Logo with Dynamic Text - Responsive Width */}
            <div className={cn(
              "flex items-center",
              isMobile ? "w-auto flex-1 ml-1 space-x-2" : "w-48 space-x-3" // Reduce spacing in mobile view
            )}>
              {/* Back Button - Show when on explore page */}
              {location.pathname === '/explore' ? (
                <button
                  onClick={handleBackNavigation}
                  className="w-8 h-8 bg-gradient-to-br from-gray-500 to-gray-600 rounded-lg flex items-center justify-center hover:from-gray-400 hover:to-gray-500 transition-all duration-200"
                  title="Back to Home"
                >
                  <ArrowLeft className="w-4 h-4 text-white" />
                </button>
              ) : (
                <Music className="w-6 h-6 text-foreground font-bold" strokeWidth={3} />
              )}
              <button
                onClick={() => navigate('/')}
                className="text-left hover:opacity-80 transition-opacity duration-200"
                title="Home"
              >
                <h1 className="text-xl font-bold text-foreground transition-colors duration-300">
                  {getHeaderText()}
                </h1>
              </button>
            </div>
            
            {/* Center - Vibes Mode Buttons - Responsive */}
            <div className={cn(
              "flex-1 flex items-center justify-center",
              isMobile && "hidden" // Hide center buttons on mobile
            )}>
              <div className={cn(
                "flex items-center",
                isMobile ? "space-x-2" : "space-x-4"
              )}>
                {/* Play Toggle */}
                <button
                  onClick={() => navigate('/play')}
                  className={cn(
                    'relative flex items-center justify-center rounded-full transition-all duration-300 border',
                    'hover:scale-[1.05] active:scale-[0.95] focus:outline-none focus:ring-2 focus:ring-offset-1 touch-target',
                    isMobile ? 'w-10 h-10' : 'w-14 h-14',
                    location.pathname === '/play'
                      ? 'bg-gradient-to-r from-green-500/20 to-emerald-500/20 text-green-600 dark:text-green-400 border-green-500/30 shadow-[0_0_20px_rgba(34,197,94,0.15)] focus:ring-green-500/30'
                      : 'text-foreground/60 hover:text-green-500 hover:bg-green-500/8 border-border/20 hover:border-green-500/20 focus:ring-green-500/20'
                  )}
                  title="Play"
                >
                  {location.pathname === '/play' && (
                    <div className="absolute inset-0 rounded-full opacity-50 animate-pulse">
                      <div className="absolute inset-0 rounded-full blur-sm bg-gradient-to-r from-green-500/10 to-emerald-500/10" />
                    </div>
                  )}
                  
                  <Play className={cn(
                    "transition-all duration-300",
                    isMobile ? "w-4 h-4" : "w-5 h-5",
                    location.pathname === '/play' && "animate-pulse drop-shadow-sm"
                  )} />
                  
                  {(currentTrack && isPlaying) && (
                    <div className="absolute -top-1 -right-1 w-3 h-3 rounded-full bg-green-500 shadow-sm" />
                  )}
                </button>

                {/* Explore Toggle */}
                <button
                  onClick={() => navigate('/explore')}
                  className={cn(
                    'relative flex items-center justify-center rounded-full transition-all duration-300 border',
                    'hover:scale-[1.05] active:scale-[0.95] w-14 h-14',
                    'focus:outline-none focus:ring-2 focus:ring-offset-1',
                    location.pathname === '/explore'
                      ? 'bg-gradient-to-r from-blue-500/20 to-cyan-500/20 text-blue-600 dark:text-blue-400 border-blue-500/30 shadow-[0_0_20px_rgba(59,130,246,0.15)] focus:ring-blue-500/30'
                      : 'text-foreground/60 hover:text-blue-500 hover:bg-blue-500/8 border-border/20 hover:border-blue-500/20 focus:ring-blue-500/20'
                  )}
                  title="Discover"
                >
                  {location.pathname === '/explore' && (
                    <div className="absolute inset-0 rounded-full opacity-50 animate-pulse">
                      <div className="absolute inset-0 rounded-full blur-sm bg-gradient-to-r from-blue-500/10 to-cyan-500/10" />
                    </div>
                  )}
                  
                  <Compass className={cn(
                    "w-5 h-5 transition-all duration-300",
                    location.pathname === '/explore' && "animate-pulse drop-shadow-sm"
                  )} />
                  
                  {location.pathname === '/explore' && (
                    <div className="absolute -top-1 -right-1 w-3 h-3 rounded-full bg-blue-500 shadow-sm" />
                  )}
                </button>

                {/* Chat Toggle */}
                <button
                  onClick={() => navigate('/chat')}
                  className={cn(
                    'relative flex items-center justify-center rounded-full transition-all duration-300 border',
                    'hover:scale-[1.05] active:scale-[0.95] w-14 h-14',
                    'focus:outline-none focus:ring-2 focus:ring-offset-1',
                    location.pathname === '/chat'
                      ? 'bg-gradient-to-r from-orange-500/20 to-red-500/20 text-orange-600 dark:text-orange-400 border-orange-500/30 shadow-[0_0_20px_rgba(249,115,22,0.15)] focus:ring-orange-500/30'
                      : 'text-foreground/60 hover:text-orange-500 hover:bg-orange-500/8 border-border/20 hover:border-orange-500/20 focus:ring-orange-500/20'
                  )}
                  title="Chat"
                >
                  {location.pathname === '/chat' && (
                    <div className="absolute inset-0 rounded-full opacity-50 animate-pulse">
                      <div className="absolute inset-0 rounded-full blur-sm bg-gradient-to-r from-orange-500/10 to-red-500/10" />
                    </div>
                  )}
                  
                  <MessageCircle className={cn(
                    "w-5 h-5 transition-all duration-300",
                    location.pathname === '/chat' && "animate-pulse drop-shadow-sm"
                  )} />
                  
                  {location.pathname === '/chat' && (
                    <div className="absolute -top-1 -right-1 w-3 h-3 rounded-full bg-orange-500 shadow-sm" />
                  )}
                </button>
              </div>
            </div>
            
            {/* Actions - Responsive Width Container with Auth/Profile */}
            <div className={cn(
              "flex items-center justify-end",
              isMobile ? "w-auto space-x-2" : "w-48 space-x-2" // Consistent spacing across mobile and desktop
            )}>
              {user ? (
                <>
                  {/* Upload Button - Only show for admin users and hide on mobile */}
                  {RoleService.canUploadMusic(user) && !isMobile && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setUploadModal(true)}
                      className="hidden sm:flex"
                    >
                      <Upload className="w-4 h-4 mr-2" />
                      <span>Upload</span>
                    </Button>
                  )}

                  {/* Admin Panel Button - Only show for admin users on desktop */}
                  {RoleService.isAdmin(user) && !isMobile && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setAdminPanel(true)}
                      className="h-10 w-10 p-0 touch-target"
                      title="Admin Panel"
                    >
                      <Settings className="w-5 h-5" />
                    </Button>
                  )}

                  {/* Notifications - Hide on mobile */}
                  {!isMobile && (
                    <Button variant="ghost" size="sm" className="h-10 w-10 p-0 touch-target">
                      <Bell className="w-5 h-5" />
                    </Button>
                  )}

                  <ThemeToggle
                    variant="button"
                    className={cn(
                      "touch-target",
                      isMobile ? "h-8 w-8" : "h-10 w-10"
                    )}
                  />

                  {/* Profile Button - Mobile: Opens menu, Desktop: Opens profile modal */}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={isMobile ? onMobileMenuToggle : () => setProfileModal(true)}
                    className={cn(
                      "p-0 touch-target rounded-full overflow-hidden",
                      isMobile ? "h-8 w-8" : "h-10 w-10",
                      isMobile && isMobileMenuOpen && "ring-2 ring-primary ring-offset-1"
                    )}
                    title={isMobile ? "Open menu" : "Profile"}
                  >
                    {user.photoURL ? (
                      <img
                        src={user.photoURL}
                        alt={user.displayName || 'User'}
                        className="w-full h-full object-cover rounded-full"
                      />
                    ) : (
                      <div className={cn(
                        "w-full h-full bg-gradient-to-br from-primary to-primary/80 rounded-full flex items-center justify-center",
                        "text-primary-foreground font-medium"
                      )}>
                        {(user.displayName || user.email || 'U').charAt(0).toUpperCase()}
                      </div>
                    )}
                  </Button>
                </>
              ) : (
                <>
                  <ThemeToggle
                    variant="button"
                    className={cn(
                      "touch-target",
                      isMobile ? "h-8 w-8" : "h-10 w-10"
                    )}
                  />

                  <Button
                    variant="primary"
                    size="sm"
                    onClick={() => handleAuthModalOpen('login')}
                    className={cn(
                      "touch-target bg-gradient-to-r from-violet-600 to-purple-600 text-white font-bold border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105",
                      isMobile && "text-xs px-3"
                    )}
                  >
                    {isMobile ? "Sign In" : "Sign In"}
                  </Button>
                </>
              )}
            </div>
          </div>
        </Card>
      </header>

      {/* Modals */}
      <AuthModal
        isOpen={authModal.isOpen}
        onClose={handleAuthModalClose}
        mode={authModal.mode}
        onModeChange={(mode) => setAuthModal(prev => ({ ...prev, mode }))}
      />
      
      {user && (
        <>
          <ProfileModal
            isOpen={profileModal}
            onClose={() => setProfileModal(false)}
          />
          
          <UploadModal
            isOpen={uploadModal}
            onClose={() => setUploadModal(false)}
            onUploadComplete={() => {
              triggerLibraryRefresh();
              console.log('Upload completed, triggering library refresh');
            }}
          />

          {RoleService.isAdmin(user) && (
            <AdminPanel
              isOpen={adminPanel}
              onClose={() => setAdminPanel(false)}
            />
          )}
        </>
      )}
    </>
  );
};