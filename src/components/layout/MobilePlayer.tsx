import React, { useState, useEffect } from 'react';
import { <PERSON>, Pause, <PERSON><PERSON><PERSON><PERSON>, SkipForward, Heart, MoreHorizontal, ChevronUp, ChevronDown, X } from 'lucide-react';
import { useLocation } from 'react-router-dom';
import { useMusicStore } from '../../store/musicStore';
import { useAudioPlayer } from '../../hooks/useAudioPlayer';
import { useAuth } from '../../hooks/useAuth';
import { EngagementService } from '../../services/engagementService';
import { Button } from '../atoms/Button';
import { Card } from '../atoms/Card';
import { cn } from '../../utils/cn';
import { PlayModeCard } from '../organisms/PlayModeCard';

interface MobilePlayerProps {
  className?: string;
}

// Helper function to format time
const formatTime = (seconds: number): string => {
  if (!seconds || isNaN(seconds)) return '0:00';
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins}:${secs.toString().padStart(2, '0')}`;
};

/**
 * Mobile-optimized Global Player Component
 * Compact design with essential controls and expandable interface
 */
export const MobilePlayer: React.FC<MobilePlayerProps> = ({ className }) => {
  const location = useLocation();
  const { user } = useAuth();
  const { nextTrack, previousTrack } = useMusicStore();
  const [isLiked, setIsLiked] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [dragY, setDragY] = useState(0);

  // Don't show mini player on the /play page
  const isOnPlayPage = location.pathname === '/play';

  // Use the audio player hook for actual playback
  const {
    currentTrack,
    isPlaying,
    currentTime,
    duration,
    seekTo,
    togglePlay
  } = useAudioPlayer();

  // Mobile-specific play handler with audio context initialization
  const handleMobilePlay = async () => {
    // Initialize audio context for mobile browsers within user gesture
    const { AudioService } = await import('../../services/audioService');
    await AudioService.initializeAudioContext();

    // Then toggle play
    togglePlay();
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!currentTrack || !duration) return;
    
    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const percentage = clickX / rect.width;
    const newTime = percentage * duration;
    
    seekTo(newTime);
  };

  const handleTogglePlay = async () => {
    if (currentTrack) {
      // Use mobile-specific play handler for better mobile browser compatibility
      await handleMobilePlay();
    }
  };

  const handleLike = async () => {
    if (!user || !currentTrack) return;
    
    try {
      if (isLiked) {
        await EngagementService.unlikeItem(currentTrack.id, 'track');
      } else {
        await EngagementService.likeItem(currentTrack.id, 'track');
      }
      setIsLiked(!isLiked);
    } catch (error) {
      console.error('Failed to toggle like:', error);
    }
  };

  const handleExpandPlayer = () => {
    setIsExpanded(true);
  };

  const handleCollapsePlayer = () => {
    setIsExpanded(false);
  };

  // Handle touch events for swipe gestures
  const handleTouchStart = (e: React.TouchEvent) => {
    if (!isExpanded) return;
    setIsDragging(true);
    setDragY(e.touches[0].clientY);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging || !isExpanded) return;
    const currentY = e.touches[0].clientY;
    const deltaY = currentY - dragY;

    // Only allow downward swipes
    if (deltaY > 0) {
      e.preventDefault();
    }
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    if (!isDragging || !isExpanded) return;
    setIsDragging(false);

    const currentY = e.changedTouches[0].clientY;
    const deltaY = currentY - dragY;

    // If swiped down more than 100px, collapse
    if (deltaY > 100) {
      handleCollapsePlayer();
    }
  };

  // Prevent body scroll when expanded
  useEffect(() => {
    if (isExpanded) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isExpanded]);

  // Don't render if no track is playing or on play page (unless expanded)
  if (!currentTrack || (isOnPlayPage && !isExpanded)) {
    console.log('MobilePlayer: Not rendering -', {
      hasTrack: !!currentTrack,
      isOnPlayPage,
      isExpanded,
      pathname: location.pathname
    });
    return null;
  }

  console.log('MobilePlayer: Rendering mini player -', {
    trackTitle: currentTrack?.title,
    isOnPlayPage,
    pathname: location.pathname
  });

  return (
    <>
      {/* Mini Player Bar - Positioned above mobile navigation */}
      <div className={cn(
        "fixed left-0 right-0 z-dropdown bg-background/95 backdrop-blur-md transition-transform duration-300",
        isExpanded ? "translate-y-full" : "translate-y-0",
        className
      )}
      style={{
        bottom: '72px' // Position above mobile navigation with less gap
      }}>
        <div className="px-4 py-3">
          <div className="relative bg-card/90 backdrop-blur-sm rounded-lg p-3 border border-border/10 shadow-sm overflow-hidden">
            <div className="flex items-center space-x-2">
              {/* Album Art */}
              <div className="w-8 h-8 bg-gradient-to-br from-primary/20 to-primary/40 rounded flex items-center justify-center flex-shrink-0 overflow-hidden">
                {currentTrack?.coverUrl ? (
                  <img
                    src={currentTrack.coverUrl}
                    alt={currentTrack.title}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <Play className="w-3 h-3 text-primary" />
                )}
              </div>

              {/* Track Info - Clickable to expand */}
              <div
                className="flex-1 min-w-0 cursor-pointer"
                onClick={handleExpandPlayer}
              >
                <p className="text-xs font-medium text-foreground truncate leading-tight">
                  {currentTrack.title}
                </p>
                <p className="text-[10px] text-foreground/60 truncate leading-tight">
                  {currentTrack.artist}
                </p>
              </div>

              {/* Play/Pause Button - Only control in mini player */}
              <Button
                variant="ghost"
                size="sm"
                onClick={handleTogglePlay}
                className="h-6 w-6 p-0 hover:bg-primary/10 flex-shrink-0"
              >
                {isPlaying ? (
                  <Pause className="w-3 h-3" />
                ) : (
                  <Play className="w-3 h-3" />
                )}
              </Button>
            </div>

            {/* Minimal Progress Bar */}
            <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-foreground/10">
              <div
                className="h-full bg-primary transition-all duration-300"
                style={{ width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%` }}
              />
            </div>
          </div>
      </div>
      </div>

      {/* Expanded Player Overlay */}
      {isExpanded && (
        <div
          className="fixed inset-0 z-modal bg-background animate-slide-up"
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        >
          {/* Full Play Mode Content with tabs - No header, clean fullscreen */}
          <div className="h-full overflow-hidden p-4">
            <PlayModeCard onCollapse={handleCollapsePlayer} isMobile={true} />
          </div>
        </div>
      )}
    </>
  );
};
