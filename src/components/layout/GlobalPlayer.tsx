import React, { useState, useEffect } from 'react';
import { Play, Pause, SkipBack, SkipForward, Volume2, Shuffle, Repeat, Repeat1, Heart, Bookmark } from 'lucide-react';
import { useMusicStore } from '../../store/musicStore';
import { useAudioPlayer } from '../../hooks/useAudioPlayer';
import { useAuth } from '../../hooks/useAuth';
import { EngagementService } from '../../services/engagementService';
import { Button } from '../atoms/Button';
import { Card } from '../atoms/Card';
import { cn } from '../../utils/cn';
import { useResponsive } from '../../hooks/useResponsive';
import { MobilePlayer } from './MobilePlayer';

export const GlobalPlayer: React.FC = () => {
  const { user } = useAuth();
  const { isMobile } = useResponsive();
  const {
    shuffle,
    repeat,
    currentAlbum,
    nextTrack,
    previousTrack,
    toggleShuffle,
    toggleRepeat,
    setVolume,
    updateTrackLikeCount
  } = useMusicStore();

  // Use mobile player on mobile devices
  if (isMobile) {
    return <MobilePlayer />;
  }

  // Engagement state
  const [isLiked, setIsLiked] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [engagementLoading, setEngagementLoading] = useState(false);

  // Use the audio player hook for actual playback
  const {
    currentTrack,
    isPlaying,
    volume,
    currentTime,
    duration,
    seekTo,
    togglePlay
  } = useAudioPlayer();

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Check engagement status when track changes
  useEffect(() => {
    const checkEngagementStatus = async () => {
      if (!currentTrack || !user) {
        setIsLiked(false);
        setIsSaved(false);
        return;
      }

      try {
        console.log('🔄 GlobalPlayer: Checking engagement status for track:', currentTrack?.title || 'undefined');
        const [likeStatus, saveStatus] = await Promise.all([
          EngagementService.getUserLike(currentTrack.id, 'track', user.id),
          EngagementService.getUserSave(currentTrack.id, 'track', user.id)
        ]);

        console.log('💚 GlobalPlayer: Like status for', currentTrack?.title || 'undefined', ':', !!likeStatus);
        setIsLiked(!!likeStatus);
        setIsSaved(!!saveStatus);
      } catch (error) {
        console.error('Failed to check engagement status:', error);
        // Set to false on error to avoid inconsistent state
        setIsLiked(false);
        setIsSaved(false);
      }
    };

    checkEngagementStatus();
  }, [currentTrack?.id, user?.id]);

  const handleTogglePlay = () => {
    if (!currentTrack) return;
    // Play tracking is now handled in useAudioPlayer hook when playback actually starts
    togglePlay();
  };

  const handleLike = async () => {
    if (!currentTrack || !user || engagementLoading) return;

    setEngagementLoading(true);
    try {
      if (isLiked) {
        await EngagementService.unlikeItem(currentTrack.id, 'track', user.id);
        setIsLiked(false);
        // Update local like count immediately
        updateTrackLikeCount(currentTrack.id, false);
      } else {
        await EngagementService.likeItem(currentTrack.id, 'track', user.id);
        setIsLiked(true);
        // Update local like count immediately
        updateTrackLikeCount(currentTrack.id, true);
      }
    } catch (error) {
      console.error('Failed to toggle like:', error);
      // Revert state on error
      setIsLiked(isLiked);
    } finally {
      setEngagementLoading(false);
    }
  };

  const handleSave = async () => {
    if (!currentTrack || !user || engagementLoading) return;

    setEngagementLoading(true);
    try {
      if (isSaved) {
        await EngagementService.unsaveItem(currentTrack.id, 'track', user.id);
        setIsSaved(false);
      } else {
        await EngagementService.saveItem(currentTrack.id, 'track', user.id);
        setIsSaved(true);
      }
    } catch (error) {
      console.error('Failed to toggle save:', error);
    } finally {
      setEngagementLoading(false);
    }
  };

  // Handle progress bar click
  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!duration) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const percentage = clickX / rect.width;
    const newTime = percentage * duration;

    seekTo(newTime);
  };

  // Handle volume bar click
  const handleVolumeClick = (e: React.MouseEvent<HTMLDivElement>) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const percentage = clickX / rect.width;
    const newVolume = Math.max(0, Math.min(1, percentage));

    setVolume(newVolume);
  };

  return (
    <footer className="fixed bottom-0 left-0 right-0 h-28 bg-background/80 backdrop-blur-md z-40" style={{ paddingLeft: '12px', paddingRight: '12px', paddingBottom: '12px' }}>
      <Card className="h-full" variant="glass">
        <div className="flex items-center justify-between h-full px-6">

          {/* Track Info - Show placeholder when no track */}
          <div className="flex items-center space-x-4 min-w-0 flex-1">
            <div className="w-16 h-16 bg-gradient-to-br from-purple-400 to-pink-400 rounded-2xl flex-shrink-0 overflow-hidden">
              {currentTrack?.coverUrl || currentAlbum?.coverUrl ? (
                <img
                  src={currentTrack?.coverUrl || currentAlbum?.coverUrl}
                  alt={currentTrack?.title || currentAlbum?.title}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <span className="text-white text-lg">🎵</span>
                </div>
              )}
            </div>
            <div className="min-w-0 flex-1">
              <h4 className="font-semibold text-foreground truncate text-[15px]">
                {currentTrack?.title || 'No track selected'}
              </h4>
              <p className="text-sm text-muted-foreground truncate">
                {currentTrack?.artist || 'Choose a song to play'}
              </p>
            </div>
          </div>

          {/* Player Controls - Wider progress bar like Spotify */}
          <div className="flex flex-col items-center space-y-3 flex-1 max-w-2xl py-4">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleShuffle}
                className={cn(
                  "h-9 w-9 p-0",
                  shuffle ? 'text-primary' : 'text-muted-foreground'
                )}
                title={shuffle ? 'Disable shuffle' : 'Enable shuffle'}
              >
                <Shuffle className="w-4 h-4" fill={shuffle ? 'currentColor' : 'none'} />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={previousTrack}
                className="h-9 w-9 p-0"
                title="Previous track (or restart current)"
              >
                <SkipBack className="w-4 h-4" fill="currentColor" />
              </Button>
              
              <button
                onClick={currentTrack ? handleTogglePlay : undefined}
                disabled={!currentTrack}
                title={currentTrack ? (isPlaying ? 'Pause' : 'Play') : 'No track selected'}
                className={cn(
                  "h-11 w-11 rounded-full flex items-center justify-center shadow-lg transition-all duration-200 border border-gray-200/50",
                  currentTrack
                    ? "bg-white hover:bg-gray-50 active:bg-gray-100 hover:shadow-xl hover:scale-105 active:scale-95 cursor-pointer"
                    : "bg-gray-300 cursor-not-allowed opacity-50"
                )}
              >
                {isPlaying ? (
                  <Pause className="w-4 h-4 ml-0 text-gray-900 dark:text-gray-900" fill="currentColor" />
                ) : (
                  <Play className="w-4 h-4 ml-1 text-gray-900 dark:text-gray-900" fill="currentColor" />
                )}
              </button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={nextTrack}
                className="h-9 w-9 p-0"
                title="Next track"
              >
                <SkipForward className="w-4 h-4" fill="currentColor" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleRepeat}
                className={cn(
                  "h-9 w-9 p-0 relative",
                  repeat !== 'none' ? 'text-primary' : 'text-muted-foreground'
                )}
                title={
                  repeat === 'none' ? 'Enable repeat' :
                  repeat === 'all' ? 'Repeat all' :
                  'Repeat one'
                }
              >
                {repeat === 'one' ? (
                  <Repeat1 className="w-4 h-4" fill="currentColor" />
                ) : (
                  <Repeat className="w-4 h-4" fill={repeat !== 'none' ? 'currentColor' : 'none'} />
                )}
              </Button>
            </div>
            
            {/* Progress Bar - Much wider like Spotify */}
            <div className="w-full max-w-xl px-2">
              <div className="flex items-center space-x-3">
                <span className="text-xs font-mono text-foreground/80 min-w-[40px] text-right">
                  {formatTime(currentTime || 0)}
                </span>

                <div
                  className="flex-1 group cursor-pointer"
                  onClick={currentTrack ? handleProgressClick : undefined}
                  title={currentTrack ? `${formatTime(currentTime || 0)} / ${formatTime(duration || 0)}` : 'No track playing'}
                >
                  {/* Outer container with padding for easier clicking */}
                  <div className="py-2">
                    {/* Visible track - much thicker and more prominent */}
                    <div className="relative h-2 bg-foreground/20 dark:bg-foreground/10 rounded-full overflow-hidden border border-foreground/10">
                      {/* Progress fill */}
                      <div
                        className="h-full bg-gradient-to-r from-primary to-primary/80 rounded-full transition-all duration-300 relative"
                        style={{ width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%` }}
                      >
                        {/* Glowing effect */}
                        <div className="absolute inset-0 bg-primary/50 rounded-full blur-sm opacity-60" />
                      </div>

                      {/* Hover handle */}
                      {currentTrack && (
                        <div
                          className="absolute top-1/2 -translate-y-1/2 w-4 h-4 bg-primary rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-all duration-200 border-2 border-background"
                          style={{ left: `calc(${duration > 0 ? (currentTime / duration) * 100 : 0}% - 8px)` }}
                        />
                      )}
                    </div>
                  </div>
                </div>

                <span className="text-xs font-mono text-foreground/80 min-w-[40px] text-left">
                  {formatTime(duration || 0)}
                </span>
              </div>
            </div>
          </div>

          {/* Right Side - Engagement + Volume Control */}
          <div className="flex items-center space-x-3 flex-1 justify-end">
            {/* Like Button */}
            {user && currentTrack && (
              <Button
                variant="ghost"
                size="sm"
                className="h-10 w-10 p-0"
                onClick={handleLike}
                disabled={engagementLoading}
                title={isLiked ? 'Unlike this track' : 'Like this track'}
              >
                <Heart
                  className={cn(
                    "w-4 h-4 transition-colors",
                    isLiked ? "text-red-500 fill-red-500" : "text-muted-foreground"
                  )}
                />
              </Button>
            )}

            {/* Save Button */}
            {user && currentTrack && (
              <Button
                variant="ghost"
                size="sm"
                className="h-10 w-10 p-0"
                onClick={handleSave}
                disabled={engagementLoading}
                title={isSaved ? 'Remove from saved' : 'Save this track'}
              >
                <Bookmark
                  className={cn(
                    "w-4 h-4 transition-colors",
                    isSaved ? "text-green-500 fill-green-500" : "text-muted-foreground"
                  )}
                />
              </Button>
            )}
            
            <Volume2 className="w-4 h-4 text-muted-foreground" />

            {/* Volume Bar - Consistent with progress bar */}
            <div
              className="w-24 group cursor-pointer"
              onClick={handleVolumeClick}
              title={`Volume: ${Math.round(volume * 100)}%`}
            >
              <div className="py-2">
                <div className="relative h-2 bg-foreground/20 dark:bg-foreground/10 rounded-full overflow-hidden border border-foreground/10">
                  <div
                    className="h-full bg-gradient-to-r from-primary to-primary/80 rounded-full transition-all duration-300 relative"
                    style={{ width: `${volume * 100}%` }}
                  >
                    <div className="absolute inset-0 bg-primary/50 rounded-full blur-sm opacity-60" />
                  </div>

                  <div
                    className="absolute top-1/2 -translate-y-1/2 w-3 h-3 bg-primary rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-all duration-200 border border-background"
                    style={{ left: `calc(${volume * 100}% - 6px)` }}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </footer>
  );
};