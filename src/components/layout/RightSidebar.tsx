import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { MessageCircle, X, Minimize2, Globe } from 'lucide-react';
import { cn } from '../../utils/cn';
import { Card } from '../atoms/Card';
import { Button } from '../atoms/Button';
import { useVibesStore } from '../../store/vibesStore';
import { TrackChat } from '../organisms/TrackChat';

export const RightSidebar: React.FC = () => {
  const location = useLocation();
  const { rightSidebarMinimized, setRightSidebarMinimized } = useVibesStore();
  const [userHasInteracted, setUserHasInteracted] = useState(false);

  // Determine if sidebar should be minimized based on current route and user preference
  const shouldBeMinimized = () => {
    const path = location.pathname;

    // If user has manually interacted, always respect their preference
    if (userHasInteracted) {
      return rightSidebarMinimized;
    }

    // Default behavior based on route
    if (path === '/') return true; // Home: minimized by default
    if (path === '/explore' || path === '/play' || path === '/chat') return false; // These pages: expanded by default

    // Other pages: use stored preference
    return rightSidebarMinimized;
  };

  const isCollapsed = shouldBeMinimized();

  // Handle user interactions
  const handleExpand = () => {
    setUserHasInteracted(true);
    setRightSidebarMinimized(false);
  };

  const handleCollapse = () => {
    setUserHasInteracted(true);
    setRightSidebarMinimized(true);
  };

  // Update CSS custom property when collapse state changes
  useEffect(() => {
    document.documentElement.style.setProperty(
      '--right-sidebar-width',
      isCollapsed ? '0px' : '360px'
    );
  }, [isCollapsed]);



  if (isCollapsed) {
    return (
      <div className="fixed right-8 top-32 z-30">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleExpand}
          className="h-12 w-12 p-0 rounded-full bg-background/80 backdrop-blur-md border border-border/20 shadow-lg hover:bg-background/90 transition-all duration-200 hover:scale-105 active:scale-95"
          title="Show sidebar"
        >
          <MessageCircle className="w-5 h-5" />
        </Button>
      </div>
    );
  }
  
  return (
    <aside
      className="fixed right-0 top-24 w-[360px] z-40 transition-all duration-300"
      style={{ 
        height: 'calc(100vh - 208px)', // Match main content height: 96px header + 112px player = 208px
        paddingRight: '12px',
        paddingTop: '12px',
        paddingBottom: '12px'
      }}
    >
      <div className="flex flex-col h-full">
        
        {/* Main Content Card */}
        <Card className="flex-1 overflow-hidden" variant="glass">
          <div className="flex flex-col h-full">
            
            {/* Header with Collapse Button */}
            <div className="flex-shrink-0 p-4 pb-0">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-base font-semibold text-foreground flex items-center space-x-2">
                  <MessageCircle className="w-4 h-4" />
                  <span>Track Chat</span>
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCollapse}
                  className="h-7 w-7 p-0 hover:bg-foreground/8"
                  title="Hide sidebar"
                >
                  <X className="w-3 h-3" />
                </Button>
              </div>
            </div>
            
            {/* Chat Content */}
            <div className="flex-1 overflow-hidden">
              <TrackChat className="h-full" />
            </div>
          </div>
        </Card>
      </div>
    </aside>
  );
};