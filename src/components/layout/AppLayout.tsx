import React, { useState } from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import { Header } from './Header';
import { LeftSidebar } from './LeftSidebar';
import { RightSidebar } from './RightSidebar';
import { GlobalPlayer } from './GlobalPlayer';
import { MobileNavigation } from './MobileNavigation';
import { useResponsive } from '../../hooks/useResponsive';
import { useMusicStore } from '../../store/musicStore';

interface AppLayoutProps {
  onProfileClick?: () => void;
  onSettingsClick?: () => void;
}

export const AppLayout: React.FC<AppLayoutProps> = ({ onProfileClick, onSettingsClick }) => {
  const { isMobile, isTablet, isDesktop } = useResponsive();
  const location = useLocation();
  const { currentTrack } = useMusicStore();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Check if mini player should be visible (has track and not on play page)
  const shouldShowMiniPlayer = isMobile && currentTrack && location.pathname !== '/play';

  const handleMobileMenuToggle = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const handleMobileAdminPanel = () => {
    if (onSettingsClick) {
      onSettingsClick();
    }
  };

  return (
    <div className="min-h-screen bg-background flex flex-col safe-area-inset">
      {/* Fixed Header - Responsive, hidden on mobile Play page */}
      {!(isMobile && location.pathname === '/play') && (
        <Header
          onMobileMenuToggle={handleMobileMenuToggle}
          isMobileMenuOpen={isMobileMenuOpen}
        />
      )}

      {/* Main Content Area - Responsive Layout */}
      <div className={`flex-1 flex overflow-hidden ${
        isMobile && location.pathname === '/play'
          ? 'pt-0' // No padding for mobile Play mode (fullscreen)
          : 'pt-16 sm:pt-24' // Original padding for other pages
      }`}>
        {/* Left Sidebar - Hidden on mobile, collapsible on tablet */}
        {!isMobile && (
          <LeftSidebar onProfileClick={onProfileClick} onSettingsClick={onSettingsClick} />
        )}

        {/* Center Content - Responsive margins and padding */}
        <main className={`
          flex-1 overflow-y-auto overflow-x-visible transition-all duration-300
          ${isMobile ?
            // Mobile: Special handling for Play mode - no top padding, minimal side padding
            location.pathname === '/play' ? 'px-2 pt-0 pb-3' : 'px-4 pt-0 pb-3' :
            isTablet ?
              // Tablet: Reduced margins, responsive to sidebar states
              'px-3 py-3' :
              // Desktop: Original layout with dynamic margins
              'px-3 py-3'
          }
        `}
        style={!isMobile ? {
          marginLeft: 'var(--left-sidebar-width, 280px)',
          marginRight: 'var(--right-sidebar-width, 360px)',
          height: 'calc(100vh - 208px)', // 96px header + 112px player = 208px total
        } : {
          height: location.pathname === '/play'
            ? '100vh' // Full height for mobile Play page (no header, no nav)
            : shouldShowMiniPlayer
              ? 'calc(100vh - 178px)' // Header (98px) + Mini Player (80px) = 178px
              : 'calc(100vh - 98px)', // Just header and mobile nav
        }}>
          <div className="h-full overflow-visible">
            <Outlet />
          </div>
        </main>

        {/* Right Sidebar - Hidden on mobile and tablet */}
        {isDesktop && <RightSidebar />}
      </div>

      {/* Fixed Global Player - Responsive */}
      <GlobalPlayer />

      {/* Mobile Navigation - Only on mobile and not on Play page */}
      {isMobile && location.pathname !== '/play' && (
        <MobileNavigation
          isMenuOpen={isMobileMenuOpen}
          onMenuToggle={handleMobileMenuToggle}
          onAdminPanelOpen={handleMobileAdminPanel}
        />
      )}
    </div>
  );
};