import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import {
  Home,
  Compass,
  Library,
  MessageCircle,
  User,
  UserPlus,
  ChevronLeft,
  ChevronRight,
  Heart,
  LogIn,
  ArrowLeft,
  TrendingUp,
  Music,
  Disc,
  Play,
  Loader2,
  Plus,
  Share,
  Link as LinkIcon,
  MoreHorizontal
} from 'lucide-react';
import { cn } from '../../utils/cn';
import { Card } from '../atoms/Card';
import { Button } from '../atoms/Button';
import { AuthModal } from '../auth/AuthModal';
import { CreatePlaylistModal } from '../playlist/CreatePlaylistModal';
import { ProfileDropdown } from '../molecules/ProfileDropdown';

import { useAuth } from '../../hooks/useAuth';
import { useMusicStore } from '../../store/musicStore';
import { EngagementService } from '../../services/engagementService';
import { ShareService } from '../../services/shareService';
import { AlbumService } from '../../services/albumService';
import { MusicService } from '../../services/musicService';
import { playModeContextActions } from '../../hooks/usePlayModeContext';
import { OverflowMenu } from '../atoms/OverflowMenu';

interface LeftSidebarProps {
  onProfileClick?: () => void;
  onSettingsClick?: () => void;
}

interface NavigationItem {
  icon: any;
  label: string;
  path?: string;
  scrollTo?: string;
  action?: () => void;
  active?: boolean;
}

export const LeftSidebar: React.FC<LeftSidebarProps> = ({ onProfileClick, onSettingsClick }) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [authModal, setAuthModal] = useState<{ isOpen: boolean; mode: 'login' | 'signup' }>({
    isOpen: false,
    mode: 'login'
  });
  const [createPlaylistModal, setCreatePlaylistModal] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { currentAlbum, setCurrentAlbum, setCurrentTrack, setQueue, setIsPlaying } = useMusicStore();

  // Function to scroll to section
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleAuthModalOpen = (mode: 'login' | 'signup') => {
    setAuthModal({ isOpen: true, mode });
  };

  const handleAuthModalClose = () => {
    setAuthModal({ isOpen: false, mode: 'login' });
  };

  // Update CSS custom property when collapse state changes
  useEffect(() => {
    document.documentElement.style.setProperty(
      '--left-sidebar-width', 
      isCollapsed ? '80px' : '280px'
    );
  }, [isCollapsed]);

  // Navigation items based on current route
  const getNavigationItems = (): NavigationItem[] => {
    const currentPath = location.pathname;

    if (currentPath === '/explore') {
      return [
        { icon: ArrowLeft, label: 'Back', path: '/', active: false },
        { icon: TrendingUp, label: 'Trending', scrollTo: 'trending-now' },
        { icon: Disc, label: 'Albums', scrollTo: 'featured-albums' },
        { icon: Music, label: 'Singles', scrollTo: 'featured-singles' },
        { icon: User, label: 'Artists', scrollTo: 'artists' },
      ];
    }

    if (currentPath === '/chat') {
      return [
        { icon: Home, label: 'Home', path: '/', active: false },
        { icon: MessageCircle, label: 'Messages', scrollTo: 'messages', active: true },
        { icon: UserPlus, label: 'Friends', scrollTo: 'friends' },
        { icon: User, label: 'Profile', scrollTo: 'profile' },
      ];
    }

    if (currentPath === '/library') {
      return [
        { icon: Home, label: 'Home', path: '/', active: false },
        { icon: Library, label: 'Library', path: '/library', active: true },
        { icon: Heart, label: 'Liked Songs', scrollTo: 'liked-songs' },
        { icon: User, label: 'Playlists', scrollTo: 'your-playlists' },
      ];
    }

    // Default/Home navigation
    return [
      { icon: Home, label: 'Home', path: '/', active: currentPath === '/' },
      { icon: Compass, label: 'Discover', path: '/explore', active: currentPath === '/explore' },
      { icon: Library, label: 'Library', path: '/library', active: currentPath === '/library' },
      { icon: MessageCircle, label: 'Chat', path: '/chat', active: currentPath === '/chat' },
      { icon: User, label: 'Profile', path: '/profile', active: currentPath === '/profile' },
      { icon: UserPlus, label: 'Invite', path: '/invite', active: currentPath === '/invite' },
    ];
  };

  // Real library data state
  const [recentAlbums, setRecentAlbums] = useState<any[]>([]);
  const [userPlaylists, setUserPlaylists] = useState<any[]>([]);
  const [likedSongsCount, setLikedSongsCount] = useState(0);
  const [libraryLoading, setLibraryLoading] = useState(false);

  // Play button loading states
  const [playingAlbumId, setPlayingAlbumId] = useState<string | null>(null);
  const [playingLikedSongs, setPlayingLikedSongs] = useState(false);

  // Load user library data when in play mode and user is authenticated
  useEffect(() => {
    if (location.pathname === '/play' && user && !isCollapsed) {
      loadUserLibrary();
    }
  }, [location.pathname, user, isCollapsed]);

  const loadUserLibrary = async () => {
    if (libraryLoading || !user) return;

    setLibraryLoading(true);
    try {
      // Load recent albums from play events
      const recentAlbumIds = await EngagementService.getUserRecentAlbums(user.id, 5);

      if (recentAlbumIds.length > 0) {
        // Fetch album details using the correct method
        const albumPromises = recentAlbumIds.map(async (albumId) => {
          try {
            const result = await AlbumService.getAlbumWithTracks(albumId);
            return result ? result.album : null;
          } catch (error) {
            console.error(`Failed to fetch album ${albumId}:`, error);
            return null;
          }
        });
        const albums = await Promise.all(albumPromises);
        const validAlbums = albums.filter(album => album !== null);
        setRecentAlbums(validAlbums);
      } else {
        setRecentAlbums([]);
      }

      // Load liked songs count (only count tracks that actually exist)
      const likedTrackIds = await EngagementService.getUserLikedTracks(user.id);
      if (likedTrackIds.length > 0) {
        // Verify which tracks actually exist to get accurate count
        const trackPromises = likedTrackIds.map(async (trackId) => {
          try {
            const track = await MusicService.getTrackById(trackId);
            return track !== null;
          } catch (error) {
            return false;
          }
        });
        const trackExistsResults = await Promise.all(trackPromises);
        const actualLikedCount = trackExistsResults.filter(exists => exists).length;
        setLikedSongsCount(actualLikedCount);
        console.log(`💚 Sidebar: ${actualLikedCount} of ${likedTrackIds.length} liked tracks exist`);
      } else {
        setLikedSongsCount(0);
      }

      // Load user playlists
      const playlists = await MusicService.getPlaylistsByUser(user.id);
      const playlistsWithTrackCount = playlists.map(playlist => ({
        ...playlist,
        title: playlist.name, // Map name to title for consistency
        trackCount: playlist.tracks.length
      }));
      setUserPlaylists(playlistsWithTrackCount);
    } catch (error) {
      console.error('Failed to load user library:', error);
      // Set empty arrays on error
      setRecentAlbums([]);
      setUserPlaylists([]);
      setLikedSongsCount(0);
    } finally {
      setLibraryLoading(false);
    }
  };

  // Play album function
  const handlePlayAlbum = async (albumId: string) => {
    if (!user || playingAlbumId === albumId) return;

    setPlayingAlbumId(albumId);
    try {
      console.log('🎵 Playing album from library:', albumId);

      // Get album with first track for instant playback
      const result = await AlbumService.getAlbumWithFirstTrack(albumId);
      if (!result || !result.firstTrack) {
        throw new Error('Album or first track not found');
      }

      const { album, firstTrack, remainingTrackIds } = result;

      // Set current album and track
      setCurrentAlbum(album);
      setCurrentTrack(firstTrack);

      // Load remaining tracks in background
      if (remainingTrackIds.length > 0) {
        AlbumService.getRemainingTracks(remainingTrackIds).then((remainingTracks) => {
          const fullQueue = [firstTrack, ...remainingTracks];
          setQueue(fullQueue);
          console.log(`🎵 Loaded ${fullQueue.length} tracks for album: ${album.title}`);
        }).catch((error) => {
          console.error('Failed to load remaining tracks:', error);
          // Still play the first track even if remaining tracks fail
          setQueue([firstTrack]);
        });
      } else {
        setQueue([firstTrack]);
      }

      // Start playing
      setIsPlaying(true);

      // Track play event
      if (user) {
        EngagementService.trackPlay(firstTrack.id, album.id, user.id);
      }

      console.log('✅ Album playback started:', album.title);
    } catch (error) {
      console.error('Failed to play album:', error);
    } finally {
      setPlayingAlbumId(null);
    }
  };

  // Play liked songs function
  const handlePlayLikedSongs = async () => {
    if (!user || playingLikedSongs) return;

    setPlayingLikedSongs(true);
    try {
      console.log('💚 Playing liked songs');

      // Get user's liked track IDs
      const likedTrackIds = await EngagementService.getUserLikedTracks(user.id);
      if (likedTrackIds.length === 0) {
        console.log('No liked songs found');
        return;
      }

      // Load first track immediately
      const firstTrackId = likedTrackIds[0];
      const firstTrackResult = await MusicService.getTrackById(firstTrackId);
      if (!firstTrackResult) {
        throw new Error('First liked track not found');
      }

      // Set current track (no album for liked songs playlist)
      setCurrentAlbum(null);
      setCurrentTrack(firstTrackResult);

      // Load remaining tracks in background
      if (likedTrackIds.length > 1) {
        const remainingTrackIds = likedTrackIds.slice(1);
        Promise.all(
          remainingTrackIds.map(async (trackId) => {
            try {
              return await MusicService.getTrackById(trackId);
            } catch (error) {
              console.error(`Failed to load track ${trackId}:`, error);
              return null;
            }
          })
        ).then((remainingTracks) => {
          const validTracks = remainingTracks.filter(track => track !== null);
          const fullQueue = [firstTrackResult, ...validTracks];
          setQueue(fullQueue);
          console.log(`💚 Loaded ${fullQueue.length} liked songs`);
        }).catch((error) => {
          console.error('Failed to load remaining liked songs:', error);
          setQueue([firstTrackResult]);
        });
      } else {
        setQueue([firstTrackResult]);
      }

      // Start playing
      setIsPlaying(true);

      // Track play event
      if (user) {
        EngagementService.trackPlay(firstTrackResult.id, undefined, user.id);
      }

      console.log('✅ Liked songs playback started');
    } catch (error) {
      console.error('Failed to play liked songs:', error);
    } finally {
      setPlayingLikedSongs(false);
    }
  };

  // Share liked songs function
  const handleShareLikedSongs = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!user || likedSongsCount === 0) return;

    try {
      const success = await ShareService.shareLikedSongs();
      if (success) {
        console.log('✅ Liked songs shared successfully');
      } else {
        console.error('❌ Failed to share liked songs');
      }
    } catch (error) {
      console.error('Error sharing liked songs:', error);
    }
  };

  // Copy liked songs link function
  const handleCopyLikedSongsLink = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!user || likedSongsCount === 0) return;

    try {
      const success = await ShareService.copyLikedSongsLink();
      if (success) {
        console.log('✅ Liked songs link copied successfully');
      } else {
        console.error('❌ Failed to copy liked songs link');
      }
    } catch (error) {
      console.error('Error copying liked songs link:', error);
    }
  };

  const navigationItems = getNavigationItems();

  return (
    <aside className={cn(
      'fixed left-0 top-24 z-50 transition-all duration-300',
      isCollapsed ? 'w-20' : 'w-[280px]'
    )} style={{ 
      height: 'calc(100vh - 208px)', // Match main content height: 96px header + 112px player = 208px
      paddingLeft: '12px',
      paddingTop: '12px',
      paddingBottom: '12px'
    }}>
      <div className="flex flex-col h-full">
        
        {/* Single Card: Dynamic Content with Profile Footer */}
        <Card className="flex-1 flex flex-col" variant="glass">
          {/* Main Content Area */}
          <div className="flex-1 p-4 overflow-y-auto">
            {/* Play Mode - Library Content */}
            {location.pathname === '/play' && (
              <div className="space-y-6">
                {/* Header with Create and Collapse/Expand Buttons */}
                <div className="flex items-center justify-between">
                  {!isCollapsed && (
                    <h3 className="text-sm font-semibold text-foreground/80">Your Library</h3>
                  )}
                  <div className="flex items-center space-x-2">
                    {/* Create Button - only show if user is signed in and sidebar is expanded */}
                    {user && !isCollapsed && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setCreatePlaylistModal(true)}
                        className="h-8 w-8 p-0 hover:bg-foreground/8"
                        title="Create playlist"
                      >
                        <Plus className="w-4 h-4" />
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setIsCollapsed(!isCollapsed)}
                      className="h-8 w-8 p-0 hover:bg-foreground/8"
                      title={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
                    >
                      {isCollapsed ? (
                        <ChevronRight className="w-4 h-4" />
                      ) : (
                        <ChevronLeft className="w-4 h-4" />
                      )}
                    </Button>
                  </div>
                </div>

                {/* Current Album Display - Simple thumbnail view like Recent Albums */}
                {!isCollapsed && currentAlbum && (
                  <div className="space-y-3">
                    <h4 className="text-xs font-semibold text-foreground/60 uppercase tracking-wider">
                      Now Playing Album
                    </h4>
                    <div className="flex items-center space-x-3 p-2 rounded-2xl bg-foreground/5">
                      <div className="w-12 h-12 rounded-xl overflow-hidden bg-gradient-to-br from-purple-400 to-pink-400">
                        {currentAlbum.coverUrl ? (
                          <img src={currentAlbum.coverUrl} alt={currentAlbum.title} className="w-full h-full object-cover" />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <Disc className="w-6 h-6 text-white" />
                          </div>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-foreground truncate">{currentAlbum.title}</p>
                        <p className="text-xs text-muted-foreground truncate">{currentAlbum.artist}</p>
                        <p className="text-xs text-muted-foreground">{currentAlbum.trackCount || 0} tracks</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Quick Access - Only show if user is signed in */}
                {user && (
                  <div className="space-y-2">
                    <div
                      className="flex items-center space-x-3 p-2 rounded-2xl hover:bg-foreground/5 transition-colors cursor-pointer group"
                      onClick={() => {
                        // Activate the Liked Songs contextual tab
                        playModeContextActions.showLikedSongs();
                      }}
                    >
                      {/* Liked Songs Icon with Play Button Overlay */}
                      <div className="relative w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
                        <Heart className="w-5 h-5 text-white" />

                        {/* Play Button Overlay - only show if user has liked songs */}
                        {likedSongsCount > 0 && (
                          <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center rounded-xl">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handlePlayLikedSongs();
                              }}
                              disabled={playingLikedSongs}
                              className="w-6 h-6 bg-white rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-200 shadow-lg"
                              title="Play Liked Songs"
                            >
                              {playingLikedSongs ? (
                                <Loader2 className="w-3 h-3 text-black animate-spin" />
                              ) : (
                                <Play className="w-3 h-3 text-black ml-0.5" />
                              )}
                            </button>
                          </div>
                        )}
                      </div>

                      {!isCollapsed && (
                        <div className="flex-1 flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-foreground">Liked Songs</p>
                            <p className="text-xs text-muted-foreground">
                              {likedSongsCount > 0 ? `${likedSongsCount} songs` : 'No liked songs yet'}
                            </p>
                          </div>

                          {/* Share Options Menu - only show if user has liked songs */}
                          {likedSongsCount > 0 && (
                            <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                              <OverflowMenu
                                items={[
                                  {
                                    id: 'share',
                                    label: 'Share',
                                    icon: Share,
                                    submenu: [
                                      {
                                        id: 'share-native',
                                        label: 'Share via...',
                                        icon: Share,
                                        onClick: handleShareLikedSongs
                                      },
                                      {
                                        id: 'copy-link',
                                        label: 'Copy link to liked songs',
                                        icon: LinkIcon,
                                        onClick: handleCopyLikedSongsLink
                                      }
                                    ]
                                  }
                                ]}
                                placement="bottom-left"
                                buttonClassName="p-1 hover:bg-white/10 rounded-lg"
                                buttonContent={<MoreHorizontal className="w-3 h-3 text-white/80 hover:text-white" />}
                                title="Share options"
                              />
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Recently Played Albums - Only show if user is signed in */}
                {user && !isCollapsed && (
                  <div className="space-y-3">
                    <h4 className="text-xs font-semibold text-foreground/60 uppercase tracking-wider">
                      Recent Albums
                    </h4>
                    {recentAlbums.length > 0 ? (
                      <div className="space-y-2">
                        {recentAlbums.map((album) => (
                          <div
                            key={album.id}
                            className="flex items-center space-x-3 p-2 rounded-2xl hover:bg-foreground/5 transition-colors cursor-pointer group"
                            onClick={() => {
                              // Activate the Album contextual tab
                              playModeContextActions.showAlbum(album.title, album);
                            }}
                          >
                            {/* Album Cover with Play Button Overlay */}
                            <div className="relative w-10 h-10 rounded-xl overflow-hidden bg-gradient-to-br from-purple-400 to-pink-400">
                              {album.coverUrl ? (
                                <img src={album.coverUrl} alt={album.title} className="w-full h-full object-cover" />
                              ) : (
                                <div className="w-full h-full flex items-center justify-center">
                                  <Disc className="w-5 h-5 text-white" />
                                </div>
                              )}

                              {/* Play Button Overlay */}
                              <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handlePlayAlbum(album.id);
                                  }}
                                  disabled={playingAlbumId === album.id}
                                  className="w-6 h-6 bg-white rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-200 shadow-lg"
                                  title={`Play ${album.title}`}
                                >
                                  {playingAlbumId === album.id ? (
                                    <Loader2 className="w-3 h-3 text-black animate-spin" />
                                  ) : (
                                    <Play className="w-3 h-3 text-black ml-0.5" />
                                  )}
                                </button>
                              </div>
                            </div>

                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium text-foreground truncate">{album.title}</p>
                              <p className="text-xs text-muted-foreground truncate">{album.artist}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-4">
                        <p className="text-xs text-muted-foreground">No recent albums</p>
                      </div>
                    )}
                  </div>
                )}

                {/* User Playlists - Only show if user is signed in */}
                {user && !isCollapsed && (
                  <div className="space-y-3">
                    <h4 className="text-xs font-semibold text-foreground/60 uppercase tracking-wider">
                      Your Playlists
                    </h4>
                    {userPlaylists.length > 0 ? (
                      <div className="space-y-2">
                        {userPlaylists.map((playlist) => (
                          <div
                            key={playlist.id}
                            onClick={() => {
                              // Activate the Playlist contextual tab
                              playModeContextActions.showPlaylist(playlist.title, playlist);
                            }}
                            className="flex items-center space-x-3 p-2 rounded-2xl hover:bg-foreground/5 transition-colors cursor-pointer group"
                          >
                            {/* Playlist Cover with Play Button Overlay */}
                            <div className="relative w-10 h-10 rounded-xl overflow-hidden bg-gradient-to-br from-blue-400 to-purple-500">
                              {playlist.coverUrl ? (
                                <img src={playlist.coverUrl} alt={playlist.title} className="w-full h-full object-cover" />
                              ) : (
                                <div className="w-full h-full flex items-center justify-center">
                                  <Music className="w-5 h-5 text-white" />
                                </div>
                              )}

                              {/* Play Button Overlay - only show if playlist has tracks */}
                              {playlist.trackCount > 0 && (
                                <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center rounded-xl">
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      // TODO: Implement playlist play functionality
                                      console.log('Play playlist:', playlist.id);
                                    }}
                                    className="w-6 h-6 bg-white rounded-full flex items-center justify-center hover:scale-110 transition-transform duration-200 shadow-lg"
                                    title={`Play ${playlist.title}`}
                                  >
                                    <Play className="w-3 h-3 text-black ml-0.5" />
                                  </button>
                                </div>
                              )}
                            </div>

                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium text-foreground truncate">{playlist.title}</p>
                              <p className="text-xs text-muted-foreground truncate">{playlist.trackCount || 0} songs</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-4">
                        <p className="text-xs text-muted-foreground">No playlists yet</p>
                        {user && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setCreatePlaylistModal(true)}
                            className="mt-2 text-xs"
                          >
                            Create your first playlist
                          </Button>
                        )}
                      </div>
                    )}
                  </div>
                )}

                {/* Welcome message for authenticated users in Play Mode - only when no current album */}
                {user && !isCollapsed && !currentAlbum && (
                  <div className="text-center space-y-3 py-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl mx-auto flex items-center justify-center">
                      <Heart className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h4 className="text-sm font-semibold text-foreground mb-1">
                        Welcome back, {user.displayName?.split(' ')[0] || user.email?.split('@')[0] || 'User'}!
                      </h4>
                      <p className="text-xs text-muted-foreground">Your music library is ready</p>
                    </div>
                  </div>
                )}

                {/* Sign in prompt for Play Mode when not authenticated */}
                {!user && !isCollapsed && (
                  <div className="text-center space-y-3 py-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl mx-auto flex items-center justify-center">
                      <LogIn className="w-6 h-6 text-white" />
                    </div>
                    <div className="space-y-3">
                      <div>
                        <h4 className="text-sm font-semibold text-foreground mb-1">Sign in to access your library</h4>
                        <p className="text-xs text-muted-foreground">Create playlists, save tracks, and more</p>
                      </div>
                      <Button
                        variant="primary"
                        size="sm"
                        onClick={() => handleAuthModalOpen('login')}
                        className="w-full"
                      >
                        Sign In
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Navigation Menu - All modes except Play */}
            {location.pathname !== '/play' && (
              <div className="space-y-2">
                {/* Collapse/Expand Button - Above navigation when collapsed */}
                {isCollapsed && (
                  <div className="flex justify-center mb-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setIsCollapsed(!isCollapsed)}
                      className="h-8 w-8 p-0 hover:bg-foreground/8"
                      title="Expand sidebar"
                    >
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  </div>
                )}

                {/* Navigation Menu */}
                <nav className="space-y-2">
                  {navigationItems.map((item, index) => {
                    const Icon = item.icon;
                    const isFirstItem = index === 0;

                    return (
                      <div key={item.path || item.scrollTo || index} className={cn(
                        "flex items-center",
                        isFirstItem && !isCollapsed ? "justify-between" : ""
                      )}>
                        {item.path ? (
                          <Link
                            to={item.path}
                            className={cn(
                              'flex items-center rounded-[20px] px-3 py-2 text-left transition-all duration-200',
                              'hover:bg-foreground/5 text-[15px] font-medium',
                              item.active
                                ? 'bg-foreground/[0.06] dark:bg-secondary/40 text-foreground shadow-sm'
                                : 'text-foreground/70 hover:text-foreground',
                              isCollapsed ? 'justify-center w-full' : 'flex-1',
                              isFirstItem && !isCollapsed ? 'flex-1' : 'w-full'
                            )}
                            title={isCollapsed ? item.label : undefined}
                          >
                            <Icon className={cn(
                              "w-5 h-5 flex-shrink-0 transition-colors duration-200",
                              item.active && "text-primary"
                            )} />
                            {!isCollapsed && (
                              <span className="ml-3">{item.label}</span>
                            )}
                            {/* Active indicator */}
                            {item.active && !isCollapsed && (
                              <div className="ml-auto w-1.5 h-1.5 rounded-full bg-primary" />
                            )}
                          </Link>
                        ) : item.action ? (
                          <button
                            onClick={item.action}
                            className={cn(
                              'flex items-center rounded-[20px] px-3 py-2 text-left transition-all duration-200',
                              'hover:bg-foreground/5 text-[15px] font-medium',
                              item.active
                                ? 'bg-foreground/[0.06] dark:bg-secondary/40 text-foreground shadow-sm'
                                : 'text-foreground/70 hover:text-foreground',
                              isCollapsed ? 'justify-center w-full' : 'flex-1',
                              isFirstItem && !isCollapsed ? 'flex-1' : 'w-full'
                            )}
                            title={isCollapsed ? item.label : undefined}
                          >
                            <Icon className={cn(
                              "w-5 h-5 flex-shrink-0 transition-colors duration-200",
                              item.active && "text-primary"
                            )} />
                            {!isCollapsed && (
                              <span className="ml-3">{item.label}</span>
                            )}
                            {/* Active indicator */}
                            {item.active && !isCollapsed && (
                              <div className="ml-auto w-1.5 h-1.5 rounded-full bg-primary" />
                            )}
                          </button>
                        ) : (
                          <button
                            onClick={() => item.scrollTo && scrollToSection(item.scrollTo)}
                            className={cn(
                              'flex items-center rounded-[20px] px-3 py-2 text-left transition-all duration-200',
                              'hover:bg-foreground/5 text-[15px] font-medium',
                              item.active
                                ? 'bg-foreground/[0.06] dark:bg-secondary/40 text-foreground shadow-sm'
                                : 'text-foreground/70 hover:text-foreground',
                              isCollapsed ? 'justify-center w-full' : 'flex-1',
                              isFirstItem && !isCollapsed ? 'flex-1' : 'w-full'
                            )}
                            title={isCollapsed ? item.label : undefined}
                          >
                            <Icon className={cn(
                              "w-5 h-5 flex-shrink-0 transition-colors duration-200",
                              item.active && "text-primary"
                            )} />
                            {!isCollapsed && (
                              <span className="ml-3">{item.label}</span>
                            )}
                            {/* Active indicator */}
                            {item.active && !isCollapsed && (
                              <div className="ml-auto w-1.5 h-1.5 rounded-full bg-primary" />
                            )}
                          </button>
                        )}
                        
                        {/* Collapse/Expand Button inline with Home - only when expanded */}
                        {isFirstItem && !isCollapsed && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setIsCollapsed(!isCollapsed)}
                            className="h-8 w-8 p-0 hover:bg-foreground/8 ml-2"
                            title="Collapse sidebar"
                          >
                            <ChevronLeft className="w-4 h-4" />
                          </Button>
                        )}
                      </div>
                    );
                  })}
                </nav>

                {/* Browse navigation - show in explore mode */}
                {location.pathname === '/explore' && !isCollapsed && (
                  <div className="mt-6 pt-4 border-t border-border/10">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <h4 className="text-xs font-semibold text-foreground/60 uppercase tracking-wider">
                          Browse
                        </h4>
                        <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
                      </div>
                      <div className="space-y-1">
                        <button
                          onClick={() => scrollToSection('new-releases')}
                          className="w-full text-left px-3 py-2 text-sm text-foreground/70 hover:text-foreground hover:bg-foreground/5 rounded-2xl transition-colors"
                        >
                          New Releases
                        </button>
                        <button
                          onClick={() => scrollToSection('genres-moods')}
                          className="w-full text-left px-3 py-2 text-sm text-foreground/70 hover:text-foreground hover:bg-foreground/5 rounded-2xl transition-colors"
                        >
                          Genres & Moods
                        </button>
                      </div>
                    </div>
                  </div>
                )}

              </div>
            )}
          </div>

          {/* Profile Footer - Always at bottom of the card */}
          <div className="flex-shrink-0 p-4">
            {user ? (
              <ProfileDropdown
                user={user}
                isCollapsed={isCollapsed}
                onProfileClick={onProfileClick}
                onSettingsClick={onSettingsClick}
              />
            ) : (
              <button
                onClick={() => handleAuthModalOpen('login')}
                className={cn(
                  "w-full flex items-center space-x-3 p-3 rounded-2xl bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/20",
                  "hover:from-purple-500/20 hover:to-pink-500/20 hover:border-purple-500/30 transition-all duration-200",
                  "focus:outline-none focus:ring-2 focus:ring-purple-500/20",
                  isCollapsed ? "justify-center" : ""
                )}
              >
                <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center flex-shrink-0">
                  <LogIn className="w-5 h-5 text-white" />
                </div>
                {!isCollapsed && (
                  <div className="flex-1 min-w-0 text-left">
                    <p className="text-sm font-medium text-foreground">
                      Sign in to Vibe!
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Personalise, Save & Share
                    </p>
                  </div>
                )}
              </button>
            )}
          </div>
        </Card>
      </div>

      {/* Auth Modal */}
      <AuthModal
        isOpen={authModal.isOpen}
        onClose={handleAuthModalClose}
        mode={authModal.mode}
        onModeChange={(mode) => setAuthModal(prev => ({ ...prev, mode }))}
      />

      {/* Create Playlist Modal */}
      <CreatePlaylistModal
        isOpen={createPlaylistModal}
        onClose={() => setCreatePlaylistModal(false)}
        onPlaylistCreated={(playlistId) => {
          // Refresh library data to show new playlist
          if (user) {
            loadUserLibrary();
          }
          // Navigate to the new playlist
          navigate(`/playlist/${playlistId}`);
        }}
      />
    </aside>
  );
};