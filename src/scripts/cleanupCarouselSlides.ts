import { collection, getDocs, doc, deleteDoc, query, where } from 'firebase/firestore';
import { db } from '../lib/firebase';
import { CarouselSlide } from '../types';

/**
 * <PERSON><PERSON>t to clean up unwanted hardcoded carousel slides from the database
 * This removes the "Plead Album" and "VibeCode Album" slides that were created by the seeding script
 */
export class CarouselCleanupService {
  
  /**
   * Remove hardcoded carousel slides (Plead Album, VibeCode Album) from database
   */
  static async cleanupHardcodedSlides(): Promise<void> {
    try {
      console.log('🧹 Starting cleanup of hardcoded carousel slides...');
      
      // Get all carousel slides
      const slidesSnapshot = await getDocs(collection(db, 'carouselSlides'));
      const slides = slidesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as CarouselSlide[];
      
      console.log(`🔍 Found ${slides.length} carousel slides to check`);
      
      let deletedCount = 0;
      
      for (const slide of slides) {
        // Check if this is a hardcoded slide we want to remove
        const isHardcodedSlide = (
          slide.title === 'Plead Album' ||
          slide.title === 'VibeCode Album' ||
          slide.subtitle === 'Probe x Bolt.New' ||
          slide.subtitle === 'AiProbe.co x Bolt.New' ||
          slide.createdBy === 'system'
        );
        
        if (isHardcodedSlide) {
          console.log(`🗑️ Deleting hardcoded slide: "${slide.title}" (${slide.subtitle})`);
          
          await deleteDoc(doc(db, 'carouselSlides', slide.id));
          deletedCount++;
        } else {
          console.log(`✅ Keeping real slide: "${slide.title}" (${slide.subtitle})`);
        }
      }
      
      console.log(`✅ Cleanup completed. Deleted ${deletedCount} hardcoded slides.`);
      
      if (deletedCount > 0) {
        console.log('🎉 Your carousel will now only show real slides from your database!');
      }
      
    } catch (error) {
      console.error('❌ Failed to cleanup carousel slides:', error);
      throw error;
    }
  }
  
  /**
   * List all carousel slides for debugging
   */
  static async debugListAllSlides(): Promise<void> {
    try {
      const slidesSnapshot = await getDocs(collection(db, 'carouselSlides'));
      const slides = slidesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as CarouselSlide[];
      
      console.log('🎠 DEBUG: All carousel slides in database:');
      console.table(slides.map(slide => ({
        id: slide.id,
        title: slide.title,
        subtitle: slide.subtitle,
        isActive: slide.isActive,
        order: slide.order,
        createdBy: slide.createdBy,
        albumId: slide.albumId
      })));
      
    } catch (error) {
      console.error('❌ Failed to list slides:', error);
    }
  }
  
  /**
   * Remove slides that have no album linked (cleanup orphaned slides)
   */
  static async cleanupOrphanedSlides(): Promise<void> {
    try {
      console.log('🧹 Starting cleanup of orphaned carousel slides...');
      
      const slidesSnapshot = await getDocs(collection(db, 'carouselSlides'));
      const slides = slidesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as CarouselSlide[];
      
      let deletedCount = 0;
      
      for (const slide of slides) {
        if (!slide.albumId) {
          console.log(`🗑️ Deleting orphaned slide: "${slide.title}" (no album linked)`);
          await deleteDoc(doc(db, 'carouselSlides', slide.id));
          deletedCount++;
        }
      }
      
      console.log(`✅ Orphaned slides cleanup completed. Deleted ${deletedCount} slides.`);
      
    } catch (error) {
      console.error('❌ Failed to cleanup orphaned slides:', error);
      throw error;
    }
  }
}

// Export functions for direct use
export const cleanupHardcodedCarouselSlides = CarouselCleanupService.cleanupHardcodedSlides;
export const debugListCarouselSlides = CarouselCleanupService.debugListAllSlides;
export const cleanupOrphanedCarouselSlides = CarouselCleanupService.cleanupOrphanedSlides;
