import { collection, getDocs, doc, updateDoc, deleteDoc } from 'firebase/firestore';
import { db } from '../lib/firebase';
import { CarouselSlide, Album, Track } from '../types';

/**
 * <PERSON>ript to clean any remaining demo data from the database
 */
export class DatabaseDemoDataCleaner {
  
  /**
   * Check and clean carousel slides that reference test albums
   */
  static async cleanCarouselSlides(): Promise<void> {
    try {
      console.log('🔍 Checking carousel slides for demo data...');
      
      const slidesSnapshot = await getDocs(collection(db, 'carouselSlides'));
      const slides = slidesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as CarouselSlide[];
      
      console.log(`Found ${slides.length} carousel slides`);
      
      let cleanedCount = 0;
      
      for (const slide of slides) {
        if (slide.albumId === 'test-album-1' || slide.albumTitle === 'Test Album' || slide.albumTitle === 'Demo Album') {
          console.log(`🧹 Cleaning carousel slide "${slide.title}" - removing test album reference`);
          
          await updateDoc(doc(db, 'carouselSlides', slide.id), {
            albumId: null,
            albumTitle: null,
            updatedAt: new Date()
          });
          
          cleanedCount++;
        }
      }
      
      console.log(`✅ Cleaned ${cleanedCount} carousel slides`);
      
    } catch (error) {
      console.error('❌ Failed to clean carousel slides:', error);
      throw error;
    }
  }
  
  /**
   * Check for any test albums or tracks in the database
   */
  static async checkForTestData(): Promise<void> {
    try {
      console.log('🔍 Checking database for test albums and tracks...');
      
      // Check albums
      const albumsSnapshot = await getDocs(collection(db, 'albums'));
      const albums = albumsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Album[];
      
      const testAlbums = albums.filter(album => 
        album.id === 'test-album-1' || 
        album.title === 'Demo Album' ||
        album.artist === 'Vibes Artist'
      );
      
      if (testAlbums.length > 0) {
        console.log(`⚠️ Found ${testAlbums.length} test albums in database:`, testAlbums.map(a => a.title));
      } else {
        console.log('✅ No test albums found in database');
      }
      
      // Check tracks
      const tracksSnapshot = await getDocs(collection(db, 'tracks'));
      const tracks = tracksSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Track[];
      
      const testTracks = tracks.filter(track => 
        track.id?.startsWith('test-track-') || 
        track.title?.startsWith('Demo Song') ||
        track.artist === 'Vibes Artist' ||
        track.albumTitle === 'Demo Album'
      );
      
      if (testTracks.length > 0) {
        console.log(`⚠️ Found ${testTracks.length} test tracks in database:`, testTracks.map(t => t.title));
      } else {
        console.log('✅ No test tracks found in database');
      }
      
    } catch (error) {
      console.error('❌ Failed to check for test data:', error);
      throw error;
    }
  }
  
  /**
   * Run complete demo data cleanup
   */
  static async runCompleteCleanup(): Promise<void> {
    try {
      console.log('🧹 Starting complete demo data cleanup...');
      
      await this.cleanCarouselSlides();
      await this.checkForTestData();
      
      console.log('✅ Complete demo data cleanup finished');
      
    } catch (error) {
      console.error('❌ Demo data cleanup failed:', error);
      throw error;
    }
  }
}

// Export for console access during development
if (typeof window !== 'undefined') {
  (window as any).cleanDatabaseDemoData = DatabaseDemoDataCleaner.runCompleteCleanup;
}
