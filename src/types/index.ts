export type UserRole = 'admin' | 'artist' | 'listener';

export interface User {
  id: string;
  email: string;
  displayName: string;
  username: string;
  role: UserRole;
  photoURL?: string;
  bio?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Album {
  id: string;
  title: string;
  artist: string;
  description?: string;
  coverUrl?: string;
  trackIds: string[]; // Array of track IDs in order
  uploadedBy: string;
  status: 'draft' | 'published';
  createdAt: Date;
  updatedAt: Date;
  publishedAt?: Date;
  genre?: string;
  totalDuration?: number; // Calculated from tracks
  trackCount?: number; // Calculated from tracks

  // Engagement metrics
  playCount?: number; // Total number of album plays
  likeCount?: number; // Total number of likes
  saveCount?: number; // Total number of saves
}

export interface Track {
  id: string;
  title: string;
  artist: string;
  albumId?: string; // Reference to album if part of one
  albumTitle?: string; // Denormalized for easier queries
  duration: number;
  url: string;
  coverUrl?: string;
  uploadedBy: string;
  status: 'draft' | 'published';
  createdAt: Date;
  updatedAt: Date;
  publishedAt?: Date;
  genre?: string;
  mood?: string;
  trackNumber?: number; // Position in album

  // Enhanced audio metadata
  fileSize?: number; // File size in bytes
  format?: string; // Audio format (MP3, FLAC, etc.)
  bitrate?: number; // Bitrate in kbps
  sampleRate?: number; // Sample rate in Hz
  channels?: number; // Number of audio channels
  year?: string; // Release year
  composer?: string; // Music composer
  bpm?: number; // Beats per minute
  key?: string; // Musical key

  // Engagement metrics
  playCount?: number; // Total number of plays
  likeCount?: number; // Total number of likes
  saveCount?: number; // Total number of saves
  trendingScore?: number; // Calculated trending score
}

export interface Playlist {
  id: string;
  name: string;
  description?: string;
  coverUrl?: string;
  tracks: string[]; // Track IDs
  createdBy: string;
  isPublic: boolean;
  collaborators?: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface ChatMessage {
  id: string;
  content: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  timestamp: Date;
  trackId?: string; // If message is related to a specific track
  reactions?: Record<string, string[]>; // emoji -> user IDs
}

export interface MusicState {
  currentTrack: Track | null;
  isPlaying: boolean;
  volume: number;
  currentTime: number;
  duration: number;
  queue: Track[];
  currentIndex: number;
  shuffle: boolean;
  repeat: 'none' | 'one' | 'all';
}

// Upload-related types
export interface UploadTrack {
  id: string;
  file: File;
  metadata: {
    title: string;
    artist: string;
    genre?: string;
    mood?: string;
    trackNumber?: number;
  };
  coverFile?: File;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  error?: string;
}

export interface UploadAlbum {
  metadata: {
    title: string;
    artist: string;
    description?: string;
    genre?: string;
  };
  coverFile?: File;
  tracks: UploadTrack[];
}

export interface UploadSession {
  id: string;
  type: 'single' | 'album';
  status: 'draft' | 'published';
  single?: UploadTrack;
  album?: UploadAlbum;
  currentStep: number;
  totalSteps: number;
}

// Engagement tracking types for Discover Page
export interface PlayEvent {
  id: string;
  trackId: string;
  albumId?: string;
  userId?: string; // null for anonymous users
  sessionId: string; // Browser session ID for anonymous tracking
  playedAt: Date;
  duration: number; // How long the track was played (in seconds)
  completed: boolean; // Whether the track was played to completion
  skipTime?: number; // Time when user skipped (if applicable)
}

export interface LikeEvent {
  id: string;
  trackId?: string;
  albumId?: string;
  userId: string; // Only authenticated users can like
  likedAt: Date;
  type: 'track' | 'album';
}

export interface SaveEvent {
  id: string;
  trackId?: string;
  albumId?: string;
  userId: string; // Only authenticated users can save
  savedAt: Date;
  type: 'track' | 'album';
}

// Trending calculation types
export interface TrendingMetrics {
  trackId: string;
  playCount: number;
  likeCount: number;
  saveCount: number;
  recentPlays: number; // Plays in last 7 days
  trendingScore: number; // Calculated score
  lastUpdated: Date;
}

// Carousel slide management types
export interface CarouselSlide {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  gradient: string;
  accentColor: string;
  icon: string; // Icon name (e.g., 'Headphones', 'Music')
  albumId: string | null; // Album to link Listen CTA to (null if no album linked)
  albumTitle: string | null; // Denormalized for easier display (null if no album linked)
  albumCoverUrl: string | null; // Denormalized album cover for instant loading (null if no album linked)
  isActive: boolean; // Whether slide is currently shown
  order: number; // Display order
  createdBy: string; // Admin who created it
  createdAt: Date;
  updatedAt: Date;
}

// Engagement tracking types
export interface PlayEvent {
  id: string;
  trackId: string;
  albumId?: string;
  userId?: string; // null for anonymous users
  sessionId: string; // Browser session ID for anonymous tracking
  playedAt: Date;
  duration: number; // How long the track was played (in seconds)
  completed: boolean; // Whether the track was played to completion
  skipTime?: number; // Time when user skipped (if applicable)
}

export interface LikeEvent {
  id: string;
  trackId?: string;
  albumId?: string;
  userId: string; // Only authenticated users can like
  likedAt: Date;
  type: 'track' | 'album';
}

export interface SaveEvent {
  id: string;
  trackId?: string;
  albumId?: string;
  userId: string; // Only authenticated users can save
  savedAt: Date;
  type: 'track' | 'album';
}

// Trending calculation types
export interface TrendingMetrics {
  trackId: string;
  playCount: number;
  likeCount: number;
  saveCount: number;
  recentPlays: number; // Plays in last 7 days
  trendingScore: number; // Calculated score
  lastUpdated: Date;
}