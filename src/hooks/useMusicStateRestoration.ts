import { useEffect, useRef } from 'react';
import { useMusicStore } from '../store/musicStore';
import { AudioService } from '../services/audioService';

/**
 * Hook to handle music state restoration after page refresh
 * This ensures that when the app loads with persisted music state,
 * the audio player is properly initialized with the current track
 *
 * NOTE: This hook only handles volume restoration. Track loading is handled
 * by useAudioPlayer to avoid conflicts and maintain proper state management.
 */
export const useMusicStateRestoration = () => {
  const { currentTrack, queue, volume } = useMusicStore();
  const hasRestoredRef = useRef(false);

  useEffect(() => {
    // Only run restoration once when the app first loads
    if (hasRestoredRef.current) return;

    // Check if we have persisted music state to restore
    const hasPersistedState = currentTrack && queue.length > 0;

    if (hasPersistedState) {
      console.log('🔄 Restoring music state from localStorage');
      console.log('📀 Current track:', currentTrack.title, 'by', currentTrack.artist);
      console.log('📋 Queue length:', queue.length);

      // Only restore volume setting - let useAudioPlayer handle track loading
      // This prevents conflicts between restoration and normal audio player flow
      AudioService.setVolume(volume);

      console.log('✅ Music state restoration completed (volume restored, track loading handled by useAudioPlayer)');
    } else {
      console.log('ℹ️ No persisted music state found');
    }

    hasRestoredRef.current = true;
  }, [currentTrack, queue, volume]);

  // Return restoration status for debugging
  return {
    hasPersistedState: !!(currentTrack && queue.length > 0),
    isRestored: hasRestoredRef.current
  };
};
