import { useEffect, useState } from 'react';
import { enableNetwork, disableNetwork } from 'firebase/firestore';
import { db } from '../lib/firebase';
import { Toast } from '../utils/toast';

export const useFirestoreConnection = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [connectionError, setConnectionError] = useState<string | null>(null);

  useEffect(() => {
    let offlineToastShown = false;

    // Listen for online/offline events
    const handleOnline = () => {
      setIsOnline(true);
      setConnectionError(null);
      offlineToastShown = false;

      // Show reconnection toast
      Toast.success('Back online! 🌐', 3000);

      enableNetwork(db).catch((error) => {
        console.warn('Failed to enable Firestore network:', error);
        setConnectionError('Failed to reconnect to database');
      });
    };

    const handleOffline = () => {
      setIsOnline(false);
      setConnectionError('You are offline');

      // Show offline toast only once
      if (!offlineToastShown) {
        Toast.info('You\'re offline 📱', 5000);
        offlineToastShown = true;
      }

      disableNetwork(db).catch((error) => {
        console.warn('Failed to disable Firestore network:', error);
      });
    };

    // Add event listeners
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Check initial connection state and show toast if offline
    if (!navigator.onLine && !offlineToastShown) {
      Toast.info('You\'re offline 📱', 5000);
      offlineToastShown = true;
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return {
    isOnline,
    connectionError
  };
};
