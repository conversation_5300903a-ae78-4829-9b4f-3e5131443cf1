import { useEffect, useState } from 'react';
import { enableNetwork, disableNetwork } from 'firebase/firestore';
import { db } from '../lib/firebase';

export const useFirestoreConnection = () => {
  const [isOnline, setIsOnline] = useState(true);
  const [connectionError, setConnectionError] = useState<string | null>(null);

  useEffect(() => {
    // Listen for online/offline events
    const handleOnline = () => {
      setIsOnline(true);
      setConnectionError(null);
      enableNetwork(db).catch((error) => {
        console.warn('Failed to enable Firestore network:', error);
        setConnectionError('Failed to reconnect to database');
      });
    };

    const handleOffline = () => {
      setIsOnline(false);
      setConnectionError('You are offline');
      disableNetwork(db).catch((error) => {
        console.warn('Failed to disable Firestore network:', error);
      });
    };

    // Add event listeners
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Check initial connection state
    setIsOnline(navigator.onLine);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return {
    isOnline,
    connectionError
  };
};
