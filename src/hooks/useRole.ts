import { useAuth } from './useAuth';
import { RoleService } from '../services/roleService';
import { UserRole } from '../types';

/**
 * Hook for role-based access control
 * Provides convenient methods to check user permissions
 */
export const useRole = () => {
  const { user } = useAuth();

  return {
    user,
    
    // Role checks
    isAdmin: RoleService.isAdmin(user),
    isArtist: RoleService.isArtist(user),
    isListener: RoleService.isListener(user),
    
    // Permission checks
    canUploadMusic: RoleService.canUploadMusic(user),
    hasPermission: (permission: string) => RoleService.hasPermission(user, permission),
    
    // Role utilities
    role: user?.role,
    roleDisplayName: user ? RoleService.getRoleDisplayName(user.role) : null,
    roleDescription: user ? RoleService.getRoleDescription(user.role) : null,
    uploadAccessMessage: RoleService.getUploadAccessMessage(user),
    
    // Role assignment validation (for admin interfaces)
    canAssignRole: (targetRole: UserRole) => RoleService.canAssignRole(user, targetRole),
    validateRoleAssignment: (targetUserId: string, newRole: UserRole) => 
      RoleService.validateRoleAssignment(user, targetUserId, newRole),
  };
};
