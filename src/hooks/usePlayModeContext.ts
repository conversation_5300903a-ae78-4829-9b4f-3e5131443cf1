import { create } from 'zustand';

interface ContextualTab {
  type: 'album' | 'playlist' | 'liked' | null;
  label: string;
  data?: any;
}

interface PlayModeContextStore {
  contextualTab: ContextualTab;
  activeTab: 'play' | 'queue' | 'playlist';
  
  // Actions
  activateContextualTab: (type: 'album' | 'playlist' | 'liked', label: string, data?: any) => void;
  clearContextualTab: () => void;
  setActiveTab: (tab: 'play' | 'queue' | 'playlist') => void;
}

export const usePlayModeContext = create<PlayModeContextStore>((set, get) => ({
  contextualTab: { type: null, label: '', data: null },
  activeTab: 'play',
  
  activateContextualTab: (type, label, data) => {
    set({ 
      contextualTab: { type, label, data },
      activeTab: 'playlist' // Automatically switch to the contextual tab
    });
  },
  
  clearContextualTab: () => {
    const { activeTab } = get();
    set({ 
      contextualTab: { type: null, label: '', data: null },
      // If we're on the playlist tab, switch back to play
      activeTab: activeTab === 'playlist' ? 'play' : activeTab
    });
  },
  
  setActiveTab: (tab) => {
    set({ activeTab: tab });
  }
}));

// Helper functions for sidebar components to use
export const playModeContextActions = {
  showAlbum: (albumTitle: string, albumData?: any) => {
    usePlayModeContext.getState().activateContextualTab('album', albumTitle, albumData);
  },
  
  showPlaylist: (playlistTitle: string, playlistData?: any) => {
    usePlayModeContext.getState().activateContextualTab('playlist', playlistTitle, playlistData);
  },
  
  showLikedSongs: () => {
    usePlayModeContext.getState().activateContextualTab('liked', 'Liked', {});
  },
  
  clearContext: () => {
    usePlayModeContext.getState().clearContextualTab();
  }
};
