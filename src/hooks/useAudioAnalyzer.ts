import { useEffect, useRef, useState } from 'react';
import { AudioService } from '../services/audioService';

interface AudioAnalyzerData {
  frequencyData: Uint8Array;
  waveformData: Uint8Array;
  isAnalyzing: boolean;
}

// Global audio context and analyzer to prevent multiple connections
let globalAudioContext: AudioContext | null = null;
let globalAnalyzer: AnalyserNode | null = null;
let globalSource: MediaElementAudioSourceNode | null = null;
let isGlobalAnalyzerInitialized = false;

export const useAudioAnalyzer = () => {
  const [analyzerData, setAnalyzerData] = useState<AudioAnalyzerData>({
    frequencyData: new Uint8Array(0),
    waveformData: new Uint8Array(0),
    isAnalyzing: false
  });

  const animationFrameRef = useRef<number | null>(null);

  const initializeAnalyzer = () => {
    try {
      // If global analyzer is already initialized, just start analyzing
      if (isGlobalAnalyzerInitialized && globalAnalyzer) {
        startAnalyzing();
        return;
      }

      // Get the current Howler audio element
      const howlerInstance = (AudioService as any).currentHowl;
      if (!howlerInstance) return;

      // Get the HTML audio element from Howler
      const audioElement = howlerInstance._sounds[0]?._node;
      if (!audioElement) return;

      // Create global audio context (only once) with mobile support
      if (!globalAudioContext) {
        globalAudioContext = new (window.AudioContext || (window as any).webkitAudioContext)();

        // Resume audio context if suspended (required for mobile browsers)
        if (globalAudioContext.state === 'suspended') {
          globalAudioContext.resume().catch(console.warn);
        }
      }

      // Create global analyzer node (only once)
      if (!globalAnalyzer) {
        globalAnalyzer = globalAudioContext.createAnalyser();
        globalAnalyzer.fftSize = 256; // Good balance of detail and performance
        globalAnalyzer.smoothingTimeConstant = 0.8;
      }

      // Create source from audio element (only once per element)
      if (!globalSource) {
        globalSource = globalAudioContext.createMediaElementSource(audioElement);

        // Connect: source -> analyzer -> destination
        globalSource.connect(globalAnalyzer);
        globalAnalyzer.connect(globalAudioContext.destination);

        isGlobalAnalyzerInitialized = true;
      }

      // Start analyzing
      startAnalyzing();

    } catch (error) {
      console.warn('Audio analyzer initialization failed:', error);
    }
  };

  const startAnalyzing = () => {
    if (!globalAnalyzer) return;

    const bufferLength = globalAnalyzer.frequencyBinCount;
    const frequencyData = new Uint8Array(bufferLength);
    const waveformData = new Uint8Array(bufferLength);

    const analyze = () => {
      if (!globalAnalyzer) return;

      // Get frequency data (for spectrum analyzer)
      globalAnalyzer.getByteFrequencyData(frequencyData);

      // Get waveform data (for waveform visualization)
      globalAnalyzer.getByteTimeDomainData(waveformData);

      setAnalyzerData({
        frequencyData: new Uint8Array(frequencyData),
        waveformData: new Uint8Array(waveformData),
        isAnalyzing: true
      });

      animationFrameRef.current = requestAnimationFrame(analyze);
    };

    analyze();
  };

  const stopAnalyzing = () => {
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }

    setAnalyzerData(prev => ({
      ...prev,
      isAnalyzing: false
    }));
  };

  const cleanup = () => {
    stopAnalyzing();
    // Note: We don't cleanup global resources here since they might be used by other components
    // Global cleanup should happen when the app unmounts or audio changes
  };

  // Initialize when audio starts playing
  useEffect(() => {
    const checkForAudio = () => {
      const howlerInstance = (AudioService as any).currentHowl;
      if (howlerInstance && howlerInstance.playing()) {
        initializeAnalyzer();
      }
    };

    // Check periodically for audio
    const interval = setInterval(checkForAudio, 1000);
    
    // Also check immediately
    checkForAudio();

    return () => {
      clearInterval(interval);
      cleanup();
    };
  }, []);

  return {
    ...analyzerData,
    initializeAnalyzer,
    stopAnalyzing,
    cleanup
  };
};
