import { useState, useEffect } from 'react';

// Breakpoint definitions matching Tailwind config
const breakpoints = {
  xs: 475,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const;

type Breakpoint = keyof typeof breakpoints;

interface ResponsiveState {
  // Current screen width
  width: number;
  height: number;
  
  // Breakpoint states
  isXs: boolean;
  isSm: boolean;
  isMd: boolean;
  isLg: boolean;
  isXl: boolean;
  is2Xl: boolean;
  
  // Device type detection
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isTouch: boolean;
  
  // Orientation
  isPortrait: boolean;
  isLandscape: boolean;
  
  // Utility functions
  isAtLeast: (breakpoint: Breakpoint) => boolean;
  isAtMost: (breakpoint: Breakpoint) => boolean;
  isBetween: (min: Breakpoint, max: Breakpoint) => boolean;
}

/**
 * Custom hook for responsive design and breakpoint detection
 * Provides mobile-first responsive utilities and screen size information
 */
export const useResponsive = (): ResponsiveState => {
  const [dimensions, setDimensions] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
  });

  useEffect(() => {
    // Handle window resize with debouncing for performance
    let timeoutId: NodeJS.Timeout;
    
    const handleResize = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        setDimensions({
          width: window.innerWidth,
          height: window.innerHeight,
        });
      }, 100); // 100ms debounce
    };

    // Handle orientation change (mobile devices)
    const handleOrientationChange = () => {
      // Small delay to ensure dimensions are updated after orientation change
      setTimeout(() => {
        setDimensions({
          width: window.innerWidth,
          height: window.innerHeight,
        });
      }, 150);
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleOrientationChange);

    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleOrientationChange);
    };
  }, []);

  const { width, height } = dimensions;

  // Breakpoint detection
  const isXs = width >= breakpoints.xs;
  const isSm = width >= breakpoints.sm;
  const isMd = width >= breakpoints.md;
  const isLg = width >= breakpoints.lg;
  const isXl = width >= breakpoints.xl;
  const is2Xl = width >= breakpoints['2xl'];

  // Device type detection
  const isMobile = width < breakpoints.md; // < 768px
  const isTablet = width >= breakpoints.md && width < breakpoints.lg; // 768px - 1023px
  const isDesktop = width >= breakpoints.lg; // >= 1024px
  const isTouch = width < breakpoints.lg; // < 1024px (mobile + tablet)

  // Orientation detection
  const isPortrait = height > width;
  const isLandscape = width > height;

  // Utility functions
  const isAtLeast = (breakpoint: Breakpoint): boolean => {
    return width >= breakpoints[breakpoint];
  };

  const isAtMost = (breakpoint: Breakpoint): boolean => {
    return width <= breakpoints[breakpoint];
  };

  const isBetween = (min: Breakpoint, max: Breakpoint): boolean => {
    return width >= breakpoints[min] && width <= breakpoints[max];
  };

  return {
    width,
    height,
    isXs,
    isSm,
    isMd,
    isLg,
    isXl,
    is2Xl,
    isMobile,
    isTablet,
    isDesktop,
    isTouch,
    isPortrait,
    isLandscape,
    isAtLeast,
    isAtMost,
    isBetween,
  };
};

/**
 * Hook for detecting if the current screen size matches a specific breakpoint
 */
export const useBreakpoint = (breakpoint: Breakpoint): boolean => {
  const { isAtLeast } = useResponsive();
  return isAtLeast(breakpoint);
};

/**
 * Hook for detecting mobile devices specifically
 */
export const useIsMobile = (): boolean => {
  const { isMobile } = useResponsive();
  return isMobile;
};

/**
 * Hook for detecting touch devices (mobile + tablet)
 */
export const useIsTouch = (): boolean => {
  const { isTouch } = useResponsive();
  return isTouch;
};

/**
 * Hook for getting current device type
 */
export const useDeviceType = (): 'mobile' | 'tablet' | 'desktop' => {
  const { isMobile, isTablet, isDesktop } = useResponsive();
  
  if (isMobile) return 'mobile';
  if (isTablet) return 'tablet';
  if (isDesktop) return 'desktop';
  
  return 'desktop'; // fallback
};
