import { useEffect, useRef, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useMusicStore } from '../store/musicStore';
import { useVibesStore } from '../store/vibesStore';
import { AudioService } from '../services/audioService';
import { EngagementService } from '../services/engagementService';
import { useAuth } from '../contexts/AuthContext';
import { Track } from '../types';

export const useAudioPlayer = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();
  const {
    currentTrack,
    isPlaying,
    volume,
    currentTime,
    duration,
    queue,
    currentIndex,
    repeat,
    setCurrentTime,
    setDuration,
    togglePlay,
    nextTrack,
    setCurrentTrack,
    setIsPlaying,
    resetToggling,
    updateTrackPlayCount
  } = useMusicStore();

  const { autoNavigateToPlay, addRecentPage } = useVibesStore();

  // State management
  const [isTrackLoading, setIsTrackLoading] = useState(false);
  const [loadingError, setLoadingError] = useState<string | null>(null);
  const [isMounted, setIsMounted] = useState(false);

  // Refs for tracking state
  const lastTrackRef = useRef<Track | null>(null);
  const isInitializedRef = useRef(false);
  const lastPlayingStateRef = useRef(false);
  const lastCurrentTimeRef = useRef<number>(0);
  const playTrackingRef = useRef<boolean>(false); // Track if we've already tracked this play session
  const hasLoadedInitialTrackRef = useRef(false); // Track if we've loaded the initial track after restoration
  const effectRunCountRef = useRef(0); // Track how many times the effect has run

  // Set mounted state on first render
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Consolidated track loading effect (handles both normal loading and restoration)
  useEffect(() => {
    effectRunCountRef.current += 1;
    console.log('🎵 useAudioPlayer: Track loading effect running for:', currentTrack?.title || 'null', 'Run #', effectRunCountRef.current);

    if (!currentTrack) {
      console.log('🎵 useAudioPlayer: No current track, skipping load');
      setIsTrackLoading(false);
      setLoadingError(null);
      return;
    }

    // Validate track has required properties
    if (!currentTrack.url) {
      const error = 'Track URL is missing';
      console.error('Failed to load track:', error, currentTrack);
      setLoadingError(error);
      setIsTrackLoading(false);
      return;
    }

    // Determine if we need to load this track
    const isNewTrack = lastTrackRef.current?.id !== currentTrack.id;
    const needsForceLoad = !hasLoadedInitialTrackRef.current && AudioService.getDuration() === 0;
    const shouldLoad = isNewTrack || needsForceLoad;

    console.log('🎵 useAudioPlayer: Loading conditions:', {
      isNewTrack,
      needsForceLoad,
      shouldLoad,
      lastTrackId: lastTrackRef.current?.id,
      currentTrackId: currentTrack.id,
      hasLoadedInitial: hasLoadedInitialTrackRef.current,
      audioDuration: AudioService.getDuration()
    });

    if (!shouldLoad) {
      console.log('🎵 useAudioPlayer: Track already loaded, skipping');
      return;
    }

    console.log('🎵 useAudioPlayer: Loading track:', currentTrack.title, {
      isNewTrack,
      needsForceLoad,
      lastTrack: lastTrackRef.current?.title || 'none'
    });

    const loadTrack = () => {
      // Set loading state
      setIsTrackLoading(true);
      setLoadingError(null);

      // Load the track
      AudioService.loadTrack(currentTrack, {
        onLoad: () => {
          const trackDuration = AudioService.getDuration();
          setDuration(trackDuration);
          setIsTrackLoading(false);
          setLoadingError(null);
          console.log('✅ Track loaded successfully:', currentTrack.title, 'Duration:', trackDuration);
        },
        onLoadError: (error) => {
          const errorMessage = `Failed to load track: ${error}`;
          console.error(errorMessage);
          setLoadingError(errorMessage);
          setIsTrackLoading(false);
        },
        onPlay: async () => {
          // Audio service confirms playback started
          // Track play event only once per track session
          if (!playTrackingRef.current && user) {
            playTrackingRef.current = true;
            try {
              console.log('🎯 Tracking play for:', currentTrack.title);
              await EngagementService.trackPlay(
                currentTrack.id,
                currentTrack.albumId,
                user.id
              );
              // Update local play count for immediate UI feedback
              updateTrackPlayCount(currentTrack.id);
              console.log('✅ Play tracked successfully');
            } catch (error) {
              console.error('❌ Failed to track play:', error);
              // Reset flag so we can try again
              playTrackingRef.current = false;
            }
          }
        },
        onPause: () => {
          // Audio service confirms playback paused
        },
        onEnd: () => {
          if (repeat === 'one') {
            // Replay the same track
            AudioService.seek(0);
            AudioService.play();
          } else {
            // Move to next track
            nextTrack();
          }
        },
        onTimeUpdate: (time) => {
          setCurrentTime(time);
        }
      }).catch((error) => {
        const errorMessage = `Failed to load track: ${error}`;
        console.error(errorMessage);
        setLoadingError(errorMessage);
        setIsTrackLoading(false);
      });

      // Update tracking refs
      lastTrackRef.current = currentTrack;
      hasLoadedInitialTrackRef.current = true;
      playTrackingRef.current = false; // Reset play tracking for new track
    };

    // Load track immediately
    loadTrack();
  }, [currentTrack, isMounted]); // Run when track changes OR when component mounts



  // Handle play/pause state changes with mobile browser support
  useEffect(() => {
    if (!currentTrack) return;

    const audioIsPlaying = AudioService.isPlaying();

    if (isPlaying && !audioIsPlaying) {
      // Check if track is still loading - wait for it to complete
      if (isTrackLoading) {
        console.log('🎵 Track is still loading, will retry when loading completes...');
        // Don't reset isPlaying - let it stay true so we retry when loading finishes
        resetToggling();
        return;
      }

      // Check if there's a loading error
      if (loadingError) {
        console.error('🎵 Cannot play due to loading error:', loadingError);
        // Reset play state since we can't play
        setIsPlaying(false);
        resetToggling();
        return;
      }

      // For mobile browsers, try immediate playback first
      const startPlayback = async () => {
        // Double-check state hasn't changed and track is loaded
        if (useMusicStore.getState().isPlaying && !AudioService.isPlaying() && !isTrackLoading) {
          console.log('🎵 Starting audio playback with mobile support');

          // Try mobile-friendly playback first
          const success = await AudioService.playWithUserGesture();

          if (success) {
            console.log('✅ Mobile audio playback started successfully!');
          } else {
            // Fallback to regular play method with minimal delay
            console.log('🎵 Falling back to regular playback');
            setTimeout(() => {
              if (useMusicStore.getState().isPlaying && !AudioService.isPlaying() && !isTrackLoading) {
                AudioService.play();
              }
            }, 10); // Minimal delay for fallback
          }
        }
        // Reset toggling state after audio operation
        resetToggling();
      };

      startPlayback();
    } else if (!isPlaying && audioIsPlaying) {
      console.log('🎵 Pausing audio playback');
      AudioService.pause();
      // Reset toggling state immediately for pause
      resetToggling();
    } else {
      // Reset toggling state if no action needed
      resetToggling();
    }
  }, [isPlaying, currentTrack, isTrackLoading, loadingError, resetToggling, setIsPlaying]);



  // Auto-navigate to Play page when music starts playing
  useEffect(() => {
    // Only navigate if:
    // 1. Auto-navigation is enabled in user preferences
    // 2. Music just started playing (wasn't playing before)
    // 3. There's a current track
    // 4. We're not already on the play page
    // 5. We're not on the profile page (to avoid interrupting user's workflow)
    if (
      autoNavigateToPlay &&
      isPlaying &&
      !lastPlayingStateRef.current &&
      currentTrack &&
      location.pathname !== '/play' &&
      location.pathname !== '/profile'
    ) {
      addRecentPage(location.pathname); // Track where user was before auto-navigation
      navigate('/play');
    }

    // Update the ref to track the previous playing state
    lastPlayingStateRef.current = isPlaying;
  }, [isPlaying, currentTrack, location.pathname, navigate, autoNavigateToPlay, addRecentPage]);

  // Handle volume changes
  useEffect(() => {
    AudioService.setVolume(volume);
  }, [volume]);

  // Handle manual currentTime changes (e.g., from previousTrack restart)
  useEffect(() => {
    if (!currentTrack) return;

    // If currentTime was manually set to 0 and it's different from the last time
    // (indicating a restart), seek the audio to the beginning
    if (currentTime === 0 && lastCurrentTimeRef.current !== 0) {
      AudioService.seek(0);
    }

    lastCurrentTimeRef.current = currentTime;
  }, [currentTime, currentTrack]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      AudioService.destroy();
    };
  }, []);

  // Seek functionality
  const seekTo = (time: number) => {
    AudioService.seek(time);
    setCurrentTime(time);
  };

  // Manual play/pause toggle
  const handleTogglePlay = () => {
    if (!currentTrack) return;
    togglePlay();
  };

  return {
    // State
    currentTrack,
    isPlaying,
    volume,
    currentTime,
    duration,
    isTrackLoading,
    loadingError,

    // Actions
    seekTo,
    togglePlay: handleTogglePlay,

    // Audio service methods for direct access if needed
    audioService: AudioService
  };
};
