import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { Track, MusicState, Album } from '../types';

interface MusicStore extends MusicState {
  // Actions
  setCurrentTrack: (track: Track) => void;
  togglePlay: () => void;
  setIsPlaying: (isPlaying: boolean) => void;
  setVolume: (volume: number) => void;
  setCurrentTime: (time: number) => void;
  setDuration: (duration: number) => void;
  addToQueue: (track: Track) => void;
  removeFromQueue: (index: number) => void;
  nextTrack: () => void;
  previousTrack: () => void;
  forcePreviousTrack: () => void; // For double-press behavior
  toggleShuffle: () => void;
  toggleRepeat: () => void;
  setQueue: (tracks: Track[]) => void;
  // Progressive loading
  appendToQueue: (tracks: Track[]) => void;
  setQueueWithFirstTrack: (firstTrack: Track) => void;
  // Library refresh
  libraryRefreshTrigger: number;
  triggerLibraryRefresh: () => void;
  // Current album in library (for play mode display)
  currentAlbum: Album | null;
  setCurrentAlbum: (album: Album | null) => void;
  // Previous button tracking for double-press
  lastPreviousPress: number;
  setLastPreviousPress: (timestamp: number) => void;
  // Debouncing protection for play/pause
  isToggling: boolean;
  lastToggleTime: number;
  resetToggling: () => void;
  // Play count tracking
  updateTrackPlayCount: (trackId: string) => void;
  // Like count tracking
  updateTrackLikeCount: (trackId: string, increment: boolean) => void;
}

export const useMusicStore = create<MusicStore>()(
  persist(
    (set, get) => ({
  // Initial state
  currentTrack: null,
  isPlaying: false,
  volume: 0.8,
  currentTime: 0,
  duration: 0,
  queue: [],
  currentIndex: 0,
  shuffle: false,
  repeat: 'none',
  libraryRefreshTrigger: 0,
  currentAlbum: null,
  lastPreviousPress: 0,
  isToggling: false,
  lastToggleTime: 0,

  // Actions
  setCurrentTrack: (track) => set({ currentTrack: track }),

  togglePlay: () => set((state) => {
    const now = Date.now();
    const timeSinceLastToggle = now - state.lastToggleTime;

    // Debounce: Ignore rapid clicks within 200ms (Spotify-like behavior)
    if (state.isToggling || timeSinceLastToggle < 200) {
      console.log('🎵 Debouncing rapid play/pause click');
      return state;
    }

    console.log('🎵 Toggle play:', !state.isPlaying);
    return {
      isPlaying: !state.isPlaying,
      isToggling: true,
      lastToggleTime: now
    };
  }),

  setIsPlaying: (isPlaying) => set({ isPlaying }),
  
  setVolume: (volume) => set({ volume: Math.max(0, Math.min(1, volume)) }),
  
  setCurrentTime: (time) => set({ currentTime: time }),
  
  setDuration: (duration) => set({ duration }),
  
  addToQueue: (track) => set((state) => ({ 
    queue: [...state.queue, track] 
  })),
  
  removeFromQueue: (index) => set((state) => ({
    queue: state.queue.filter((_, i) => i !== index)
  })),
  
  nextTrack: () => set((state) => {
    const { queue, currentIndex, shuffle, repeat } = state;
    if (queue.length === 0) return state;

    let nextIndex = currentIndex;

    if (shuffle) {
      // In shuffle mode, pick a random track but avoid current track if queue has more than 1 song
      if (queue.length > 1) {
        do {
          nextIndex = Math.floor(Math.random() * queue.length);
        } while (nextIndex === currentIndex);
      } else {
        nextIndex = currentIndex; // Only one track, stay on it
      }
    } else if (repeat === 'one') {
      // Stay on current track - don't change index
      return {
        currentTime: 0 // Just restart the track
      };
    } else if (repeat === 'all') {
      nextIndex = (currentIndex + 1) % queue.length;
    } else {
      nextIndex = currentIndex + 1;
      if (nextIndex >= queue.length) return state; // End of queue
    }

    return {
      currentIndex: nextIndex,
      currentTrack: queue[nextIndex],
      currentTime: 0
    };
  }),
  
  previousTrack: () => set((state) => {
    const { queue, currentIndex, currentTime, lastPreviousPress } = state;
    if (queue.length === 0) return state;

    const now = Date.now();
    const timeSinceLastPress = now - lastPreviousPress;

    // Double-press detection: if pressed within 500ms, force go to previous track
    const isDoublePress = timeSinceLastPress < 500;

    // Spotify-like behavior:
    // Double press: Always go to previous track
    // Single press + track playing > 3 seconds: restart current track
    // Single press + track playing < 3 seconds: go to previous track
    // If we're at the first track and not double press: restart current track

    const shouldGoToPrevious = isDoublePress || (currentTime <= 3 && currentIndex > 0);

    if (shouldGoToPrevious && currentIndex > 0) {
      // Go to previous track
      const prevIndex = currentIndex - 1;
      return {
        currentIndex: prevIndex,
        currentTrack: queue[prevIndex],
        currentTime: 0,
        lastPreviousPress: now
      };
    } else {
      // Restart current track
      return {
        currentTime: 0,
        lastPreviousPress: now
      };
    }
  }),

  forcePreviousTrack: () => set((state) => {
    const { queue, currentIndex } = state;
    if (queue.length === 0 || currentIndex === 0) return state;

    const prevIndex = currentIndex - 1;
    return {
      currentIndex: prevIndex,
      currentTrack: queue[prevIndex],
      currentTime: 0
    };
  }),
  
  toggleShuffle: () => set((state) => ({ shuffle: !state.shuffle })),
  
  toggleRepeat: () => set((state) => ({
    repeat: state.repeat === 'none' ? 'all' : 
            state.repeat === 'all' ? 'one' : 'none'
  })),
  
  setQueue: (tracks) => set({
    queue: tracks,
    currentIndex: 0,
    currentTrack: tracks[0] || null
  }),

  // Progressive loading: Start with just the first track
  setQueueWithFirstTrack: (firstTrack) => set({
    queue: [firstTrack],
    currentIndex: 0,
    currentTrack: firstTrack
  }),

  // Progressive loading: Add more tracks to existing queue
  appendToQueue: (tracks) => set((state) => {
    const newQueue = [...state.queue, ...tracks];
    return {
      queue: newQueue,
      // Keep current track and index unchanged
    };
  }),

  triggerLibraryRefresh: () => set((state) => ({
    libraryRefreshTrigger: state.libraryRefreshTrigger + 1
  })),

  setCurrentAlbum: (album) => set({ currentAlbum: album }),

  setLastPreviousPress: (timestamp) => set({ lastPreviousPress: timestamp }),

  resetToggling: () => set({ isToggling: false }),

  updateTrackPlayCount: (trackId) => set((state) => {
    // Update play count for the track in current track and queue
    const updateTrack = (track: Track | null) => {
      if (track && track.id === trackId) {
        return {
          ...track,
          playCount: (track.playCount || 0) + 1
        };
      }
      return track;
    };

    return {
      currentTrack: updateTrack(state.currentTrack),
      queue: state.queue.map(track =>
        track.id === trackId
          ? { ...track, playCount: (track.playCount || 0) + 1 }
          : track
      )
    };
  }),

  updateTrackLikeCount: (trackId, increment) => set((state) => {
    // Update like count for the track in current track and queue
    const updateTrack = (track: Track | null) => {
      if (track && track.id === trackId) {
        return {
          ...track,
          likeCount: Math.max(0, (track.likeCount || 0) + (increment ? 1 : -1))
        };
      }
      return track;
    };

    return {
      currentTrack: updateTrack(state.currentTrack),
      queue: state.queue.map(track =>
        track.id === trackId
          ? { ...track, likeCount: Math.max(0, (track.likeCount || 0) + (increment ? 1 : -1)) }
          : track
      )
    };
  })
}),
{
  name: 'vibes-music-state',
  partialize: (state) => ({
    currentTrack: state.currentTrack,
    queue: state.queue,
    currentIndex: state.currentIndex,
    currentAlbum: state.currentAlbum,
    volume: state.volume,
    shuffle: state.shuffle,
    repeat: state.repeat,
  }),
}
)
);

// Debug function to test play/pause debouncing
if (typeof window !== 'undefined') {
  (window as any).testPlayPauseDebouncing = () => {
    console.log('🧪 Testing play/pause debouncing...');
    const store = useMusicStore.getState();

    // Simulate rapid clicks
    for (let i = 0; i < 5; i++) {
      setTimeout(() => {
        console.log(`🧪 Rapid click ${i + 1}`);
        store.togglePlay();
      }, i * 50); // 50ms intervals (very rapid)
    }

    console.log('🧪 Test completed - check console for debouncing messages');
  };
}