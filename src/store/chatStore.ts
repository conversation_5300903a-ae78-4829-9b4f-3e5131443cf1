import { create } from 'zustand';
import { ChatMessage } from '../types';

interface ChatState {
  // Messages state
  messages: ChatMessage[];
  isLoading: boolean;
  error: string | null;

  // Track context
  currentTrackId: string | null;

  // Pagination state
  hasMoreMessages: boolean;
  lastMessageTimestamp: Date | null;

  // UI state
  isConnected: boolean;
  unreadCount: number;

  // Online users tracking
  onlineUsers: Set<string>;
  onlineCount: number;

  // User typing indicators
  typingUsers: Set<string>;
}

interface ChatActions {
  // Message actions
  addMessage: (message: ChatMessage) => void;
  addMessages: (messages: ChatMessage[]) => void;
  updateMessage: (messageId: string, updates: Partial<ChatMessage>) => void;
  removeMessage: (messageId: string) => void;
  clearMessages: () => void;

  // Track context
  setCurrentTrackId: (trackId: string | null) => void;

  // Loading and error states
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;

  // Connection state
  setConnected: (connected: boolean) => void;
  
  // Pagination
  setHasMoreMessages: (hasMore: boolean) => void;
  setLastMessageTimestamp: (timestamp: Date | null) => void;
  
  // Unread count
  setUnreadCount: (count: number) => void;
  incrementUnreadCount: () => void;
  resetUnreadCount: () => void;

  // Online users tracking
  setOnlineUsers: (users: Set<string>) => void;
  addOnlineUser: (userId: string) => void;
  removeOnlineUser: (userId: string) => void;
  setOnlineCount: (count: number) => void;

  // Typing indicators
  addTypingUser: (userId: string) => void;
  removeTypingUser: (userId: string) => void;
  clearTypingUsers: () => void;
}

interface ChatStore extends ChatState, ChatActions {}

export const useChatStore = create<ChatStore>((set, get) => ({
  // Initial state - optimistic for instant loading
  messages: [],
  isLoading: false, // Firebase provides instant callback - no loading needed
  error: null,
  currentTrackId: null,
  hasMoreMessages: true,
  lastMessageTimestamp: null,
  isConnected: true, // Optimistically connected for instant UI
  unreadCount: 0,
  onlineUsers: new Set(),
  onlineCount: 0,
  typingUsers: new Set(),

  // Message actions
  addMessage: (message) => set((state) => {
    // Check if message already exists to prevent duplicates
    const exists = state.messages.some(m => m.id === message.id);
    if (exists) return state;
    
    // Add message and sort by timestamp (newest first for display)
    const newMessages = [...state.messages, message].sort(
      (a, b) => b.timestamp.getTime() - a.timestamp.getTime()
    );
    
    return {
      messages: newMessages,
      lastMessageTimestamp: message.timestamp
    };
  }),

  addMessages: (messages) => set((state) => {
    // Filter out duplicates and merge with existing messages
    const existingIds = new Set(state.messages.map(m => m.id));
    const newMessages = messages.filter(m => !existingIds.has(m.id));
    
    const allMessages = [...state.messages, ...newMessages].sort(
      (a, b) => b.timestamp.getTime() - a.timestamp.getTime()
    );
    
    return {
      messages: allMessages,
      lastMessageTimestamp: messages.length > 0 
        ? messages[messages.length - 1].timestamp 
        : state.lastMessageTimestamp
    };
  }),

  updateMessage: (messageId, updates) => set((state) => ({
    messages: state.messages.map(message =>
      message.id === messageId ? { ...message, ...updates } : message
    )
  })),

  removeMessage: (messageId) => set((state) => ({
    messages: state.messages.filter(message => message.id !== messageId)
  })),

  clearMessages: () => set({
    messages: [],
    lastMessageTimestamp: null,
    hasMoreMessages: true,
    unreadCount: 0
  }),

  // Track context
  setCurrentTrackId: (trackId) => set((state) => {
    // Clear messages when switching tracks
    if (state.currentTrackId !== trackId) {
      return {
        currentTrackId: trackId,
        messages: [],
        lastMessageTimestamp: null,
        error: null,
        hasMoreMessages: true,
        unreadCount: 0
      };
    }
    return { currentTrackId: trackId };
  }),

  // Loading and error states
  setLoading: (loading) => set({ isLoading: loading }),
  setError: (error) => set({ error }),

  // Connection state
  setConnected: (connected) => set({ isConnected: connected }),

  // Pagination
  setHasMoreMessages: (hasMore) => set({ hasMoreMessages: hasMore }),
  setLastMessageTimestamp: (timestamp) => set({ lastMessageTimestamp: timestamp }),

  // Unread count
  setUnreadCount: (count) => set({ unreadCount: count }),
  incrementUnreadCount: () => set((state) => ({ unreadCount: state.unreadCount + 1 })),
  resetUnreadCount: () => set({ unreadCount: 0 }),

  // Online users tracking
  setOnlineUsers: (users) => set({
    onlineUsers: users,
    onlineCount: users.size
  }),

  addOnlineUser: (userId) => set((state) => {
    const newOnlineUsers = new Set(state.onlineUsers);
    newOnlineUsers.add(userId);
    return {
      onlineUsers: newOnlineUsers,
      onlineCount: newOnlineUsers.size
    };
  }),

  removeOnlineUser: (userId) => set((state) => {
    const newOnlineUsers = new Set(state.onlineUsers);
    newOnlineUsers.delete(userId);
    return {
      onlineUsers: newOnlineUsers,
      onlineCount: newOnlineUsers.size
    };
  }),

  setOnlineCount: (count) => set({ onlineCount: count }),

  // Typing indicators
  addTypingUser: (userId) => set((state) => {
    const newTypingUsers = new Set(state.typingUsers);
    newTypingUsers.add(userId);
    return { typingUsers: newTypingUsers };
  }),

  removeTypingUser: (userId) => set((state) => {
    const newTypingUsers = new Set(state.typingUsers);
    newTypingUsers.delete(userId);
    return { typingUsers: newTypingUsers };
  }),

  clearTypingUsers: () => set({ typingUsers: new Set() })
}));
