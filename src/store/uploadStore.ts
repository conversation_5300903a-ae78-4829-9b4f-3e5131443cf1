import { create } from 'zustand';
import { UploadSession, UploadTrack, UploadAlbum } from '../types';

interface UploadState {
  session: UploadSession | null;
  isUploading: boolean;
  error: string | null;
}

interface UploadStore extends UploadState {
  // Session management
  startSession: (type: 'single' | 'album') => void;
  clearSession: () => void;
  setCurrentStep: (step: number) => void;
  
  // Single track actions
  setSingleTrack: (track: UploadTrack) => void;
  updateSingleMetadata: (metadata: Partial<UploadTrack['metadata']>) => void;
  setSingleCover: (file: File) => void;
  
  // Album actions
  setAlbumMetadata: (metadata: UploadAlbum['metadata']) => void;
  setAlbumCover: (file: File) => void;
  addTrackToAlbum: (track: UploadTrack) => void;
  updateAlbumTrack: (trackId: string, updates: Partial<UploadTrack>) => void;
  removeTrackFromAlbum: (trackId: string) => void;
  reorderAlbumTracks: (fromIndex: number, toIndex: number) => void;
  
  // Upload progress
  setTrackProgress: (trackId: string, progress: number) => void;
  setTrackStatus: (trackId: string, status: UploadTrack['status'], error?: string) => void;
  setUploading: (uploading: boolean) => void;
  
  // Status management
  setPublishStatus: (status: 'draft' | 'published') => void;
  setError: (error: string | null) => void;
}

export const useUploadStore = create<UploadStore>((set, get) => ({
  // Initial state
  session: null,
  isUploading: false,
  error: null,

  // Session management
  startSession: (type) => {
    const sessionId = Math.random().toString(36).substring(2, 11);
    const totalSteps = type === 'single' ? 3 : 4; // Single: Type -> Upload -> Review | Album: Type -> Upload -> Metadata -> Review
    
    set({
      session: {
        id: sessionId,
        type,
        status: 'draft',
        currentStep: 1,
        totalSteps,
        ...(type === 'single' ? { single: undefined } : { album: { metadata: { title: '', artist: '' }, tracks: [] } })
      },
      error: null
    });
  },

  clearSession: () => set({ session: null, isUploading: false, error: null }),

  setCurrentStep: (step) => set((state) => ({
    session: state.session ? { ...state.session, currentStep: step } : null
  })),

  // Single track actions
  setSingleTrack: (track) => set((state) => ({
    session: state.session ? { ...state.session, single: track } : null
  })),

  updateSingleMetadata: (metadata) => set((state) => {
    if (!state.session?.single) return state;
    
    return {
      session: {
        ...state.session,
        single: {
          ...state.session.single,
          metadata: { ...state.session.single.metadata, ...metadata }
        }
      }
    };
  }),

  setSingleCover: (file) => set((state) => {
    if (!state.session?.single) return state;
    
    return {
      session: {
        ...state.session,
        single: {
          ...state.session.single,
          coverFile: file
        }
      }
    };
  }),

  // Album actions
  setAlbumMetadata: (metadata) => set((state) => {
    if (!state.session?.album) return state;
    
    return {
      session: {
        ...state.session,
        album: {
          ...state.session.album,
          metadata: { ...state.session.album.metadata, ...metadata }
        }
      }
    };
  }),

  setAlbumCover: (file) => set((state) => {
    if (!state.session?.album) return state;
    
    return {
      session: {
        ...state.session,
        album: {
          ...state.session.album,
          coverFile: file
        }
      }
    };
  }),

  addTrackToAlbum: (track) => set((state) => {
    if (!state.session?.album) return state;
    
    // Set track number based on current position
    const trackWithNumber = {
      ...track,
      metadata: {
        ...track.metadata,
        trackNumber: state.session.album.tracks.length + 1
      }
    };
    
    return {
      session: {
        ...state.session,
        album: {
          ...state.session.album,
          tracks: [...state.session.album.tracks, trackWithNumber]
        }
      }
    };
  }),

  updateAlbumTrack: (trackId, updates) => set((state) => {
    if (!state.session?.album) return state;
    
    return {
      session: {
        ...state.session,
        album: {
          ...state.session.album,
          tracks: state.session.album.tracks.map(track =>
            track.id === trackId ? { ...track, ...updates } : track
          )
        }
      }
    };
  }),

  removeTrackFromAlbum: (trackId) => set((state) => {
    if (!state.session?.album) return state;
    
    const filteredTracks = state.session.album.tracks.filter(track => track.id !== trackId);
    
    // Renumber tracks
    const renumberedTracks = filteredTracks.map((track, index) => ({
      ...track,
      metadata: {
        ...track.metadata,
        trackNumber: index + 1
      }
    }));
    
    return {
      session: {
        ...state.session,
        album: {
          ...state.session.album,
          tracks: renumberedTracks
        }
      }
    };
  }),

  reorderAlbumTracks: (fromIndex, toIndex) => set((state) => {
    if (!state.session?.album) return state;
    
    const tracks = [...state.session.album.tracks];
    const [movedTrack] = tracks.splice(fromIndex, 1);
    tracks.splice(toIndex, 0, movedTrack);
    
    // Renumber tracks
    const renumberedTracks = tracks.map((track, index) => ({
      ...track,
      metadata: {
        ...track.metadata,
        trackNumber: index + 1
      }
    }));
    
    return {
      session: {
        ...state.session,
        album: {
          ...state.session.album,
          tracks: renumberedTracks
        }
      }
    };
  }),

  // Upload progress
  setTrackProgress: (trackId, progress) => set((state) => {
    if (state.session?.type === 'single' && state.session.single?.id === trackId) {
      return {
        session: {
          ...state.session,
          single: {
            ...state.session.single,
            progress
          }
        }
      };
    }
    
    if (state.session?.type === 'album' && state.session.album) {
      return {
        session: {
          ...state.session,
          album: {
            ...state.session.album,
            tracks: state.session.album.tracks.map(track =>
              track.id === trackId ? { ...track, progress } : track
            )
          }
        }
      };
    }
    
    return state;
  }),

  setTrackStatus: (trackId, status, error) => set((state) => {
    if (state.session?.type === 'single' && state.session.single?.id === trackId) {
      return {
        session: {
          ...state.session,
          single: {
            ...state.session.single,
            status,
            error
          }
        }
      };
    }
    
    if (state.session?.type === 'album' && state.session.album) {
      return {
        session: {
          ...state.session,
          album: {
            ...state.session.album,
            tracks: state.session.album.tracks.map(track =>
              track.id === trackId ? { ...track, status, error } : track
            )
          }
        }
      };
    }
    
    return state;
  }),

  setUploading: (uploading) => set({ isUploading: uploading }),

  // Status management
  setPublishStatus: (status) => {
    console.log('Setting publish status to:', status);
    set((state) => {
      const newSession = state.session ? { ...state.session, status } : null;
      console.log('Updated session:', newSession);
      return { session: newSession };
    });
  },

  setError: (error) => set({ error })
}));
