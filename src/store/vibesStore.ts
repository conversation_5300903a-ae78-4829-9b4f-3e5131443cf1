import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// Route-aware preferences and UI state
interface VibesState {
  // UI preferences that persist across routes
  sidebarCollapsed: boolean;
  rightSidebarMinimized: boolean;

  // User preferences
  autoNavigateToPlay: boolean;

  // Recently visited pages for quick navigation
  recentPages: string[];
}

interface VibesStore extends VibesState {
  // UI actions
  setSidebarCollapsed: (collapsed: boolean) => void;
  setRightSidebarMinimized: (minimized: boolean) => void;
  toggleSidebar: () => void;
  toggleRightSidebar: () => void;

  // Preferences
  setAutoNavigateToPlay: (enabled: boolean) => void;

  // Navigation helpers
  addRecentPage: (path: string) => void;
  clearRecentPages: () => void;
}

export const useVibesStore = create<VibesStore>()(
  persist(
    (set, get) => ({
      // Initial state
      sidebarCollapsed: false,
      rightSidebarMinimized: true, // Right sidebar minimized by default
      autoNavigateToPlay: true,
      recentPages: [],

      // UI actions
      setSidebarCollapsed: (collapsed) => set({ sidebarCollapsed: collapsed }),
      setRightSidebarMinimized: (minimized) => set({ rightSidebarMinimized: minimized }),

      toggleSidebar: () => set((state) => ({
        sidebarCollapsed: !state.sidebarCollapsed
      })),

      toggleRightSidebar: () => set((state) => ({
        rightSidebarMinimized: !state.rightSidebarMinimized
      })),

      // Preferences
      setAutoNavigateToPlay: (enabled) => set({ autoNavigateToPlay: enabled }),

      // Navigation helpers
      addRecentPage: (path) => set((state) => {
        const filtered = state.recentPages.filter(p => p !== path);
        return {
          recentPages: [path, ...filtered].slice(0, 5) // Keep last 5 pages
        };
      }),

      clearRecentPages: () => set({ recentPages: [] })
    }),
    {
      name: 'vibes-ui-state', // localStorage key
      partialize: (state) => ({
        sidebarCollapsed: state.sidebarCollapsed,
        rightSidebarMinimized: state.rightSidebarMinimized,
        autoNavigateToPlay: state.autoNavigateToPlay,
        recentPages: state.recentPages
      })
    }
  )
);