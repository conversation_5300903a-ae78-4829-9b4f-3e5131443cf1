import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User } from '../types';
import { AuthService } from '../services/authService';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  error: string | null;
  signUp: (email: string, password: string, displayName: string) => Promise<void>;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  signInWithApple: () => Promise<void>;
  signInWithTwitter: () => Promise<void>;
  isAuthenticated: boolean;
  clearError: () => void;
  refreshUserData: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

// Global flag to prevent multiple auth initializations (StrictMode protection)
let authInitialized = false;
let globalUnsubscribe: (() => void) | null = null;
let initializationPromise: Promise<void> | null = null;

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(false); // Start as false - don't block the app
  const [error, setError] = useState<string | null>(null);
  const [hasInitialized, setHasInitialized] = useState(false);

  // Single auth state listener for the entire app (StrictMode safe)
  useEffect(() => {
    // Prevent double initialization in StrictMode
    if (authInitialized && globalUnsubscribe) {
      console.log('🔄 AuthContext: Already initialized and active, skipping setup');
      return;
    }

    // If there's already an initialization in progress, wait for it
    if (initializationPromise) {
      console.log('🔄 AuthContext: Initialization in progress, waiting...');
      return;
    }

    console.log('🚀 AuthContext: Setting up auth state listener (non-blocking)');
    authInitialized = true;

    // Create initialization promise to prevent race conditions
    initializationPromise = (async () => {
      // Clean up any existing listeners first
      if (globalUnsubscribe) {
        console.log('🧹 AuthContext: Cleaning up existing listener before setup');
        globalUnsubscribe();
        globalUnsubscribe = null;
      }

      // Initialize AuthService (non-blocking)
      await AuthService.initialize().catch((error) => {
        console.warn('⚠️ AuthService initialization failed (non-blocking):', error);
      });

      // Handle redirect result on app initialization (non-blocking)
      try {
        const user = await AuthService.handleRedirectResult();
        if (user) {
          setUser(user);
          console.log('✅ Successfully signed in via redirect');
        }
      } catch (error: any) {
        console.warn('⚠️ Error handling redirect result (non-blocking):', error);
        setError(error.message);
      }

      // Set up the auth state listener (only once for the entire app)
      const unsubscribe = AuthService.onAuthStateChange((user) => {
        console.log('🔍 AuthContext: User state changed:', user?.email || 'null');

        // Additional validation before setting user state
        if (user && (!user.id || !user.email)) {
          console.error('🔴 AuthContext: Received invalid user data, not setting state:', user);
          return;
        }

        setUser(user);

        // Mark as initialized but don't change loading state (app is already running)
        if (!hasInitialized) {
          setHasInitialized(true);
          console.log('✅ AuthContext: Auth state determined (non-blocking)');
        }
      });

      // Store global unsubscribe for cleanup
      globalUnsubscribe = unsubscribe;
      console.log('✅ AuthContext: Initialization completed (non-blocking)');
    })().catch((error) => {
      console.error('❌ AuthContext: Initialization failed (non-blocking):', error);
    }).finally(() => {
      initializationPromise = null;
    });

    return () => {
      // Only cleanup if this is the actual unmount (not StrictMode)
      if (globalUnsubscribe) {
        console.log('🧹 AuthContext: Cleaning up auth state listener');
        globalUnsubscribe();
        globalUnsubscribe = null;
        authInitialized = false;
        initializationPromise = null;
      }
    };
  }, []); // Empty dependency array - only run once

  const clearError = () => setError(null);

  const signUp = async (email: string, password: string, displayName: string) => {
    try {
      setLoading(true);
      clearError();
      const user = await AuthService.signUp(email, password, displayName);
      setUser(user);
    } catch (error: any) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      clearError();
      const user = await AuthService.signIn(email, password);
      // Don't manually set user here - let the auth state listener handle it
    } catch (error: any) {
      setError(error.message);
      throw error;
    }
  };

  const signOut = async () => {
    try {
      setLoading(true);
      clearError();
      await AuthService.signOut();
      setUser(null);
    } catch (error: any) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signInWithGoogle = async () => {
    try {
      clearError();
      const user = await AuthService.signInWithGoogle();
      // Don't manually set user here - let the auth state listener handle it
    } catch (error: any) {
      if (error.message !== 'redirect_initiated') {
        setError(error.message);
      }
      throw error;
    }
  };

  const signInWithApple = async () => {
    try {
      setLoading(true);
      clearError();
      const user = await AuthService.signInWithApple();
      setUser(user);
    } catch (error: any) {
      if (error.message !== 'redirect_initiated') {
        setError(error.message);
      }
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signInWithTwitter = async () => {
    try {
      setLoading(true);
      clearError();
      const user = await AuthService.signInWithTwitter();
      setUser(user);
    } catch (error: any) {
      if (error.message !== 'redirect_initiated') {
        setError(error.message);
      }
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const refreshUserData = async () => {
    try {
      setLoading(true);
      clearError();
      const refreshedUser = await AuthService.refreshUserData();
      setUser(refreshedUser);
    } catch (error: any) {
      setError(error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const value: AuthContextType = {
    user,
    loading,
    error,
    signUp,
    signIn,
    signOut,
    signInWithGoogle,
    signInWithApple,
    signInWithTwitter,
    isAuthenticated: !!user,
    clearError,
    refreshUserData
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
