import React from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { AppLayout } from './components/layout/AppLayout';
import { AdminLayout } from './components/layout/AdminLayout';
import { ConnectionStatus } from './components/atoms/ConnectionStatus';
import { DevDiagnostics } from './components/atoms/DevDiagnostics';
import { AuthProvider } from './contexts/AuthContext';
import { HomePage } from './components/pages/HomePage';
import { ExplorePage } from './components/pages/ExplorePage';
import { LibraryPage } from './components/pages/LibraryPage';
import { ChatPage } from './components/pages/ChatPage';

import { PlayPage } from './components/pages/PlayPage';
import { PlaylistPage } from './components/pages/PlaylistPage';
import { ProfilePage } from './components/pages/ProfilePage';
import { InvitePage } from './components/pages/InvitePage';
import { AccountSettingsPage } from './components/pages/AccountSettingsPage';
import { AdminDashboardPage } from './pages/AdminDashboardPage';
import { AdminCarouselPage } from './pages/AdminCarouselPage';
import { AdminUsersPage } from './pages/AdminUsersPage';
import { AdminDebugPage } from './pages/AdminDebugPage';
import { DebugIndexPage } from './pages/DebugIndexPage';
import { NotFoundPage } from './components/pages/NotFoundPage';

import { useTheme } from './hooks/useTheme';
import { useAudioPlayer } from './hooks/useAudioPlayer';
import { useMusicStateRestoration } from './hooks/useMusicStateRestoration';

// Import test functionality for development
if (process.env.NODE_ENV === 'development') {
  import('./tests/shareService.test');
  import('./utils/testLikeSystem');
}



// Component that initializes audio player inside router context
const AppWithRouter: React.FC = () => {
  // Initialize music state restoration first (handles persisted state after page refresh)
  const { hasPersistedState, isRestored } = useMusicStateRestoration();

  // Initialize audio player after restoration (this will handle audio playback and auto-navigation)
  useAudioPlayer();

  return (
    <Routes>
      {/* Main App Routes (with music player layout) */}
      <Route path="/" element={<AppLayout />}>
        <Route index element={<HomePage />} />
        <Route path="explore" element={<ExplorePage />} />
        <Route path="library" element={<LibraryPage />} />
        <Route path="chat" element={<ChatPage />} />
        <Route path="play" element={<PlayPage />} />

        <Route path="playlist/:playlistId" element={<PlaylistPage />} />
        <Route path="profile" element={<ProfilePage />} />
        <Route path="invite" element={<InvitePage />} />
        <Route path="settings" element={<AccountSettingsPage />} />
        <Route path="debug-indexes" element={<DebugIndexPage />} />
        {/* 404 Catch-all route */}
        <Route path="*" element={<NotFoundPage />} />
      </Route>

      {/* Admin Routes (clean admin layout, no music player) */}
      <Route path="/admin" element={<AdminLayout />}>
        <Route index element={<AdminDashboardPage />} />
        <Route path="carousel" element={<AdminCarouselPage />} />
        <Route path="users" element={<AdminUsersPage />} />
        <Route path="debug" element={<AdminDebugPage />} />
      </Route>
    </Routes>
  );
};

function App() {
  // Initialize theme system
  useTheme();

  return (
    <>
      <ConnectionStatus />
      <BrowserRouter
        future={{
          v7_startTransition: true,
          v7_relativeSplatPath: true
        }}
      >
        <AuthProvider>
          <AppWithRouter />
        </AuthProvider>
      </BrowserRouter>
    </>
  );
}

export default App;