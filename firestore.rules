rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    function getUserRole() {
      return get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role;
    }

    function isAdmin() {
      return isAuthenticated() &&
             exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
             getUserRole() == 'admin';
    }

    function isInitialAdminSetup() {
      // Allow role update to 'admin' ONLY if:
      // 1. User is updating their own document
      // 2. They're changing from 'listener' to 'admin'
      // 3. No other admins exist in the system (checked via REST API)
      // Note: This function is now primarily used by the REST API method
      // which includes additional server-side validation
      return isAuthenticated() &&
             isOwner(resource.id) &&
             resource.data.role == 'listener' &&
             request.resource.data.role == 'admin' &&
             request.resource.data.diff(resource.data).affectedKeys().hasOnly(['role', 'updatedAt']);
    }

    function isArtist() {
      return isAuthenticated() && getUserRole() in ['artist', 'admin'];
    }

    function canUploadMusic() {
      return isAuthenticated() && getUserRole() == 'admin';
    }
    
    function isValidUser(userData) {
      return userData.keys().hasAll(['id', 'email', 'displayName', 'username', 'role', 'createdAt', 'updatedAt']) &&
             userData.id is string &&
             userData.email is string &&
             userData.displayName is string &&
             userData.username is string &&
             userData.role in ['admin', 'artist', 'listener'] &&
             userData.createdAt is timestamp &&
             userData.updatedAt is timestamp &&
             userData.size() >= 7; // Allow additional optional fields
    }
    
    function isValidTrack(trackData) {
      return trackData.keys().hasAll(['title', 'artist', 'duration', 'url', 'uploadedBy', 'status', 'createdAt', 'updatedAt']) &&
             trackData.title is string &&
             trackData.artist is string &&
             trackData.duration is number &&
             trackData.url is string &&
             trackData.uploadedBy is string &&
             trackData.status in ['draft', 'published'] &&
             trackData.createdAt is timestamp &&
             trackData.updatedAt is timestamp &&
             trackData.size() >= 8 && trackData.size() <= 25; // Allow additional optional metadata fields
    }
    
    function isValidAlbum(albumData) {
      return albumData.keys().hasAll(['title', 'artist', 'trackIds', 'uploadedBy', 'status', 'createdAt', 'updatedAt']) &&
             albumData.title is string &&
             albumData.artist is string &&
             albumData.trackIds is list &&
             albumData.uploadedBy is string &&
             albumData.status in ['draft', 'published'] &&
             albumData.createdAt is timestamp &&
             albumData.updatedAt is timestamp &&
             albumData.size() >= 7; // Allow additional optional fields
    }

    function isValidCarouselSlide(slideData) {
      return slideData.keys().hasAll(['title', 'subtitle', 'description', 'gradient', 'accentColor', 'icon', 'isActive', 'order', 'createdBy', 'createdAt', 'updatedAt']) &&
             slideData.title is string &&
             slideData.subtitle is string &&
             slideData.description is string &&
             slideData.gradient is string &&
             slideData.accentColor is string &&
             slideData.icon is string &&
             slideData.isActive is bool &&
             slideData.order is number &&
             slideData.createdBy is string &&
             slideData.createdAt is timestamp &&
             slideData.updatedAt is timestamp &&
             slideData.size() >= 11; // Allow additional optional fields like albumId, albumTitle
    }

    function isValidPlaylist(playlistData) {
      return playlistData.keys().hasAll(['name', 'tracks', 'createdBy', 'isPublic', 'collaborators', 'createdAt', 'updatedAt']) &&
             playlistData.name is string &&
             playlistData.name.size() > 0 &&
             playlistData.name.size() <= 100 &&
             playlistData.tracks is list &&
             playlistData.createdBy is string &&
             playlistData.isPublic is bool &&
             playlistData.collaborators is list &&
             playlistData.createdAt is timestamp &&
             playlistData.updatedAt is timestamp &&
             playlistData.size() >= 7 && playlistData.size() <= 15; // Allow additional optional fields like description, coverUrl
    }
    
    // Users collection
    match /users/{userId} {
      allow read: if isAuthenticated();
      allow create: if isOwner(userId) && isValidUser(request.resource.data);
      allow update: if isOwner(userId) ||
                       (isAdmin() &&
                        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['role', 'updatedAt'])) ||
                       isInitialAdminSetup();
      allow delete: if isOwner(userId);
    }
    
    // Tracks collection
    match /tracks/{trackId} {
      // Anyone can read published tracks
      allow read: if resource.data.status == 'published' ||
                     (isAuthenticated() && isOwner(resource.data.uploadedBy));

      // Only users with upload permissions can create tracks
      allow create: if canUploadMusic() &&
                       request.resource.data.uploadedBy == request.auth.uid &&
                       isValidTrack(request.resource.data);

      // Only track owner can update
      allow update: if isAuthenticated() &&
                       resource.data.uploadedBy == request.auth.uid;

      // Only track owner can delete
      allow delete: if isAuthenticated() && resource.data.uploadedBy == request.auth.uid;
    }
    
    // Albums collection
    match /albums/{albumId} {
      // Anyone can read published albums
      allow read: if resource.data.status == 'published' ||
                     (isAuthenticated() && isOwner(resource.data.uploadedBy));

      // Only users with upload permissions can create albums
      allow create: if canUploadMusic() &&
                       request.resource.data.uploadedBy == request.auth.uid &&
                       isValidAlbum(request.resource.data);

      // Only album owner can update
      allow update: if isAuthenticated() &&
                       resource.data.uploadedBy == request.auth.uid;

      // Only album owner can delete
      allow delete: if isAuthenticated() && resource.data.uploadedBy == request.auth.uid;
    }
    
    // Playlists collection
    match /playlists/{playlistId} {
      // Read: public playlists or owner/collaborators
      allow read: if resource.data.isPublic == true ||
                     (isAuthenticated() &&
                      (isOwner(resource.data.createdBy) ||
                       request.auth.uid in resource.data.get('collaborators', [])));

      // Only authenticated users can create playlists they own
      allow create: if isAuthenticated() &&
                       request.resource.data.createdBy == request.auth.uid &&
                       isValidPlaylist(request.resource.data);

      // Only playlist owner can update
      allow update: if isAuthenticated() && isOwner(resource.data.createdBy);

      // Only playlist owner can delete
      allow delete: if isAuthenticated() && isOwner(resource.data.createdBy);
    }
    
    // Carousel slides collection
    match /carouselSlides/{slideId} {
      // Public read access - anyone can view carousel slides (no authentication required)
      allow read: if true;

      // Only admins can create carousel slides
      allow create: if isAdmin() &&
                       request.resource.data.createdBy == request.auth.uid &&
                       isValidCarouselSlide(request.resource.data);

      // Only admins can update carousel slides
      allow update: if isAdmin();

      // Only admins can delete carousel slides
      allow delete: if isAdmin();
    }

    // Global chat messages collection (DEPRECATED - keeping for migration)
    match /globalChat/{messageId} {
      // Anyone can read messages (for public chat)
      allow read: if true;

      // Only authenticated users can create messages
      allow create: if isAuthenticated() &&
                       request.resource.data.userId == request.auth.uid &&
                       request.resource.data.content is string &&
                       request.resource.data.content.size() > 0 &&
                       request.resource.data.content.size() <= 500;

      // Only message author can update their own messages (for reactions)
      allow update: if isAuthenticated() &&
                       resource.data.userId == request.auth.uid;

      // Only message author or admins can delete messages
      allow delete: if isAuthenticated() &&
                       (resource.data.userId == request.auth.uid || isAdmin());
    }

    // Track-based chat messages collection (NEW)
    match /trackChats/{trackId}/messages/{messageId} {
      // Anyone can read track chat messages (public track discussions)
      allow read: if true;

      // Only authenticated users can create messages for tracks
      allow create: if isAuthenticated() &&
                       request.resource.data.userId == request.auth.uid &&
                       request.resource.data.content is string &&
                       request.resource.data.content.size() > 0 &&
                       request.resource.data.content.size() <= 500 &&
                       request.resource.data.trackId == trackId; // Ensure trackId matches

      // Only message author can update their own messages (for reactions)
      allow update: if isAuthenticated() &&
                       resource.data.userId == request.auth.uid;

      // Only message author or admins can delete messages
      allow delete: if isAuthenticated() &&
                       (resource.data.userId == request.auth.uid || isAdmin());
    }

    // Online users presence collection
    match /onlineUsers/{userId} {
      // Anyone can read online status (for public online count)
      allow read: if true;

      // Only authenticated users can update their own presence
      allow create, update: if isAuthenticated() &&
                               userId == request.auth.uid;

      // Users can delete their own presence, admins can delete any
      allow delete: if isAuthenticated() &&
                       (userId == request.auth.uid || isAdmin());
    }

    // Tracks collection - allow reading and engagement count updates
    match /tracks/{trackId} {
      // Anyone can read tracks
      allow read: if true;

      // Anyone can update engagement counts (play, like, save counts for tracking)
      allow update: if request.resource.data.diff(resource.data).affectedKeys()
                       .hasOnly(['playCount', 'likeCount', 'saveCount', 'updatedAt']);
    }

    // Albums collection - allow reading and engagement count updates
    match /albums/{albumId} {
      // Anyone can read albums
      allow read: if true;

      // Anyone can update engagement counts (play, like, save counts for tracking)
      allow update: if request.resource.data.diff(resource.data).affectedKeys()
                       .hasOnly(['playCount', 'likeCount', 'saveCount', 'updatedAt']);
    }

    // Engagement tracking collections
    match /playEvents/{eventId} {
      // Anyone can create play events (for anonymous tracking)
      allow create: if true;

      // Admins can read all play events for analytics
      allow read: if isAdmin();

      // Users can read their own play events
      allow read: if isAuthenticated() &&
                     (resource.data.userId == request.auth.uid ||
                      resource.data.sessionId == request.resource.data.sessionId);
    }

    match /likeEvents/{eventId} {
      // Only authenticated users can create like events
      allow create: if isAuthenticated() &&
                       request.resource.data.userId == request.auth.uid;

      // Only authenticated users can delete their own like events
      allow delete: if isAuthenticated() &&
                       resource.data.userId == request.auth.uid;

      // Admins can read all like events for analytics
      allow read: if isAdmin();

      // Users can read their own like events
      allow read: if isAuthenticated() && resource.data.userId == request.auth.uid;
    }

    match /saveEvents/{eventId} {
      // Only authenticated users can create save events
      allow create: if isAuthenticated() &&
                       request.resource.data.userId == request.auth.uid;

      // Only authenticated users can delete their own save events
      allow delete: if isAuthenticated() &&
                       resource.data.userId == request.auth.uid;

      // Admins can read all save events for analytics
      allow read: if isAdmin();

      // Users can read their own save events
      allow read: if isAuthenticated() && resource.data.userId == request.auth.uid;
    }
  }
}
