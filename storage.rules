rules_version = '2';

service firebase.storage {
  match /b/{bucket}/o {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isValidAudioFile() {
      return request.resource.contentType.matches('audio/.*') &&
             request.resource.size < 100 * 1024 * 1024; // 100MB limit
    }

    function isValidImageFile() {
      return request.resource.contentType.matches('image/.*') &&
             request.resource.size < 10 * 1024 * 1024; // 10MB limit
    }
    
    // User profile images
    match /users/{userId}/profile/{fileName} {
      allow read: if true; // Profile images are public
      allow write: if isOwner(userId) && isValidImageFile();
      allow delete: if isOwner(userId);
    }
    
    // Track audio files
    match /tracks/{userId}/{fileName} {
      allow read: if true; // Published tracks are public (access controlled by Firestore rules)
      allow write: if isOwner(userId);
      allow delete: if isOwner(userId);
    }
    
    // Track cover images
    match /covers/{userId}/{fileName} {
      allow read: if true; // Cover images are public
      allow write: if isOwner(userId);
      allow delete: if isOwner(userId);
    }

    // Album cover images
    match /albums/{userId}/{albumId}/{fileName} {
      allow read: if true; // Album covers are public
      allow write: if isOwner(userId);
      allow delete: if isOwner(userId);
    }

    // Playlist cover images
    match /playlists/{userId}/{fileName} {
      allow read: if true; // Playlist covers are public
      allow write: if isOwner(userId);
      allow delete: if isOwner(userId);
    }
    
    // Fallback rule - deny all other access
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
