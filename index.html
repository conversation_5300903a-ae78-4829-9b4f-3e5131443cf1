<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="theme-color" content="#fafafc" />
    <title>Vibes - AI-Powered Music Platform</title>
    <meta name="description" content="Discover, share, and vibe to music with friends. AI-powered music recommendations and real-time social listening." />
    
    <!-- Google Fonts - Montserrat -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">
    
    <!-- Prevent flash of unstyled content -->
    <script>
      // Prevent FOUC by applying theme class immediately
      (function() {
        const theme = localStorage.getItem('theme-storage');
        if (theme) {
          try {
            const parsed = JSON.parse(theme);
            const themeValue = parsed.state?.theme || 'system';
            
            const getSystemTheme = () => {
              return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
            };
            
            const resolvedTheme = themeValue === 'system' ? getSystemTheme() : themeValue;
            
            document.documentElement.classList.add(resolvedTheme);
            
            // Update meta theme-color
            const metaThemeColor = document.querySelector('meta[name="theme-color"]');
            if (metaThemeColor) {
              metaThemeColor.setAttribute('content', resolvedTheme === 'dark' ? '#000000' : '#fafafc');
            }
          } catch (e) {
            // Fallback to system theme
            const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
            document.documentElement.classList.add(systemTheme);
          }
        } else {
          // Default to system theme
          const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
          document.documentElement.classList.add(systemTheme);
        }
      })();
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>