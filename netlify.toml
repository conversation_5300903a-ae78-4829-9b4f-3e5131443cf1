[build]
  # Build command for Vite
  command = "npm run build"
  # Directory to publish (Vite builds to 'dist' by default)
  publish = "dist"

[build.environment]
  # Node.js version
  NODE_VERSION = "18"

# ⚠️  CRITICAL: DO NOT MODIFY THESE REDIRECTS WITHOUT TESTING ⚠️
# This configuration is WORKING and has been tested in production.
# Any changes to redirects can break JavaScript/CSS loading and cause MIME type errors.
# If you need to modify, test locally first, then deploy incrementally.
#
# SPA routing configuration - exclude assets and static files from redirect
# First, ensure assets are served directly (no redirect)
[[redirects]]
  from = "/assets/*"
  to = "/assets/:splat"
  status = 200

# Then handle SPA routing for all other requests
# ⚠️  WARNING: This redirect MUST come LAST to avoid breaking static file serving
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Headers for better performance and security
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

# Cache static assets
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

# Cache HTML with shorter duration
[[headers]]
  for = "/*.html"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"
