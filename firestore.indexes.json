{"indexes": [{"collectionGroup": "tracks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uploadedBy", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "tracks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uploadedBy", "order": "ASCENDING"}, {"fieldPath": "albumId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "tracks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uploadedBy", "order": "ASCENDING"}, {"fieldPath": "albumId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "tracks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "publishedAt", "order": "DESCENDING"}]}, {"collectionGroup": "tracks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "genre", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "albums", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uploadedBy", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "albums", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "publishedAt", "order": "DESCENDING"}]}, {"collectionGroup": "playlists", "queryScope": "COLLECTION", "fields": [{"fieldPath": "created<PERSON>y", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "playlists", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isPublic", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "carouselSlides", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "order", "order": "ASCENDING"}]}, {"collectionGroup": "messages", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "trackId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "messages", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "likeEvents", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "likedAt", "order": "DESCENDING"}]}, {"collectionGroup": "likeEvents", "queryScope": "COLLECTION", "fields": [{"fieldPath": "trackId", "order": "ASCENDING"}, {"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}]}, {"collectionGroup": "likeEvents", "queryScope": "COLLECTION", "fields": [{"fieldPath": "albumId", "order": "ASCENDING"}, {"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}]}, {"collectionGroup": "saveEvents", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "savedAt", "order": "DESCENDING"}]}, {"collectionGroup": "saveEvents", "queryScope": "COLLECTION", "fields": [{"fieldPath": "trackId", "order": "ASCENDING"}, {"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}]}, {"collectionGroup": "saveEvents", "queryScope": "COLLECTION", "fields": [{"fieldPath": "albumId", "order": "ASCENDING"}, {"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}]}, {"collectionGroup": "playEvents", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "playedAt", "order": "DESCENDING"}]}, {"collectionGroup": "messages", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "trackId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "messages", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}], "fieldOverrides": []}