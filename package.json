{"name": "vibes-music-platform", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "verify-deployment": "node scripts/verify-deployment.js", "build:verify": "npm run build && npm run verify-deployment"}, "dependencies": {"clsx": "^2.1.1", "firebase": "^10.13.0", "framer-motion": "^11.3.28", "howler": "^2.2.4", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.52.2", "react-router-dom": "^6.26.0", "tailwind-merge": "^2.5.2", "zustand": "^4.4.7"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/howler": "^2.2.12", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}