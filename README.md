# Vibes - AI-Powered Music Platform

🎵 Discover, share, and vibe to music with friends. AI-powered music recommendations and real-time social listening.

## Deployment

- **Production**: [vibes-web.netlify.app](https://vibes-web.netlify.app)
- **GitHub**: [enokiadev/vibes-web](https://github.com/enokiadev/vibes-web)

### ⚠️ CRITICAL DEPLOYMENT SAFETY RULES

**FOR AI AGENTS AND DEVELOPERS:**

1. **NEVER modify `netlify.toml` redirects without extreme caution**

   - Current config is WORKING in production
   - Changes can break JS/CSS loading (MIME type errors)
   - Test locally first, deploy incrementally

2. **When cleaning up debug components:**

   - Remove ONE component at a time
   - Test after each change
   - If deployment breaks, revert to working checkpoint immediately
   - Use `git reset --hard [working-commit]` if needed

3. **Safe deployment process:**

   - Make minimal changes
   - Commit single files at a time
   - Test locally before pushing
   - Monitor deployment logs
   - Have rollback plan ready

4. **Working checkpoint:** Commit `6fe8dfe` was last known fully working state
