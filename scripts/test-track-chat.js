#!/usr/bin/env node

/**
 * Test Track Chat System
 * 
 * This script tests the track-based chat functionality by:
 * 1. Checking if we can read from track chat collections
 * 2. Verifying the security rules are working
 * 3. Testing the query structure
 */

const { initializeApp } = require('firebase/app');
const { getFirestore, collection, query, orderBy, limit, getDocs } = require('firebase/firestore');

// Firebase config (using environment variables)
const firebaseConfig = {
  apiKey: process.env.VITE_FIREBASE_API_KEY,
  authDomain: process.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: process.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.VITE_FIREBASE_APP_ID
};

async function testTrackChat() {
  console.log('🧪 Testing Track Chat System...');
  
  try {
    // Initialize Firebase
    const app = initializeApp(firebaseConfig);
    const db = getFirestore(app);
    
    // Test track ID (you can change this to any track ID)
    const testTrackId = 'test-track-123';
    
    console.log(`📝 Testing track chat for: ${testTrackId}`);
    
    // Test reading from track chat collection
    const trackChatRef = collection(db, 'trackChats', testTrackId, 'messages');
    const q = query(trackChatRef, orderBy('timestamp', 'desc'), limit(10));
    
    console.log('🔍 Attempting to read track chat messages...');
    
    const snapshot = await getDocs(q);
    
    console.log(`✅ Success! Found ${snapshot.docs.length} messages`);
    
    if (snapshot.docs.length > 0) {
      console.log('📋 Sample messages:');
      snapshot.docs.forEach((doc, index) => {
        const data = doc.data();
        console.log(`  ${index + 1}. ${data.userName}: ${data.content}`);
      });
    } else {
      console.log('📭 No messages found (this is normal for a new track)');
    }
    
    console.log('');
    console.log('🎉 Track chat system is working correctly!');
    console.log('');
    console.log('✅ Verified:');
    console.log('  • Firestore rules allow reading track chats');
    console.log('  • Query structure is correct');
    console.log('  • Collection path is accessible');
    console.log('  • Indexes are working');
    
  } catch (error) {
    console.error('❌ Track chat test failed:', error.message);
    console.log('');
    console.log('🔧 Troubleshooting:');
    console.log('  1. Check if Firestore rules are deployed');
    console.log('  2. Verify Firebase project configuration');
    console.log('  3. Ensure indexes are created');
    console.log('  4. Check network connectivity');
    process.exit(1);
  }
}

// Load environment variables from .env file
require('dotenv').config();

// Run the test
testTrackChat();
