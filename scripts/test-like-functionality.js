import { initializeApp } from 'firebase/app';
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';
import { getAuth, connectAuthEmulator, signInWithEmailAndPassword } from 'firebase/auth';

// Firebase config (using environment variables)
const firebaseConfig = {
  apiKey: process.env.VITE_FIREBASE_API_KEY,
  authDomain: process.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: process.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.VITE_FIREBASE_APP_ID
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);
const auth = getAuth(app);

// Import EngagementService
import { EngagementService } from '../src/services/engagementService.js';

async function testLikeFunctionality() {
  console.log('🧪 Testing Like Functionality...\n');

  try {
    // Test 1: Test Firestore permissions
    console.log('1️⃣ Testing Firestore permissions...');
    
    // You'll need to sign in with a test user first
    // Replace with actual test credentials
    const testEmail = '<EMAIL>';
    const testPassword = 'testpassword';
    
    try {
      const userCredential = await signInWithEmailAndPassword(auth, testEmail, testPassword);
      const user = userCredential.user;
      console.log('✅ Signed in as:', user.email);
      
      // Test permissions
      await EngagementService.testFirestorePermissions(user.uid);
      console.log('✅ Firestore permissions test passed\n');
      
    } catch (authError) {
      console.log('⚠️ Auth test skipped (no test user):', authError.message);
      console.log('   You can test manually in the browser\n');
    }

    // Test 2: Test getUserLike query (this was the main issue)
    console.log('2️⃣ Testing getUserLike query...');
    try {
      const result = await EngagementService.getUserLike('test-track-id', 'track', 'test-user-id');
      console.log('✅ getUserLike query executed successfully');
      console.log('   Result:', result ? 'Found like' : 'No like found');
    } catch (error) {
      if (error.message.includes('index')) {
        console.log('❌ Index still building or missing:', error.message);
        console.log('   Wait a few minutes for indexes to build');
      } else {
        console.log('✅ Query executed (no index error)');
      }
    }

    // Test 3: Test getUserLikedTracks query
    console.log('\n3️⃣ Testing getUserLikedTracks query...');
    try {
      const likedTracks = await EngagementService.getUserLikedTracks('test-user-id');
      console.log('✅ getUserLikedTracks query executed successfully');
      console.log('   Found', likedTracks.length, 'liked tracks');
    } catch (error) {
      if (error.message.includes('index')) {
        console.log('❌ Index still building or missing:', error.message);
      } else {
        console.log('✅ Query executed (no index error)');
      }
    }

    console.log('\n🎉 Like functionality test completed!');
    console.log('\n📝 Next steps:');
    console.log('   1. Wait 5-10 minutes for indexes to fully build');
    console.log('   2. Test like functionality in the browser');
    console.log('   3. Check browser console for any remaining errors');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testLikeFunctionality().then(() => {
  console.log('\n✅ Test script completed');
  process.exit(0);
}).catch((error) => {
  console.error('❌ Test script failed:', error);
  process.exit(1);
});
