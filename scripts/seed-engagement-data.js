#!/usr/bin/env node

/**
 * Seed Engagement Data Script
 *
 * This script creates some initial engagement data (plays, likes, saves)
 * to test the engagement tracking system and populate the admin dashboard.
 *
 * Run with: node scripts/seed-engagement-data.js
 */

import { initializeApp } from "firebase/app";
import {
  getFirestore,
  collection,
  addDoc,
  getDocs,
  query,
  where,
  serverTimestamp,
  Timestamp,
} from "firebase/firestore";

// Firebase config (same as in your app)
const firebaseConfig = {
  apiKey: "AIzaSyDGpAHia_wEmrhnmYjrPrrhiQIrV5EQZZE",
  authDomain: "probe-vibes.firebaseapp.com",
  projectId: "probe-vibes",
  storageBucket: "probe-vibes.firebasestorage.app",
  messagingSenderId: "1092527848130",
  appId: "1:1092527848130:web:a6a92a7bb94e6e0c6d9b8f",
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Helper function to create random timestamps within the last 30 days
function getRandomTimestamp() {
  const now = new Date();
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  const randomTime =
    thirtyDaysAgo.getTime() +
    Math.random() * (now.getTime() - thirtyDaysAgo.getTime());
  return Timestamp.fromDate(new Date(randomTime));
}

// Helper function to create random session ID
function generateSessionId() {
  return "session_" + Math.random().toString(36).substr(2, 9);
}

async function seedEngagementData() {
  try {
    console.log("🌱 Starting engagement data seeding...");

    // Get all published tracks
    const tracksQuery = query(
      collection(db, "tracks"),
      where("status", "==", "published")
    );
    const tracksSnapshot = await getDocs(tracksQuery);

    if (tracksSnapshot.empty) {
      console.log(
        "❌ No published tracks found. Please upload some music first."
      );
      return;
    }

    const tracks = tracksSnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));

    console.log(`📀 Found ${tracks.length} published tracks`);

    // Get admin user ID (for some engagement events)
    const usersQuery = query(
      collection(db, "users"),
      where("role", "==", "admin")
    );
    const usersSnapshot = await getDocs(usersQuery);
    const adminUser = usersSnapshot.docs[0];
    const adminUserId = adminUser ? adminUser.id : null;

    console.log(`👤 Admin user: ${adminUserId || "Not found"}`);

    // Create play events (mix of authenticated and anonymous)
    console.log("🎵 Creating play events...");
    const playPromises = [];

    tracks.forEach((track) => {
      // Create 5-20 random plays per track
      const playCount = Math.floor(Math.random() * 16) + 5;

      for (let i = 0; i < playCount; i++) {
        const isAuthenticated = Math.random() > 0.3; // 70% authenticated plays

        const playEvent = {
          trackId: track.id,
          albumId: track.albumId || null,
          playedAt: getRandomTimestamp(),
          sessionId: generateSessionId(),
          ...(isAuthenticated && adminUserId ? { userId: adminUserId } : {}),
        };

        playPromises.push(addDoc(collection(db, "playEvents"), playEvent));
      }
    });

    await Promise.all(playPromises);
    console.log(`✅ Created ${playPromises.length} play events`);

    // Create like events (only for authenticated users)
    if (adminUserId) {
      console.log("❤️ Creating like events...");
      const likePromises = [];

      // Like 30-70% of tracks randomly
      const tracksToLike = tracks.filter(() => Math.random() > 0.4);

      tracksToLike.forEach((track) => {
        const likeEvent = {
          trackId: track.id,
          albumId: track.albumId || null,
          type: "track",
          userId: adminUserId,
          likedAt: getRandomTimestamp(),
        };

        likePromises.push(addDoc(collection(db, "likeEvents"), likeEvent));
      });

      await Promise.all(likePromises);
      console.log(`✅ Created ${likePromises.length} like events`);

      // Create save events (only for authenticated users)
      console.log("💾 Creating save events...");
      const savePromises = [];

      // Save 20-50% of tracks randomly
      const tracksToSave = tracks.filter(() => Math.random() > 0.6);

      tracksToSave.forEach((track) => {
        const saveEvent = {
          trackId: track.id,
          albumId: track.albumId || null,
          type: "track",
          userId: adminUserId,
          savedAt: getRandomTimestamp(),
        };

        savePromises.push(addDoc(collection(db, "saveEvents"), saveEvent));
      });

      await Promise.all(savePromises);
      console.log(`✅ Created ${savePromises.length} save events`);
    } else {
      console.log("⚠️ Skipping like/save events (no admin user found)");
    }

    console.log("🎉 Engagement data seeding completed successfully!");
    console.log("📊 You can now check the admin dashboard for analytics.");
  } catch (error) {
    console.error("❌ Error seeding engagement data:", error);
  }
}

// Run the seeding
seedEngagementData()
  .then(() => {
    console.log("✨ Seeding process finished");
    process.exit(0);
  })
  .catch((error) => {
    console.error("💥 Seeding failed:", error);
    process.exit(1);
  });
