// <PERSON>ript to trigger creation of missing Firestore indexes
// Run this to generate the index creation suggestions in Firebase console

import { initializeApp } from 'firebase/app';
import { getFirestore, collection, query, where, orderBy, limit, getDocs } from 'firebase/firestore';

// Firebase config (using environment variables)
const firebaseConfig = {
  apiKey: process.env.VITE_FIREBASE_API_KEY,
  authDomain: process.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: process.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.VITE_FIREBASE_APP_ID
};

const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

async function triggerIndexCreation() {
  console.log('🔥 Triggering Firestore index creation...');
  
  try {
    // 1. likeEvents indexes
    console.log('📝 Triggering likeEvents indexes...');
    
    // Query: userId + type + likedAt
    const likeQuery1 = query(
      collection(db, 'likeEvents'),
      where('userId', '==', 'dummy'),
      where('type', '==', 'track'),
      orderBy('likedAt', 'desc'),
      limit(1)
    );
    
    // Query: trackId + userId + type
    const likeQuery2 = query(
      collection(db, 'likeEvents'),
      where('trackId', '==', 'dummy'),
      where('userId', '==', 'dummy'),
      where('type', '==', 'track'),
      limit(1)
    );
    
    // 2. saveEvents indexes
    console.log('📝 Triggering saveEvents indexes...');
    
    // Query: userId + type + savedAt
    const saveQuery1 = query(
      collection(db, 'saveEvents'),
      where('userId', '==', 'dummy'),
      where('type', '==', 'track'),
      orderBy('savedAt', 'desc'),
      limit(1)
    );
    
    // Query: trackId + userId + type
    const saveQuery2 = query(
      collection(db, 'saveEvents'),
      where('trackId', '==', 'dummy'),
      where('userId', '==', 'dummy'),
      where('type', '==', 'track'),
      limit(1)
    );
    
    // 3. tracks indexes
    console.log('📝 Triggering tracks indexes...');
    
    // Query: albumId + status + trackNumber
    const tracksQuery1 = query(
      collection(db, 'tracks'),
      where('albumId', '==', 'dummy'),
      where('status', '==', 'published'),
      orderBy('trackNumber', 'asc'),
      limit(1)
    );
    
    // Query: genre + status + createdAt
    const tracksQuery2 = query(
      collection(db, 'tracks'),
      where('genre', '==', 'Electronic'),
      where('status', '==', 'published'),
      orderBy('createdAt', 'desc'),
      limit(1)
    );
    
    // Execute all queries to trigger index suggestions
    const queries = [
      likeQuery1, likeQuery2, 
      saveQuery1, saveQuery2, 
      tracksQuery1, tracksQuery2
    ];
    
    for (let i = 0; i < queries.length; i++) {
      try {
        console.log(`⚡ Executing query ${i + 1}/${queries.length}...`);
        await getDocs(queries[i]);
        console.log(`✅ Query ${i + 1} completed`);
      } catch (error) {
        console.log(`🔍 Query ${i + 1} triggered index creation:`, error.message);
      }
    }
    
    console.log('\n🎉 All queries executed!');
    console.log('📋 Check the Firebase Console for index creation suggestions:');
    console.log('🔗 https://console.firebase.google.com/project/probe-vibes/firestore/indexes');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

// Run the script
triggerIndexCreation();
