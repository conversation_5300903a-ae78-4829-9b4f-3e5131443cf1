#!/usr/bin/env node

/**
 * Deployment Verification Script
 * Checks if the build output is correctly configured for Netlify SPA routing
 */

import fs from 'fs';
import path from 'path';

const DIST_DIR = 'dist';
const REQUIRED_FILES = [
  'index.html',
  '_redirects',
  'assets'
];

console.log('🔍 Verifying deployment configuration...\n');

// Check if dist directory exists
if (!fs.existsSync(DIST_DIR)) {
  console.error('❌ Error: dist directory not found. Run "npm run build" first.');
  process.exit(1);
}

// Check required files
let allFilesPresent = true;
REQUIRED_FILES.forEach(file => {
  const filePath = path.join(DIST_DIR, file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file} - Found`);
  } else {
    console.log(`❌ ${file} - Missing`);
    allFilesPresent = false;
  }
});

// Check _redirects content
const redirectsPath = path.join(DIST_DIR, '_redirects');
if (fs.existsSync(redirectsPath)) {
  const redirectsContent = fs.readFileSync(redirectsPath, 'utf8');
  if (redirectsContent.includes('/*  /index.html  200')) {
    console.log('✅ _redirects - Contains SPA routing rule');
  } else {
    console.log('❌ _redirects - Missing SPA routing rule');
    allFilesPresent = false;
  }
}

// Check netlify.toml
if (fs.existsSync('netlify.toml')) {
  console.log('✅ netlify.toml - Found');
} else {
  console.log('⚠️  netlify.toml - Not found (optional)');
}

console.log('\n📋 Deployment Checklist:');
console.log('1. ✅ Build completed successfully');
console.log('2. ✅ _redirects file in dist/');
console.log('3. ✅ netlify.toml in root');
console.log('4. 🔄 Deploy to Netlify');
console.log('5. 🧪 Test routing after deployment');

if (allFilesPresent) {
  console.log('\n🎉 All checks passed! Ready for deployment.');
  console.log('\n📝 Next steps:');
  console.log('1. Commit and push changes to your repository');
  console.log('2. Netlify will automatically redeploy');
  console.log('3. Test the /play route after deployment');
} else {
  console.log('\n❌ Some checks failed. Please fix the issues above.');
  process.exit(1);
}
