#!/usr/bin/env node

/**
 * Deploy Firestore Rules and Indexes
 * 
 * This script deploys the updated Firestore security rules and indexes
 * to support the new track-based chat system.
 */

const { execSync } = require('child_process');
const path = require('path');

console.log('🔥 Deploying Firestore Configuration...');

try {
  // Deploy Firestore rules
  console.log('📋 Deploying Firestore security rules...');
  execSync('firebase deploy --only firestore:rules', { 
    stdio: 'inherit',
    cwd: process.cwd()
  });

  // Deploy Firestore indexes
  console.log('📊 Deploying Firestore indexes...');
  execSync('firebase deploy --only firestore:indexes', { 
    stdio: 'inherit',
    cwd: process.cwd()
  });

  console.log('✅ Firestore configuration deployed successfully!');
  console.log('');
  console.log('🎯 Track-based chat system is now ready!');
  console.log('');
  console.log('📝 What was deployed:');
  console.log('  • Security rules for trackChats/{trackId}/messages collection');
  console.log('  • Indexes for efficient track message queries');
  console.log('  • Public read access for track discussions');
  console.log('  • Authenticated write access with validation');

} catch (error) {
  console.error('❌ Failed to deploy Firestore configuration:', error.message);
  console.log('');
  console.log('🔧 Troubleshooting:');
  console.log('  1. Make sure you are logged in: firebase login');
  console.log('  2. Check your Firebase project: firebase use --list');
  console.log('  3. Verify firebase.json configuration');
  process.exit(1);
}
