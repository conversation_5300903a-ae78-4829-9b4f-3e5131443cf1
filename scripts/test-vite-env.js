// Test script to verify Vite environment variables during build
// This will be included in the build output to check if env vars are available

console.log('🔍 Testing Vite environment variables...');

const envVars = {
  VITE_FIREBASE_API_KEY: import.meta.env.VITE_FIREBASE_API_KEY,
  VITE_FIREBASE_AUTH_DOMAIN: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  VITE_FIREBASE_PROJECT_ID: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  VITE_FIREBASE_STORAGE_BUCKET: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  VITE_FIREBASE_MESSAGING_SENDER_ID: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  VITE_FIREBASE_APP_ID: import.meta.env.VITE_FIREBASE_APP_ID,
  DEV: import.meta.env.DEV,
  PROD: import.meta.env.PROD,
  MODE: import.meta.env.MODE
};

console.log('📋 Vite Environment Variables Status:');
Object.entries(envVars).forEach(([key, value]) => {
  const status = value ? '✅ Set' : '❌ Missing';
  const displayValue = key.includes('API_KEY') && value ? 
    value.substring(0, 10) + '...' : value;
  console.log(`${key}: ${status}${value ? ` (${displayValue})` : ''}`);
});

// Export for potential use
export { envVars };
