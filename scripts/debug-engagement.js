#!/usr/bin/env node

/**
 * Debug Engagement Data Script
 * 
 * This script checks what engagement data exists in Firestore
 * to help debug why the dashboard isn't showing activity.
 */

import { initializeApp } from "firebase/app";
import {
  getFirestore,
  collection,
  getDocs,
  query,
  orderBy,
  limit,
} from "firebase/firestore";

// Firebase config
const firebaseConfig = {
  apiKey: "AIzaSyDGpAHia_wEmrhnmYjrPrrhiQIrV5EQZZE",
  authDomain: "probe-vibes.firebaseapp.com",
  projectId: "probe-vibes",
  storageBucket: "probe-vibes.firebasestorage.app",
  messagingSenderId: "1092527848130",
  appId: "1:1092527848130:web:a6a92a7bb94e6e0c6d9b8f"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

async function debugEngagement() {
  try {
    console.log('🔍 Debugging engagement data...\n');

    // Check play events
    console.log('📊 Checking play events...');
    try {
      const playEventsQuery = query(
        collection(db, 'playEvents'),
        orderBy('playedAt', 'desc'),
        limit(10)
      );
      const playEventsSnapshot = await getDocs(playEventsQuery);
      
      console.log(`Found ${playEventsSnapshot.size} play events`);
      
      if (playEventsSnapshot.size > 0) {
        console.log('Recent play events:');
        playEventsSnapshot.docs.forEach((doc, index) => {
          const data = doc.data();
          console.log(`  ${index + 1}. Track: ${data.trackId}, User: ${data.userId || 'anonymous'}, Time: ${data.playedAt?.toDate?.() || data.playedAt}`);
        });
      }
    } catch (error) {
      console.log('❌ Error reading play events:', error.message);
    }

    console.log('\n');

    // Check like events
    console.log('❤️ Checking like events...');
    try {
      const likeEventsQuery = query(
        collection(db, 'likeEvents'),
        orderBy('likedAt', 'desc'),
        limit(10)
      );
      const likeEventsSnapshot = await getDocs(likeEventsQuery);
      
      console.log(`Found ${likeEventsSnapshot.size} like events`);
      
      if (likeEventsSnapshot.size > 0) {
        console.log('Recent like events:');
        likeEventsSnapshot.docs.forEach((doc, index) => {
          const data = doc.data();
          console.log(`  ${index + 1}. Track: ${data.trackId}, User: ${data.userId}, Time: ${data.likedAt?.toDate?.() || data.likedAt}`);
        });
      }
    } catch (error) {
      console.log('❌ Error reading like events:', error.message);
    }

    console.log('\n');

    // Check save events
    console.log('💾 Checking save events...');
    try {
      const saveEventsQuery = query(
        collection(db, 'saveEvents'),
        orderBy('savedAt', 'desc'),
        limit(10)
      );
      const saveEventsSnapshot = await getDocs(saveEventsQuery);
      
      console.log(`Found ${saveEventsSnapshot.size} save events`);
      
      if (saveEventsSnapshot.size > 0) {
        console.log('Recent save events:');
        saveEventsSnapshot.docs.forEach((doc, index) => {
          const data = doc.data();
          console.log(`  ${index + 1}. Track: ${data.trackId}, User: ${data.userId}, Time: ${data.savedAt?.toDate?.() || data.savedAt}`);
        });
      }
    } catch (error) {
      console.log('❌ Error reading save events:', error.message);
    }

    console.log('\n');

    // Check tracks for play counts
    console.log('🎵 Checking track play counts...');
    try {
      const tracksSnapshot = await getDocs(collection(db, 'tracks'));
      
      console.log(`Found ${tracksSnapshot.size} tracks`);
      
      if (tracksSnapshot.size > 0) {
        console.log('Track play counts:');
        tracksSnapshot.docs.forEach((doc, index) => {
          const data = doc.data();
          if (data.playCount > 0 || data.likeCount > 0 || data.saveCount > 0) {
            console.log(`  ${index + 1}. ${data.title}: ${data.playCount || 0} plays, ${data.likeCount || 0} likes, ${data.saveCount || 0} saves`);
          }
        });
      }
    } catch (error) {
      console.log('❌ Error reading tracks:', error.message);
    }

    console.log('\n✅ Debug complete!');
    
  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
}

// Run the debug
debugEngagement().then(() => {
  console.log('🔍 Debug process finished');
  process.exit(0);
}).catch(error => {
  console.error('💥 Debug failed:', error);
  process.exit(1);
});
