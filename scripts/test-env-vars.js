#!/usr/bin/env node

// Test script to verify environment variables are loaded correctly
console.log('🔍 Testing environment variable loading...');

// Check if we're in a Node.js environment
if (typeof process !== 'undefined' && process.env) {
  console.log('\n📋 Node.js Environment Variables:');
  console.log('VITE_FIREBASE_API_KEY:', process.env.VITE_FIREBASE_API_KEY ? '✅ Set' : '❌ Missing');
  console.log('VITE_FIREBASE_AUTH_DOMAIN:', process.env.VITE_FIREBASE_AUTH_DOMAIN ? '✅ Set' : '❌ Missing');
  console.log('VITE_FIREBASE_PROJECT_ID:', process.env.VITE_FIREBASE_PROJECT_ID ? '✅ Set' : '❌ Missing');
  console.log('VITE_FIREBASE_STORAGE_BUCKET:', process.env.VITE_FIREBASE_STORAGE_BUCKET ? '✅ Set' : '❌ Missing');
  console.log('VITE_FIREBASE_MESSAGING_SENDER_ID:', process.env.VITE_FIREBASE_MESSAGING_SENDER_ID ? '✅ Set' : '❌ Missing');
  console.log('VITE_FIREBASE_APP_ID:', process.env.VITE_FIREBASE_APP_ID ? '✅ Set' : '❌ Missing');
  
  // Show actual values (first few characters only for security)
  console.log('\n🔍 Partial Values (for verification):');
  if (process.env.VITE_FIREBASE_API_KEY) {
    console.log('API_KEY starts with:', process.env.VITE_FIREBASE_API_KEY.substring(0, 10) + '...');
  }
  if (process.env.VITE_FIREBASE_PROJECT_ID) {
    console.log('PROJECT_ID:', process.env.VITE_FIREBASE_PROJECT_ID);
  }
  if (process.env.VITE_FIREBASE_AUTH_DOMAIN) {
    console.log('AUTH_DOMAIN:', process.env.VITE_FIREBASE_AUTH_DOMAIN);
  }
} else {
  console.log('❌ Not in Node.js environment or process.env not available');
}

console.log('\n✅ Environment variable test completed');
